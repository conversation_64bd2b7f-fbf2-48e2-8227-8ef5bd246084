from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.hr import Employee, Attendance, Payroll, LeaveRequest
from database_setup import db

bp = Blueprint('hr', __name__, url_prefix='/hr')

@bp.route('/')
@login_required
def index():
    """صفحة الموارد البشرية الرئيسية"""
    if not current_user.has_permission('view_hr'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    stats = {
        'total_employees': Employee.query.filter_by(employment_status='active').count(),
        'pending_leave_requests': LeaveRequest.query.filter_by(status='pending').count(),
        'present_today': Attendance.query.filter_by(
            date=db.func.current_date(),
            status='present'
        ).count()
    }

    return render_template('hr/index.html', stats=stats)

@bp.route('/employees')
@login_required
def employees():
    """قائمة الموظفين"""
    employees = Employee.query.filter_by(employment_status='active').all()
    return render_template('hr/employees.html', employees=employees)

@bp.route('/attendance')
@login_required
def attendance():
    """الحضور والانصراف"""
    from datetime import date
    today = date.today()
    today_attendance = Attendance.query.filter_by(date=today).all()

    # إذا لم يكن هناك سجلات حضور لليوم، إنشاء سجلات فارغة للموظفين النشطين
    if not today_attendance:
        active_employees = Employee.query.filter_by(employment_status='active').all()
        for employee in active_employees:
            attendance_record = Attendance(
                employee_id=employee.id,
                date=today,
                status='absent'  # افتراضياً غائب حتى يتم تسجيل الحضور
            )
            db.session.add(attendance_record)
        db.session.commit()
        today_attendance = Attendance.query.filter_by(date=today).all()

    return render_template('hr/attendance.html', attendance=today_attendance, today=today.strftime('%Y-%m-%d'))

@bp.route('/payroll')
@login_required
def payroll():
    """الرواتب"""
    try:
        payrolls = Payroll.query.order_by(Payroll.pay_period_start.desc()).limit(50).all()
        # التأكد من أن جميع القيم صحيحة
        for payroll in payrolls:
            if payroll.basic_salary is None:
                payroll.basic_salary = 0
            if payroll.allowances is None:
                payroll.allowances = 0
            if payroll.overtime_amount is None:
                payroll.overtime_amount = 0
            if payroll.total_deductions is None:
                payroll.total_deductions = 0
            if payroll.net_salary is None:
                payroll.net_salary = 0
        return render_template('hr/payroll.html', payrolls=payrolls)
    except Exception as e:
        # في حالة وجود خطأ، إرسال قائمة فارغة
        return render_template('hr/payroll.html', payrolls=[])

@bp.route('/leave_requests')
@login_required
def leave_requests():
    """طلبات الإجازات"""
    requests = LeaveRequest.query.order_by(LeaveRequest.created_at.desc()).all()
    return render_template('hr/leave_requests.html', requests=requests)

# مسارات API للتفاعل مع البيانات
@bp.route('/api/check_in/<int:employee_id>', methods=['POST'])
@login_required
def api_check_in(employee_id):
    """تسجيل حضور موظف"""
    from datetime import datetime, date

    today = date.today()
    now = datetime.now().time()

    # البحث عن سجل الحضور لليوم
    attendance = Attendance.query.filter_by(
        employee_id=employee_id,
        date=today
    ).first()

    if not attendance:
        # إنشاء سجل جديد
        attendance = Attendance(
            employee_id=employee_id,
            date=today,
            status='present',
            check_in=now
        )
        db.session.add(attendance)
    else:
        # تحديث السجل الموجود
        attendance.check_in = now
        attendance.status = 'present'

    db.session.commit()

    return {'success': True, 'message': 'تم تسجيل الحضور بنجاح', 'time': now.strftime('%H:%M')}

@bp.route('/api/check_out/<int:employee_id>', methods=['POST'])
@login_required
def api_check_out(employee_id):
    """تسجيل انصراف موظف"""
    from datetime import datetime, date

    today = date.today()
    now = datetime.now().time()

    # البحث عن سجل الحضور لليوم
    attendance = Attendance.query.filter_by(
        employee_id=employee_id,
        date=today
    ).first()

    if attendance:
        attendance.check_out = now
        attendance.calculate_hours()  # حساب ساعات العمل
        db.session.commit()

        return {'success': True, 'message': 'تم تسجيل الانصراف بنجاح', 'time': now.strftime('%H:%M')}
    else:
        return {'success': False, 'message': 'لم يتم العثور على سجل حضور لهذا اليوم'}

@bp.route('/api/approve_leave/<int:request_id>', methods=['POST'])
@login_required
def api_approve_leave(request_id):
    """الموافقة على طلب إجازة"""
    from datetime import datetime

    leave_request = LeaveRequest.query.get_or_404(request_id)

    if leave_request.status == 'pending':
        leave_request.status = 'approved'
        leave_request.reviewed_by = current_user.id
        leave_request.review_date = datetime.utcnow()
        leave_request.review_notes = 'تم الموافقة على الطلب'

        db.session.commit()

        return {'success': True, 'message': 'تم الموافقة على طلب الإجازة'}
    else:
        return {'success': False, 'message': 'لا يمكن الموافقة على هذا الطلب'}

@bp.route('/api/reject_leave/<int:request_id>', methods=['POST'])
@login_required
def api_reject_leave(request_id):
    """رفض طلب إجازة"""
    from datetime import datetime
    from flask import request

    leave_request = LeaveRequest.query.get_or_404(request_id)

    if leave_request.status == 'pending':
        leave_request.status = 'rejected'
        leave_request.reviewed_by = current_user.id
        leave_request.review_date = datetime.utcnow()

        # الحصول على سبب الرفض من البيانات المرسلة
        data = request.get_json() or {}
        leave_request.review_notes = data.get('reason', 'تم رفض الطلب')

        db.session.commit()

        return {'success': True, 'message': 'تم رفض طلب الإجازة'}
    else:
        return {'success': False, 'message': 'لا يمكن رفض هذا الطلب'}

# الإجراءات السريعة لطلبات الإجازات
@bp.route('/api/approve_all_pending', methods=['POST'])
@login_required
def api_approve_all_pending():
    """الموافقة على جميع الطلبات المعلقة"""
    from datetime import datetime

    pending_requests = LeaveRequest.query.filter_by(status='pending').all()

    if not pending_requests:
        return {'success': False, 'message': 'لا يوجد طلبات معلقة للموافقة عليها'}

    approved_count = 0
    for leave_request in pending_requests:
        leave_request.status = 'approved'
        leave_request.reviewed_by = current_user.id
        leave_request.review_date = datetime.now()
        leave_request.review_notes = 'تم الموافقة عليه ضمن الموافقة الجماعية'
        approved_count += 1

    db.session.commit()

    return {
        'success': True,
        'message': f'تم الموافقة على {approved_count} طلب إجازة',
        'count': approved_count
    }

@bp.route('/api/leave_report')
@login_required
def api_leave_report():
    """إنتاج تقرير الإجازات"""
    from datetime import datetime, timedelta
    from sqlalchemy import func

    # إحصائيات عامة
    total_requests = LeaveRequest.query.count()
    pending_requests = LeaveRequest.query.filter_by(status='pending').count()
    approved_requests = LeaveRequest.query.filter_by(status='approved').count()
    rejected_requests = LeaveRequest.query.filter_by(status='rejected').count()

    # إحصائيات حسب نوع الإجازة
    leave_types_stats = db.session.query(
        LeaveRequest.leave_type,
        func.count(LeaveRequest.id).label('count'),
        func.sum(LeaveRequest.days_requested).label('total_days')
    ).group_by(LeaveRequest.leave_type).all()

    # إحصائيات حسب الشهر (آخر 6 شهور)
    six_months_ago = datetime.now() - timedelta(days=180)
    monthly_stats = db.session.query(
        func.strftime('%Y-%m', LeaveRequest.created_at).label('month'),
        func.count(LeaveRequest.id).label('count')
    ).filter(
        LeaveRequest.created_at >= six_months_ago
    ).group_by(
        func.strftime('%Y-%m', LeaveRequest.created_at)
    ).all()

    # أكثر الموظفين طلباً للإجازات
    top_employees = db.session.query(
        Employee.full_name_ar,
        func.count(LeaveRequest.id).label('request_count'),
        func.sum(LeaveRequest.days_requested).label('total_days')
    ).join(
        LeaveRequest, Employee.id == LeaveRequest.employee_id
    ).group_by(
        Employee.id, Employee.full_name_ar
    ).order_by(
        func.count(LeaveRequest.id).desc()
    ).limit(5).all()

    report_data = {
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'summary': {
            'total_requests': total_requests,
            'pending_requests': pending_requests,
            'approved_requests': approved_requests,
            'rejected_requests': rejected_requests,
            'approval_rate': round((approved_requests / total_requests * 100) if total_requests > 0 else 0, 1)
        },
        'leave_types': [
            {
                'type': lt[0],
                'count': lt[1],
                'total_days': lt[2] or 0,
                'type_name': {
                    'annual': 'إجازة سنوية',
                    'sick': 'إجازة مرضية',
                    'emergency': 'إجازة طارئة',
                    'maternity': 'إجازة أمومة',
                    'unpaid': 'إجازة بدون راتب'
                }.get(lt[0], lt[0])
            } for lt in leave_types_stats
        ],
        'monthly_trends': [
            {
                'month': ms[0],
                'count': ms[1]
            } for ms in monthly_stats
        ],
        'top_employees': [
            {
                'name': te[0],
                'request_count': te[1],
                'total_days': te[2] or 0
            } for te in top_employees
        ]
    }

    return {'success': True, 'data': report_data}

@bp.route('/api/export_leaves')
@login_required
def api_export_leaves():
    """تصدير بيانات الإجازات"""
    from datetime import datetime

    # جلب جميع طلبات الإجازات مع بيانات الموظفين
    requests = db.session.query(
        LeaveRequest,
        Employee.full_name_ar,
        Employee.employee_number,
        Employee.department
    ).join(
        Employee, LeaveRequest.employee_id == Employee.id
    ).order_by(LeaveRequest.created_at.desc()).all()

    export_data = []
    for req, emp_name, emp_number, emp_dept in requests:
        export_data.append({
            'رقم_الطلب': req.id,
            'رقم_الموظف': emp_number,
            'اسم_الموظف': emp_name,
            'القسم': emp_dept or 'غير محدد',
            'نوع_الإجازة': {
                'annual': 'إجازة سنوية',
                'sick': 'إجازة مرضية',
                'emergency': 'إجازة طارئة',
                'maternity': 'إجازة أمومة',
                'unpaid': 'إجازة بدون راتب'
            }.get(req.leave_type, req.leave_type),
            'من_تاريخ': req.start_date.strftime('%Y-%m-%d'),
            'إلى_تاريخ': req.end_date.strftime('%Y-%m-%d'),
            'عدد_الأيام': req.days_requested,
            'السبب': req.reason,
            'الحالة': {
                'pending': 'معلقة',
                'approved': 'موافق عليها',
                'rejected': 'مرفوضة',
                'cancelled': 'ملغاة'
            }.get(req.status, req.status),
            'تاريخ_التقديم': req.created_at.strftime('%Y-%m-%d %H:%M'),
            'تاريخ_المراجعة': req.review_date.strftime('%Y-%m-%d %H:%M') if req.review_date else '',
            'ملاحظات_المراجعة': req.review_notes or ''
        })

    return {
        'success': True,
        'data': export_data,
        'filename': f'leave_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
        'total_records': len(export_data)
    }

@bp.route('/api/leave_settings', methods=['GET', 'POST'])
@login_required
def api_leave_settings():
    """إعدادات الإجازات"""
    if request.method == 'GET':
        # إرجاع الإعدادات الحالية
        settings = {
            'annual_leave_days': 30,  # أيام الإجازة السنوية
            'sick_leave_days': 15,    # أيام الإجازة المرضية
            'maternity_leave_days': 90, # أيام إجازة الأمومة
            'emergency_leave_days': 5,  # أيام الإجازة الطارئة
            'administrative_leave_days': 10, # أيام الإجازة الإدارية
            'holiday_compensation_days': 15, # أيام إجازة بدل أعياد
            'grant_compensation_days': 7, # أيام إجازة بدل منح
            'casual_leave_days': 12, # أيام الإجازة العرضية
            'auto_approve_annual': False, # الموافقة التلقائية على الإجازة السنوية
            'require_medical_certificate': True, # طلب شهادة طبية للإجازة المرضية
            'advance_notice_days': 7, # عدد أيام الإشعار المسبق
            'max_consecutive_days': 14 # الحد الأقصى للأيام المتتالية
        }
        return {'success': True, 'settings': settings}

    else:
        # حفظ الإعدادات الجديدة
        data = request.get_json() or {}
        # هنا يمكن حفظ الإعدادات في قاعدة البيانات
        return {'success': True, 'message': 'تم حفظ الإعدادات بنجاح'}

@bp.route('/api/employee_leave_balance/<int:employee_id>')
@login_required
def api_employee_leave_balance(employee_id):
    """رصيد إجازات الموظف"""
    from datetime import datetime, timedelta

    employee = Employee.query.get_or_404(employee_id)

    # حساب الإجازات المستخدمة هذا العام
    current_year = datetime.now().year
    year_start = datetime(current_year, 1, 1)
    year_end = datetime(current_year, 12, 31)

    from sqlalchemy import func

    used_leaves = db.session.query(
        LeaveRequest.leave_type,
        func.sum(LeaveRequest.days_requested).label('total_days')
    ).filter(
        LeaveRequest.employee_id == employee_id,
        LeaveRequest.status == 'approved',
        LeaveRequest.start_date >= year_start,
        LeaveRequest.start_date <= year_end
    ).group_by(LeaveRequest.leave_type).all()

    # الأرصدة المتاحة (يمكن تخصيصها حسب المنصب)
    available_balances = {
        'annual': 30,
        'sick': 15,
        'emergency': 5,
        'maternity': 90 if employee.gender == 'female' else 0,
        'administrative': 10,  # إجازة إدارية
        'holiday_compensation': 15,  # إجازة بدل أعياد
        'grant_compensation': 7,  # إجازة بدل منح
        'casual': 12,  # إجازة عرضية
        'unpaid': 365  # بدون حد للإجازة بدون راتب
    }

    # حساب الأرصدة المتبقية
    balances = {}
    used_dict = {ul[0]: ul[1] for ul in used_leaves}

    for leave_type, available in available_balances.items():
        used = used_dict.get(leave_type, 0) or 0
        balances[leave_type] = {
            'available': available,
            'used': used,
            'remaining': max(0, available - used),
            'type_name': {
                'annual': 'إجازة سنوية',
                'sick': 'إجازة مرضية',
                'emergency': 'إجازة طارئة',
                'maternity': 'إجازة أمومة',
                'administrative': 'إجازة إدارية',
                'holiday_compensation': 'إجازة بدل أعياد',
                'grant_compensation': 'إجازة بدل منح',
                'casual': 'إجازة عرضية',
                'unpaid': 'إجازة بدون راتب'
            }.get(leave_type, leave_type)
        }

    return {
        'success': True,
        'employee': {
            'id': employee.id,
            'name': employee.full_name_ar,
            'employee_number': employee.employee_number,
            'department': employee.department
        },
        'balances': balances,
        'year': current_year
    }

# مسارات API لإدارة الموظفين
@bp.route('/api/add_employee', methods=['POST'])
@login_required
def api_add_employee():
    """إضافة موظف جديد"""
    from flask import request

    data = request.get_json()

    if not data:
        return {'success': False, 'message': 'لا توجد بيانات'}

    # التحقق من الحقول المطلوبة
    required_fields = ['first_name_ar', 'last_name_ar', 'national_id', 'employee_number', 'department', 'position', 'hire_date']
    for field in required_fields:
        if not data.get(field):
            return {'success': False, 'message': f'الحقل {field} مطلوب'}

    # التحقق من صحة رقم الموظف (أرقام فقط)
    employee_number = str(data['employee_number']).strip()
    if not employee_number.isdigit() or len(employee_number) < 4:
        return {'success': False, 'message': 'رقم الموظف يجب أن يكون أرقام فقط (4 أرقام على الأقل)'}

    # التحقق من عدم تكرار رقم الموظف
    try:
        existing_employee = Employee.query.filter_by(employee_number=employee_number).first()
        if existing_employee:
            return {'success': False, 'message': 'رقم الموظف موجود مسبقاً'}
    except:
        # إذا كانت قاعدة البيانات غير متاحة، نتجاهل هذا التحقق
        pass

    # التحقق من عدم تكرار رقم الهوية
    existing_national_id = Employee.query.filter_by(national_id=data['national_id']).first()
    if existing_national_id:
        return {'success': False, 'message': 'رقم الهوية موجود مسبقاً'}

    try:
        from datetime import datetime
        from decimal import Decimal

        # إنشاء موظف جديد
        employee = Employee(
            first_name_ar=data['first_name_ar'],
            last_name_ar=data['last_name_ar'],
            first_name_en=data.get('first_name_en'),
            last_name_en=data.get('last_name_en'),
            national_id=data['national_id'],
            birth_date=datetime.strptime(data['birth_date'], '%Y-%m-%d').date() if data.get('birth_date') else None,
            gender=data.get('gender'),
            employee_number=employee_number,
            department=data['department'],
            position=data['position'],
            hire_date=datetime.strptime(data['hire_date'], '%Y-%m-%d').date(),
            employment_status=data.get('employment_status', 'active'),
            # manager_id=int(data['manager_id']) if data.get('manager_id') else None,
            basic_salary=Decimal(str(data.get('basic_salary', 0))),
            allowances=Decimal(str(data.get('allowances', 0))),
            email=data.get('email'),
            phone=data.get('phone'),
            address=data.get('address')
        )

        db.session.add(employee)
        db.session.commit()

        return {
            'success': True,
            'message': 'تم إضافة الموظف بنجاح',
            'employee_id': employee.id
        }

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'message': f'حدث خطأ: {str(e)}'}

@bp.route('/api/employee/<int:employee_id>')
@login_required
def api_get_employee(employee_id):
    """جلب بيانات موظف محدد"""
    employee = Employee.query.get_or_404(employee_id)

    return {
        'success': True,
        'employee': {
            'id': employee.id,
            'first_name_ar': employee.first_name_ar,
            'last_name_ar': employee.last_name_ar,
            'first_name_en': employee.first_name_en,
            'last_name_en': employee.last_name_en,
            'full_name_ar': employee.full_name_ar,
            'national_id': employee.national_id,
            'birth_date': employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else None,
            'gender': employee.gender,
            'employee_number': employee.employee_number,
            'department': employee.department,
            'position': employee.position,
            'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else None,
            'employment_status': employee.employment_status,
            'manager_id': employee.manager_id,
            'basic_salary': float(employee.basic_salary) if employee.basic_salary else 0,
            'allowances': float(employee.allowances) if employee.allowances else 0,
            'email': employee.email,
            'phone': employee.phone,
            'address': employee.address
        }
    }

@bp.route('/api/update_employee/<int:employee_id>', methods=['PUT'])
@login_required
def api_update_employee(employee_id):
    """تحديث بيانات موظف"""
    from flask import request

    employee = Employee.query.get_or_404(employee_id)
    data = request.get_json()

    if not data:
        return {'success': False, 'message': 'لا توجد بيانات'}

    try:
        from datetime import datetime
        from decimal import Decimal

        # تحديث البيانات
        if 'first_name_ar' in data:
            employee.first_name_ar = data['first_name_ar']
        if 'last_name_ar' in data:
            employee.last_name_ar = data['last_name_ar']
        if 'first_name_en' in data:
            employee.first_name_en = data['first_name_en']
        if 'last_name_en' in data:
            employee.last_name_en = data['last_name_en']
        if 'national_id' in data:
            # التحقق من عدم تكرار رقم الهوية
            existing = Employee.query.filter(Employee.national_id == data['national_id'], Employee.id != employee_id).first()
            if existing:
                return {'success': False, 'message': 'رقم الهوية موجود مسبقاً'}
            employee.national_id = data['national_id']
        if 'birth_date' in data and data['birth_date']:
            employee.birth_date = datetime.strptime(data['birth_date'], '%Y-%m-%d').date()
        if 'gender' in data:
            employee.gender = data['gender']
        if 'employee_number' in data:
            # التحقق من عدم تكرار رقم الموظف
            existing = Employee.query.filter(Employee.employee_number == data['employee_number'], Employee.id != employee_id).first()
            if existing:
                return {'success': False, 'message': 'رقم الموظف موجود مسبقاً'}
            employee.employee_number = data['employee_number']
        if 'department' in data:
            employee.department = data['department']
        if 'position' in data:
            employee.position = data['position']
        if 'hire_date' in data and data['hire_date']:
            employee.hire_date = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
        if 'employment_status' in data:
            employee.employment_status = data['employment_status']
        if 'manager_id' in data:
            employee.manager_id = int(data['manager_id']) if data['manager_id'] else None
        if 'basic_salary' in data:
            employee.basic_salary = Decimal(str(data['basic_salary']))
        if 'allowances' in data:
            employee.allowances = Decimal(str(data['allowances']))
        if 'email' in data:
            employee.email = data['email']
        if 'phone' in data:
            employee.phone = data['phone']
        if 'address' in data:
            employee.address = data['address']

        db.session.commit()

        return {'success': True, 'message': 'تم تحديث بيانات الموظف بنجاح'}

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'message': f'حدث خطأ: {str(e)}'}

@bp.route('/api/delete_employee/<int:employee_id>', methods=['DELETE'])
@login_required
def api_delete_employee(employee_id):
    """حذف موظف"""
    employee = Employee.query.get_or_404(employee_id)

    try:
        # التحقق من وجود بيانات مرتبطة
        attendance_count = Attendance.query.filter_by(employee_id=employee_id).count()
        payroll_count = Payroll.query.filter_by(employee_id=employee_id).count()
        leave_count = LeaveRequest.query.filter_by(employee_id=employee_id).count()

        if attendance_count > 0 or payroll_count > 0 or leave_count > 0:
            # تغيير حالة الموظف إلى منتهي الخدمة بدلاً من الحذف
            employee.employment_status = 'terminated'
            db.session.commit()
            return {'success': True, 'message': 'تم تغيير حالة الموظف إلى منتهي الخدمة (يوجد بيانات مرتبطة)'}
        else:
            # حذف الموظف إذا لم توجد بيانات مرتبطة
            db.session.delete(employee)
            db.session.commit()
            return {'success': True, 'message': 'تم حذف الموظف بنجاح'}

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'message': f'حدث خطأ: {str(e)}'}

@bp.route('/api/export_employees')
@login_required
def api_export_employees():
    """تصدير قائمة الموظفين"""
    from datetime import datetime

    employees = Employee.query.all()

    export_data = []
    for emp in employees:
        export_data.append({
            'رقم_الموظف': emp.employee_number,
            'الاسم_الكامل_عربي': emp.full_name_ar,
            'الاسم_الكامل_إنجليزي': f"{emp.first_name_en or ''} {emp.last_name_en or ''}".strip(),
            'رقم_الهوية': emp.national_id,
            'تاريخ_الميلاد': emp.birth_date.strftime('%Y-%m-%d') if emp.birth_date else '',
            'الجنس': {'male': 'ذكر', 'female': 'أنثى'}.get(emp.gender, emp.gender or ''),
            'القسم': emp.department or '',
            'المنصب': emp.position or '',
            'تاريخ_التوظيف': emp.hire_date.strftime('%Y-%m-%d') if emp.hire_date else '',
            'حالة_التوظيف': {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'terminated': 'منتهي الخدمة'
            }.get(emp.employment_status, emp.employment_status or ''),
            'الراتب_الأساسي': float(emp.basic_salary) if emp.basic_salary else 0,
            'البدلات': float(emp.allowances) if emp.allowances else 0,
            'إجمالي_الراتب': float((emp.basic_salary or 0) + (emp.allowances or 0)),
            'البريد_الإلكتروني': emp.email or '',
            'رقم_الهاتف': emp.phone or '',
            'العنوان': emp.address or ''
        })

    return {
        'success': True,
        'data': export_data,
        'filename': f'employees_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
        'total_records': len(export_data)
    }
