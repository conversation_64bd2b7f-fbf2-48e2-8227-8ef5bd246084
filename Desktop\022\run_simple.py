"""
تشغيل النظام مع الميزات الجديدة
"""

from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///erp_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# نموذج المستخدم
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='employee')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات الأساسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    stats = {
        'total_employees': 25,
        'total_customers': 150,
        'monthly_revenue': 125000,
        'pending_orders': 8,
        'notifications_count': 3,
        'workflow_tasks': 2
    }
    return render_template('dashboard/index.html', stats=stats)

# APIs الإشعارات
@app.route('/api/notifications')
@login_required
def get_notifications():
    notifications = [
        {
            'id': 1,
            'title': 'موظف جديد',
            'message': 'تم إضافة موظف جديد: أحمد محمد',
            'type': 'success',
            'time_ago': 'منذ 5 دقائق',
            'is_read': False,
            'icon': 'fas fa-user-plus'
        },
        {
            'id': 2,
            'title': 'فاتورة جديدة',
            'message': 'فاتورة جديدة تحتاج موافقة - 15,000 ر.ق',
            'type': 'warning',
            'time_ago': 'منذ 15 دقيقة',
            'is_read': False,
            'icon': 'fas fa-file-invoice'
        },
        {
            'id': 3,
            'title': 'تذكير اجتماع',
            'message': 'اجتماع فريق المبيعات الساعة 3:00 م',
            'type': 'info',
            'time_ago': 'منذ ساعة',
            'is_read': True,
            'icon': 'fas fa-calendar'
        }
    ]
    return jsonify(notifications)

@app.route('/api/notifications/count')
@login_required
def get_notifications_count():
    return jsonify({'count': 2})

@app.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})

@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    return jsonify({'success': True, 'message': 'تم تحديد جميع الإشعارات كمقروءة'})

# APIs سير العمل
@app.route('/api/workflow/tasks')
@login_required
def get_workflow_tasks():
    tasks = [
        {
            'id': 1,
            'title': 'موافقة طلب إجازة',
            'description': 'طلب إجازة - سارة أحمد',
            'due_date': '2024-12-20',
            'priority': 'high'
        },
        {
            'id': 2,
            'title': 'مراجعة أمر شراء',
            'description': 'أمر شراء معدات مكتبية - 5,000 ر.ق',
            'due_date': '2024-12-21',
            'priority': 'normal'
        }
    ]
    return jsonify(tasks)

@app.route('/api/system/status')
@login_required
def get_system_status():
    stats = {
        'notifications_count': 2,
        'workflow_tasks_count': 2,
        'active_users': 12,
        'system_uptime': '99.9%'
    }
    return jsonify(stats)

# صفحات الإعدادات
@app.route('/settings')
@login_required
def settings_index():
    return render_template('settings/index.html')

@app.route('/settings/api/test-notification', methods=['POST'])
@login_required
def test_notification():
    return jsonify({
        'success': True,
        'message': 'تم إرسال الإشعار التجريبي بنجاح'
    })

# لوحة الإدارة
@app.route('/admin')
@login_required
def admin_dashboard():
    if current_user.role != 'admin':
        flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    stats = {
        'total_users': 15,
        'active_users': 12,
        'total_notifications': 45,
        'unread_notifications': 8,
        'active_workflows': 3,
        'completed_workflows': 12,
        'enabled_modules': 8
    }
    
    weekly_stats = {
        'new_users': 2,
        'new_notifications': 15,
        'completed_workflows': 5
    }
    
    modules = [
        {'module_name': 'accounting', 'module_title': 'المحاسبة', 'is_enabled': True},
        {'module_name': 'hr', 'module_title': 'الموارد البشرية', 'is_enabled': True},
        {'module_name': 'inventory', 'module_title': 'المخزون', 'is_enabled': True},
        {'module_name': 'sales', 'module_title': 'المبيعات', 'is_enabled': True},
        {'module_name': 'notifications', 'module_title': 'الإشعارات', 'is_enabled': True},
        {'module_name': 'workflow', 'module_title': 'سير العمل', 'is_enabled': True}
    ]
    
    warnings = [
        {
            'type': 'info',
            'message': 'مساحة قاعدة البيانات: 2.5 MB (طبيعية)',
            'icon': 'fas fa-database'
        }
    ]
    
    return render_template('admin/advanced_dashboard.html',
                         stats=stats,
                         weekly_stats=weekly_stats,
                         recent_activities=[],
                         modules=modules,
                         warnings=warnings)

@app.route('/admin/api/send-announcement', methods=['POST'])
@login_required
def send_announcement():
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإرسال الإعلانات'}), 403
    
    data = request.get_json()
    return jsonify({
        'success': True,
        'message': 'تم إرسال الإعلان بنجاح لـ 12 مستخدم',
        'recipients_count': 12
    })

@app.route('/admin/api/backup/create', methods=['POST'])
@login_required
def create_backup():
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإنشاء النسخ الاحتياطية'}), 403
    
    backup_filename = f"backup_{datetime.utcnow().strftime('%Y_%m_%d_%H_%M')}.db"
    return jsonify({
        'success': True,
        'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
        'filename': backup_filename
    })

@app.route('/admin/api/system/cleanup', methods=['POST'])
@login_required
def system_cleanup():
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بتنظيف النظام'}), 403
    
    cleanup_stats = {
        'old_notifications': 15,
        'old_logs': 25,
        'temp_files': 8
    }
    
    return jsonify({
        'success': True,
        'message': 'تم تنظيف النظام بنجاح',
        'stats': cleanup_stats
    })

# إنشاء الجداول والبيانات التجريبية
def create_sample_data():
    db.create_all()
    
    # إنشاء مستخدم مدير إذا لم يكن موجوداً
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            full_name='مدير النظام',
            role='admin'
        )
        db.session.add(admin)
        db.session.commit()
        print("✓ تم إنشاء المستخدم المدير")

if __name__ == '__main__':
    with app.app_context():
        create_sample_data()
    
    print("🚀 تم تشغيل نظام إدارة الموارد مع الميزات المتقدمة!")
    print("🌐 الرابط: http://localhost:5000")
    print("🔑 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("\n📱 الميزات الجديدة:")
    print("• نظام الإشعارات المتقدم")
    print("• سير العمل والموافقات")
    print("• لوحة الإدارة المتقدمة")
    print("• إعدادات النظام الشاملة")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
