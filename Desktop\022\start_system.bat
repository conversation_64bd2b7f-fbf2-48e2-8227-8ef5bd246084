@echo off
chcp 65001 >nul
title نظام إدارة الموارد - دولة قطر

echo.
echo ========================================
echo    نظام إدارة الموارد - دولة قطر
echo        Qatar ERP System
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات المطلوبة
echo 🔍 التحقق من المكتبات المطلوبة...
pip show flask >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة

REM تشغيل النظام
echo.
echo 🚀 جاري تشغيل النظام...
echo 🌐 سيتم فتح النظام على: http://localhost:5000
echo ⏹️  للإيقاف: اضغط Ctrl+C
echo.

python run.py

echo.
echo 👋 شكراً لاستخدام نظام إدارة الموارد
pause
