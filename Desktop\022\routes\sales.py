from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.sales import Customer, SalesOrder, Invoice
from database_setup import db
from datetime import datetime

bp = Blueprint('sales', __name__, url_prefix='/sales')

@bp.route('/')
@login_required
def index():
    """صفحة المبيعات الرئيسية"""
    # جلب جميع البيانات المطلوبة للقالب
    sales = SalesOrder.query.all()
    customers = Customer.query.filter_by(is_active=True).all()

    # حساب الإحصائيات
    total_sales = sum(sale.total_amount or 0 for sale in sales)
    monthly_sales = sum(sale.total_amount or 0 for sale in sales if sale.order_date and sale.order_date.month == datetime.now().month)
    unique_customers = len(set(sale.customer_id for sale in sales if sale.customer_id))

    # إحصائيات إضافية
    stats = {
        'total_customers': len(customers),
        'pending_orders': SalesOrder.query.filter_by(status='pending').count(),
        'unpaid_invoices': Invoice.query.filter_by(status='posted').count()
    }

    return render_template('sales/index.html',
                         sales=sales,
                         customers=customers,
                         total_sales=total_sales,
                         monthly_sales=monthly_sales,
                         unique_customers=unique_customers,
                         stats=stats)

@bp.route('/customers')
@login_required
def customers():
    """قائمة العملاء"""
    customers = Customer.query.filter_by(is_active=True).all()
    return render_template('sales/customers.html', customers=customers)

@bp.route('/orders')
@login_required
def orders():
    """أوامر البيع"""
    orders = SalesOrder.query.order_by(SalesOrder.order_date.desc()).all()
    return render_template('sales/orders.html', orders=orders)

@bp.route('/invoices')
@login_required
def invoices():
    """الفواتير"""
    invoices = Invoice.query.order_by(Invoice.invoice_date.desc()).all()
    return render_template('sales/invoices.html', invoices=invoices)
