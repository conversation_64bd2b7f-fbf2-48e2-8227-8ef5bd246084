from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.procurement import Supplier, PurchaseOrder
from database_setup import db
from datetime import datetime

bp = Blueprint('procurement', __name__, url_prefix='/procurement')

@bp.route('/')
@login_required
def index():
    """صفحة المشتريات الرئيسية"""
    # جلب جميع البيانات المطلوبة للقالب
    purchases = PurchaseOrder.query.all()
    suppliers = Supplier.query.filter_by(is_active=True).all()

    # حساب الإحصائيات
    total_purchases = sum(purchase.total_amount or 0 for purchase in purchases)
    pending_purchases = len([p for p in purchases if p.status == 'pending'])
    suppliers_count = len(suppliers)

    # إحصائيات إضافية
    stats = {
        'total_suppliers': len(suppliers),
        'pending_orders': pending_purchases,
        'received_orders': PurchaseOrder.query.filter_by(status='received').count()
    }

    return render_template('procurement/index.html',
                         purchases=purchases,
                         suppliers=suppliers,
                         total_purchases=total_purchases,
                         pending_purchases=pending_purchases,
                         suppliers_count=suppliers_count,
                         stats=stats)

@bp.route('/suppliers')
@login_required
def suppliers():
    """قائمة الموردين"""
    suppliers = Supplier.query.filter_by(is_active=True).all()
    return render_template('procurement/suppliers.html', suppliers=suppliers)

@bp.route('/purchase_orders')
@login_required
def purchase_orders():
    """أوامر الشراء"""
    orders = PurchaseOrder.query.order_by(PurchaseOrder.order_date.desc()).all()
    return render_template('procurement/purchase_orders.html', orders=orders)
