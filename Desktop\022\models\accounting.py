from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal
from app import db

class Account(db.Model):
    __tablename__ = 'accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    
    # نوع الحساب
    account_type = db.Column(db.String(20), nullable=False)  # asset, liability, equity, revenue, expense
    parent_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))
    level = db.Column(db.Integer, default=1)
    
    # حالة الحساب
    is_active = db.Column(db.Boolean, default=True)
    is_system = db.Column(db.Bo<PERSON>, default=False)  # حسابات النظام الأساسية
    
    # أرصدة
    opening_balance = db.Column(db.Numeric(15, 2), default=0)
    current_balance = db.Column(db.Numeric(15, 2), default=0)
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    children = db.relationship('Account', backref=db.backref('parent', remote_side=[id]))
    transactions = db.relationship('Transaction', backref='account', lazy='dynamic')
    
    def __repr__(self):
        return f'<Account {self.code} - {self.name_ar}>'
    
    def get_balance(self, date=None):
        """حساب الرصيد في تاريخ معين"""
        query = self.transactions
        if date:
            query = query.filter(Transaction.date <= date)
        
        debit_total = query.filter(Transaction.type == 'debit').with_entities(
            db.func.sum(Transaction.amount)).scalar() or 0
        credit_total = query.filter(Transaction.type == 'credit').with_entities(
            db.func.sum(Transaction.amount)).scalar() or 0
        
        if self.account_type in ['asset', 'expense']:
            return self.opening_balance + debit_total - credit_total
        else:
            return self.opening_balance + credit_total - debit_total

class JournalEntry(db.Model):
    __tablename__ = 'journal_entries'
    
    id = db.Column(db.Integer, primary_key=True)
    entry_number = db.Column(db.String(20), unique=True, nullable=False)
    date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    description = db.Column(db.Text, nullable=False)
    
    # معلومات القيد
    reference = db.Column(db.String(50))  # مرجع خارجي
    source = db.Column(db.String(20))  # manual, sales, purchase, payroll
    
    # حالة القيد
    status = db.Column(db.String(20), default='draft')  # draft, posted, cancelled
    
    # المبالغ
    total_debit = db.Column(db.Numeric(15, 2), default=0)
    total_credit = db.Column(db.Numeric(15, 2), default=0)
    
    # المستخدم والتواريخ
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    posted_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    posted_at = db.Column(db.DateTime)
    
    # العلاقات
    transactions = db.relationship('Transaction', backref='journal_entry', cascade='all, delete-orphan')
    creator = db.relationship('User', foreign_keys=[created_by])
    poster = db.relationship('User', foreign_keys=[posted_by])
    
    def __repr__(self):
        return f'<JournalEntry {self.entry_number}>'
    
    def is_balanced(self):
        """فحص توازن القيد"""
        return abs(self.total_debit - self.total_credit) < 0.01
    
    def post(self, user_id):
        """ترحيل القيد"""
        if not self.is_balanced():
            raise ValueError("القيد غير متوازن")
        
        if self.status != 'draft':
            raise ValueError("لا يمكن ترحيل قيد مرحل مسبقاً")
        
        self.status = 'posted'
        self.posted_by = user_id
        self.posted_at = datetime.utcnow()
        
        # تحديث أرصدة الحسابات
        for transaction in self.transactions:
            account = transaction.account
            if transaction.type == 'debit':
                if account.account_type in ['asset', 'expense']:
                    account.current_balance += transaction.amount
                else:
                    account.current_balance -= transaction.amount
            else:  # credit
                if account.account_type in ['asset', 'expense']:
                    account.current_balance -= transaction.amount
                else:
                    account.current_balance += transaction.amount
        
        db.session.commit()

class Transaction(db.Model):
    __tablename__ = 'transactions'
    
    id = db.Column(db.Integer, primary_key=True)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'), nullable=False)
    
    # تفاصيل المعاملة
    description = db.Column(db.String(255))
    type = db.Column(db.String(10), nullable=False)  # debit, credit
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    date = db.Column(db.Date, nullable=False)
    
    # معلومات إضافية
    reference = db.Column(db.String(50))
    cost_center = db.Column(db.String(50))
    
    def __repr__(self):
        return f'<Transaction {self.type} {self.amount}>'
