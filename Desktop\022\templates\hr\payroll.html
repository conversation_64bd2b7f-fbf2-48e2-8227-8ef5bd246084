{% extends "base.html" %}

{% block title %}إدارة الرواتب - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-money-bill-wave me-2 text-primary"></i>
            إدارة الرواتب
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('hr.index') }}">الموارد البشرية</a></li>
                <li class="breadcrumb-item active">الرواتب</li>
            </ol>
        </nav>
    </div>
</div>

<!-- إحصائيات الرواتب -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-2x text-primary"></i>
                </div>
                <h3 class="text-primary">{{ payrolls|length if payrolls else 0 }}</h3>
                <p class="text-muted mb-0">إجمالي كشوف الرواتب</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-2x text-success"></i>
                </div>
                <h3 class="text-success">{{ payrolls|selectattr("status", "equalto", "approved")|list|length }}</h3>
                <p class="text-muted mb-0">معتمد</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-clock fa-2x text-warning"></i>
                </div>
                <h3 class="text-warning">{{ payrolls|selectattr("status", "equalto", "draft")|list|length }}</h3>
                <p class="text-muted mb-0">في انتظار الاعتماد</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-dollar-sign fa-2x text-info"></i>
                </div>
                <h3 class="text-info">
                    {% set approved_payrolls = payrolls|selectattr("status", "equalto", "approved")|list %}
                    {% set total_amount = 0 %}
                    {% for payroll in approved_payrolls %}
                        {% set total_amount = total_amount + (payroll.net_salary|float if payroll.net_salary else 0) %}
                    {% endfor %}
                    {{ total_amount|number_format }} ر.ق
                </h3>
                <p class="text-muted mb-0">إجمالي الرواتب المعتمدة</p>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التحكم -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">الشهر</label>
                        <select class="form-select" id="monthFilter">
                            <option value="">جميع الشهور</option>
                            <option value="01">يناير</option>
                            <option value="02">فبراير</option>
                            <option value="03">مارس</option>
                            <option value="04">أبريل</option>
                            <option value="05">مايو</option>
                            <option value="06">يونيو</option>
                            <option value="07">يوليو</option>
                            <option value="08">أغسطس</option>
                            <option value="09">سبتمبر</option>
                            <option value="10">أكتوبر</option>
                            <option value="11">نوفمبر</option>
                            <option value="12">ديسمبر</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">السنة</label>
                        <select class="form-select" id="yearFilter">
                            <option value="">جميع السنوات</option>
                            <option value="2024" selected>2024</option>
                            <option value="2023">2023</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">القسم</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                            <option value="hr">الموارد البشرية</option>
                            <option value="finance">المالية</option>
                            <option value="it">تقنية المعلومات</option>
                            <option value="operations">العمليات</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="draft">مسودة</option>
                            <option value="approved">معتمد</option>
                            <option value="paid">مدفوع</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="generatePayroll()">
                                <i class="fas fa-plus me-2"></i>
                                إنتاج كشف راتب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الرواتب -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                كشوف الرواتب
            </div>
            <div class="card-body">
                {% if payrolls %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>فترة الراتب</th>
                                <th>الراتب الأساسي</th>
                                <th>البدلات</th>
                                <th>الإضافي</th>
                                <th>الخصومات</th>
                                <th>صافي الراتب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payroll in payrolls %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            {{ payroll.employee.first_name_ar[0] if payroll.employee.first_name_ar else 'م' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ payroll.employee.full_name_ar }}</div>
                                            <small class="text-muted">{{ payroll.employee.employee_number }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ payroll.pay_period_start.strftime('%Y-%m-%d') }}</strong>
                                        <br>
                                        <small class="text-muted">إلى {{ payroll.pay_period_end.strftime('%Y-%m-%d') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-primary">{{ (payroll.basic_salary|float if payroll.basic_salary else 0)|number_format }} ر.ق</span>
                                </td>
                                <td>
                                    {% if payroll.allowances and payroll.allowances > 0 %}
                                    <span class="text-success">{{ (payroll.allowances|float)|number_format }} ر.ق</span>
                                    {% else %}
                                    <span class="text-muted">0 ر.ق</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payroll.overtime_amount and payroll.overtime_amount > 0 %}
                                    <span class="text-info">{{ (payroll.overtime_amount|float)|number_format }} ر.ق</span>
                                    {% else %}
                                    <span class="text-muted">0 ر.ق</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payroll.total_deductions and payroll.total_deductions > 0 %}
                                    <span class="text-danger">{{ (payroll.total_deductions|float)|number_format }} ر.ق</span>
                                    {% else %}
                                    <span class="text-muted">0 ر.ق</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="fw-bold text-success fs-5">{{ (payroll.net_salary|float if payroll.net_salary else 0)|number_format }} ر.ق</span>
                                </td>
                                <td>
                                    {% if payroll.status == 'draft' %}
                                    <span class="badge bg-warning">مسودة</span>
                                    {% elif payroll.status == 'approved' %}
                                    <span class="badge bg-success">معتمد</span>
                                    {% elif payroll.status == 'paid' %}
                                    <span class="badge bg-primary">مدفوع</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل" onclick="viewPayroll({{ payroll.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if payroll.status == 'draft' %}
                                        <button class="btn btn-outline-success" title="اعتماد" onclick="approvePayroll({{ payroll.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" title="تعديل" onclick="editPayroll({{ payroll.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-outline-info" title="طباعة" onclick="printPayroll({{ payroll.id }})">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-money-bill-wave"></i>
                    <h5 class="text-muted">لا يوجد كشوف رواتب</h5>
                    <p class="text-muted">ابدأ بإنتاج كشوف الرواتب للموظفين</p>
                    <button class="btn btn-primary" onclick="generatePayroll()">
                        <i class="fas fa-plus me-2"></i>
                        إنتاج كشف راتب جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="generateMonthlyPayroll()">
                            <i class="fas fa-calendar me-2"></i>
                            إنتاج رواتب الشهر
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="payrollReport()">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير الرواتب
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="exportPayroll()">
                            <i class="fas fa-download me-2"></i>
                            تصدير البيانات
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="payrollSettings()">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات الرواتب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإنتاج كشف راتب -->
<div class="modal fade" id="payrollModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنتاج كشف راتب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="payrollForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الموظف</label>
                            <select class="form-select" id="employeeSelect" required>
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate" required>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate" required>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الراتب الأساسي</label>
                            <input type="number" class="form-control" id="basicSalary" step="0.01" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البدلات</label>
                            <input type="number" class="form-control" id="allowances" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">مبلغ الساعات الإضافية</label>
                            <input type="number" class="form-control" id="overtimeAmount" step="0.01" value="0">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المكافآت</label>
                            <input type="number" class="form-control" id="bonus" step="0.01" value="0">
                        </div>
                    </div>

                    <hr class="my-4">
                    <h6>الخصومات</h6>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">خصم الضرائب</label>
                            <input type="number" class="form-control" id="taxDeduction" step="0.01" value="0">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">خصم التأمين</label>
                            <input type="number" class="form-control" id="insuranceDeduction" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">خصم القروض</label>
                            <input type="number" class="form-control" id="loanDeduction" step="0.01" value="0">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">خصومات أخرى</label>
                            <input type="number" class="form-control" id="otherDeductions" step="0.01" value="0">
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <strong>صافي الراتب: </strong>
                                <span id="netSalaryDisplay" class="fs-5 fw-bold">0.00 ر.ق</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="payrollNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePayroll()">حفظ كشف الراتب</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديد التواريخ الافتراضية
const now = new Date();
const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

// وظائف الرواتب
function generatePayroll() {
    loadEmployees();
    new bootstrap.Modal(document.getElementById('payrollModal')).show();
}

function loadEmployees() {
    const select = document.getElementById('employeeSelect');
    select.innerHTML = '<option value="">اختر الموظف</option>';

    // موظفين تجريبيين
    const employees = [
        {id: 1, name: 'أحمد محمد الكعبي', salary: 15000},
        {id: 2, name: 'فاطمة علي النعيمي', salary: 12000},
        {id: 3, name: 'محمد سالم الثاني', salary: 13000}
    ];

    employees.forEach(emp => {
        const option = document.createElement('option');
        option.value = emp.id;
        option.textContent = emp.name;
        option.dataset.salary = emp.salary;
        select.appendChild(option);
    });
}

// تحديث الراتب الأساسي عند اختيار الموظف
document.getElementById('employeeSelect').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.dataset.salary) {
        document.getElementById('basicSalary').value = selectedOption.dataset.salary;
        calculateNetSalary();
    }
});

// حساب صافي الراتب
function calculateNetSalary() {
    const basicSalary = parseFloat(document.getElementById('basicSalary').value) || 0;
    const allowances = parseFloat(document.getElementById('allowances').value) || 0;
    const overtimeAmount = parseFloat(document.getElementById('overtimeAmount').value) || 0;
    const bonus = parseFloat(document.getElementById('bonus').value) || 0;

    const taxDeduction = parseFloat(document.getElementById('taxDeduction').value) || 0;
    const insuranceDeduction = parseFloat(document.getElementById('insuranceDeduction').value) || 0;
    const loanDeduction = parseFloat(document.getElementById('loanDeduction').value) || 0;
    const otherDeductions = parseFloat(document.getElementById('otherDeductions').value) || 0;

    const grossSalary = basicSalary + allowances + overtimeAmount + bonus;
    const totalDeductions = taxDeduction + insuranceDeduction + loanDeduction + otherDeductions;
    const netSalary = grossSalary - totalDeductions;

    document.getElementById('netSalaryDisplay').textContent = netSalary.toLocaleString('ar-QA') + ' ر.ق';
}

// إضافة مستمعين للحقول لحساب صافي الراتب تلقائياً
['basicSalary', 'allowances', 'overtimeAmount', 'bonus', 'taxDeduction', 'insuranceDeduction', 'loanDeduction', 'otherDeductions'].forEach(id => {
    document.getElementById(id).addEventListener('input', calculateNetSalary);
});

function savePayroll() {
    if (!document.getElementById('employeeSelect').value) {
        alert('يرجى اختيار الموظف');
        return;
    }

    if (!document.getElementById('basicSalary').value) {
        alert('يرجى إدخال الراتب الأساسي');
        return;
    }

    alert('تم حفظ كشف الراتب بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('payrollModal')).hide();
    // هنا يمكن إضافة استدعاء AJAX لحفظ البيانات
}

function viewPayroll(id) {
    alert(`عرض تفاصيل كشف الراتب رقم ${id}`);
}

function approvePayroll(id) {
    if (confirm('هل تريد اعتماد كشف الراتب؟')) {
        alert(`تم اعتماد كشف الراتب رقم ${id}`);
        // هنا يمكن إضافة استدعاء AJAX
    }
}

function editPayroll(id) {
    alert(`تعديل كشف الراتب رقم ${id}`);
}

function printPayroll(id) {
    alert(`طباعة كشف الراتب رقم ${id}`);
    // هنا يمكن إضافة وظيفة الطباعة
}

function generateMonthlyPayroll() {
    if (confirm('هل تريد إنتاج كشوف رواتب جميع الموظفين لهذا الشهر؟')) {
        alert('سيتم إنتاج كشوف الرواتب');
        // هنا يمكن إضافة استدعاء AJAX
    }
}

function payrollReport() {
    alert('سيتم إنتاج تقرير الرواتب');
}

function exportPayroll() {
    alert('سيتم تصدير بيانات الرواتب');
}

function payrollSettings() {
    alert('إعدادات الرواتب');
}

// تصفية الجدول
document.getElementById('monthFilter').addEventListener('change', filterTable);
document.getElementById('yearFilter').addEventListener('change', filterTable);
document.getElementById('departmentFilter').addEventListener('change', filterTable);
document.getElementById('statusFilter').addEventListener('change', filterTable);

function filterTable() {
    const monthFilter = document.getElementById('monthFilter').value;
    const yearFilter = document.getElementById('yearFilter').value;
    const deptFilter = document.getElementById('departmentFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        let showRow = true;

        // تصفية حسب الشهر والسنة
        if (monthFilter || yearFilter) {
            const dateCell = row.cells[1].textContent;
            if (monthFilter && !dateCell.includes(`-${monthFilter}-`)) {
                showRow = false;
            }
            if (yearFilter && !dateCell.includes(yearFilter)) {
                showRow = false;
            }
        }

        // تصفية حسب القسم
        if (deptFilter && showRow) {
            // هنا يمكن إضافة منطق التصفية حسب القسم
        }

        // تصفية حسب الحالة
        if (statusFilter && showRow) {
            const statusCell = row.cells[7].textContent.toLowerCase();
            const statusMap = {
                'draft': 'مسودة',
                'approved': 'معتمد',
                'paid': 'مدفوع'
            };
            if (!statusCell.includes(statusMap[statusFilter])) {
                showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    });
}
</script>
{% endblock %}
