{% extends "base.html" %}

{% block title %}الحضور والانصراف - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-clock me-2 text-primary"></i>
            الحضور والانصراف
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('hr.index') }}">الموارد البشرية</a></li>
                <li class="breadcrumb-item active">الحضور والانصراف</li>
            </ol>
        </nav>
    </div>
</div>

<!-- إحصائيات الحضور اليوم -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-2x text-success"></i>
                </div>
                <h3 class="text-success">{{ attendance|selectattr("status", "equalto", "present")|list|length }}</h3>
                <p class="text-muted mb-0">حاضر اليوم</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                </div>
                <h3 class="text-danger">{{ attendance|selectattr("status", "equalto", "absent")|list|length }}</h3>
                <p class="text-muted mb-0">غائب اليوم</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-clock fa-2x text-warning"></i>
                </div>
                <h3 class="text-warning">{{ attendance|selectattr("status", "equalto", "late")|list|length }}</h3>
                <p class="text-muted mb-0">متأخر اليوم</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-clock fa-2x text-info"></i>
                </div>
                <h3 class="text-info">{{ attendance|selectattr("status", "equalto", "half_day")|list|length }}</h3>
                <p class="text-muted mb-0">نصف يوم</p>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التحكم -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="attendanceDate" value="{{ today }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">القسم</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                            <option value="hr">الموارد البشرية</option>
                            <option value="finance">المالية</option>
                            <option value="it">تقنية المعلومات</option>
                            <option value="operations">العمليات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="present">حاضر</option>
                            <option value="absent">غائب</option>
                            <option value="late">متأخر</option>
                            <option value="half_day">نصف يوم</option>
                            <option value="sick_leave">إجازة مرضية</option>
                            <option value="vacation">إجازة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="markAttendance()">
                                <i class="fas fa-plus me-2"></i>
                                تسجيل حضور
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الحضور -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                سجل الحضور - {{ today }}
            </div>
            <div class="card-body">
                {% if attendance %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>وقت الحضور</th>
                                <th>وقت الانصراف</th>
                                <th>ساعات العمل</th>
                                <th>الساعات الإضافية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in attendance %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            {{ record.employee.first_name_ar[0] if record.employee.first_name_ar else 'م' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ record.employee.full_name_ar }}</div>
                                            <small class="text-muted">{{ record.employee.employee_number }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ record.employee.department or 'غير محدد' }}</span>
                                </td>
                                <td>
                                    {% if record.check_in %}
                                    <span class="text-success fw-bold">{{ record.check_in.strftime('%H:%M') }}</span>
                                    {% else %}
                                    <span class="text-muted">لم يسجل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.check_out %}
                                    <span class="text-danger fw-bold">{{ record.check_out.strftime('%H:%M') }}</span>
                                    {% else %}
                                    <span class="text-muted">لم يسجل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.regular_hours %}
                                    <span class="fw-bold">{{ "%.1f"|format(record.regular_hours) }} ساعة</span>
                                    {% else %}
                                    <span class="text-muted">0.0 ساعة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.overtime_hours and record.overtime_hours > 0 %}
                                    <span class="text-warning fw-bold">{{ "%.1f"|format(record.overtime_hours) }} ساعة</span>
                                    {% else %}
                                    <span class="text-muted">0.0 ساعة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.status == 'present' %}
                                    <span class="badge bg-success">حاضر</span>
                                    {% elif record.status == 'absent' %}
                                    <span class="badge bg-danger">غائب</span>
                                    {% elif record.status == 'late' %}
                                    <span class="badge bg-warning">متأخر</span>
                                    {% elif record.status == 'half_day' %}
                                    <span class="badge bg-info">نصف يوم</span>
                                    {% elif record.status == 'sick_leave' %}
                                    <span class="badge bg-secondary">إجازة مرضية</span>
                                    {% elif record.status == 'vacation' %}
                                    <span class="badge bg-primary">إجازة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-success" title="تسجيل حضور" onclick="checkIn({{ record.employee.id }})">
                                            <i class="fas fa-sign-in-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="تسجيل انصراف" onclick="checkOut({{ record.employee.id }})">
                                            <i class="fas fa-sign-out-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" title="تعديل" onclick="editAttendance({{ record.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-clock"></i>
                    <h5 class="text-muted">لا يوجد سجلات حضور لهذا التاريخ</h5>
                    <p class="text-muted">ابدأ بتسجيل حضور الموظفين</p>
                    <button class="btn btn-primary" onclick="markAttendance()">
                        <i class="fas fa-plus me-2"></i>
                        تسجيل حضور جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="markAllPresent()">
                            <i class="fas fa-check-double me-2"></i>
                            تسجيل حضور جماعي
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="generateReport()">
                            <i class="fas fa-file-alt me-2"></i>
                            تقرير الحضور
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="exportAttendance()">
                            <i class="fas fa-download me-2"></i>
                            تصدير البيانات
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="attendanceSettings()">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات الحضور
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتسجيل الحضور -->
<div class="modal fade" id="attendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل حضور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="attendanceForm">
                    <div class="mb-3">
                        <label class="form-label">الموظف</label>
                        <select class="form-select" id="employeeSelect" required>
                            <option value="">اختر الموظف</option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="modalDate" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">وقت الحضور</label>
                            <input type="time" class="form-control" id="checkInTime">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">وقت الانصراف</label>
                            <input type="time" class="form-control" id="checkOutTime">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="attendanceStatus" required>
                            <option value="present">حاضر</option>
                            <option value="absent">غائب</option>
                            <option value="late">متأخر</option>
                            <option value="half_day">نصف يوم</option>
                            <option value="sick_leave">إجازة مرضية</option>
                            <option value="vacation">إجازة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="attendanceNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveAttendance()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديد التاريخ الحالي
document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
document.getElementById('modalDate').value = new Date().toISOString().split('T')[0];

// وظائف الحضور
function markAttendance() {
    // ملء قائمة الموظفين
    loadEmployees();
    new bootstrap.Modal(document.getElementById('attendanceModal')).show();
}

function loadEmployees() {
    // هنا يمكن إضافة استدعاء AJAX لجلب قائمة الموظفين
    const select = document.getElementById('employeeSelect');
    select.innerHTML = '<option value="">اختر الموظف</option>';
    // إضافة موظفين تجريبيين
    const employees = [
        {id: 1, name: 'أحمد محمد الكعبي'},
        {id: 2, name: 'فاطمة علي النعيمي'},
        {id: 3, name: 'محمد سالم الثاني'}
    ];
    employees.forEach(emp => {
        const option = document.createElement('option');
        option.value = emp.id;
        option.textContent = emp.name;
        select.appendChild(option);
    });
}

function checkIn(employeeId) {
    fetch(`/hr/api/check_in/${employeeId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`تم تسجيل حضور الموظف في الساعة ${data.time}`);
            location.reload(); // إعادة تحميل الصفحة لإظهار التحديث
        } else {
            alert('حدث خطأ في تسجيل الحضور');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function checkOut(employeeId) {
    fetch(`/hr/api/check_out/${employeeId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`تم تسجيل انصراف الموظف في الساعة ${data.time}`);
            location.reload(); // إعادة تحميل الصفحة لإظهار التحديث
        } else {
            alert(data.message || 'حدث خطأ في تسجيل الانصراف');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function saveAttendance() {
    const form = document.getElementById('attendanceForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    if (!document.getElementById('employeeSelect').value) {
        alert('يرجى اختيار الموظف');
        return;
    }

    alert('تم حفظ بيانات الحضور بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('attendanceModal')).hide();
    // هنا يمكن إضافة استدعاء AJAX لحفظ البيانات
}

function markAllPresent() {
    if (confirm('هل تريد تسجيل حضور جميع الموظفين؟')) {
        alert('تم تسجيل حضور جميع الموظفين');
        // هنا يمكن إضافة استدعاء AJAX
    }
}

function generateReport() {
    alert('سيتم إنتاج تقرير الحضور');
    // هنا يمكن إضافة وظيفة إنتاج التقرير
}

function exportAttendance() {
    alert('سيتم تصدير بيانات الحضور');
    // هنا يمكن إضافة وظيفة التصدير
}

function attendanceSettings() {
    alert('إعدادات الحضور');
    // هنا يمكن إضافة صفحة الإعدادات
}

// تصفية الجدول
document.getElementById('departmentFilter').addEventListener('change', function() {
    filterTable();
});

document.getElementById('statusFilter').addEventListener('change', function() {
    filterTable();
});

function filterTable() {
    const deptFilter = document.getElementById('departmentFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        let showRow = true;

        if (deptFilter) {
            const deptCell = row.cells[1].textContent.toLowerCase();
            if (!deptCell.includes(deptFilter)) {
                showRow = false;
            }
        }

        if (statusFilter && showRow) {
            const statusCell = row.cells[6].textContent.toLowerCase();
            const statusMap = {
                'present': 'حاضر',
                'absent': 'غائب',
                'late': 'متأخر',
                'half_day': 'نصف',
                'sick_leave': 'مرضية',
                'vacation': 'إجازة'
            };
            if (!statusCell.includes(statusMap[statusFilter])) {
                showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    });
}
</script>
{% endblock %}
