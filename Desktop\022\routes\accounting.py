from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.accounting import Account, JournalEntry, Transaction
from database_setup import db

bp = Blueprint('accounting', __name__, url_prefix='/accounting')

@bp.route('/')
@login_required
def index():
    """صفحة المحاسبة الرئيسية"""
    if not current_user.has_permission('view_accounting'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    # إحصائيات سريعة
    stats = {
        'total_accounts': Account.query.filter_by(is_active=True).count(),
        'pending_entries': JournalEntry.query.filter_by(status='draft').count(),
        'posted_entries': JournalEntry.query.filter_by(status='posted').count()
    }

    return render_template('accounting/index.html', stats=stats)

@bp.route('/accounts')
@login_required
def accounts():
    """دليل الحسابات"""
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    return render_template('accounting/accounts.html', accounts=accounts)

@bp.route('/journal_entries')
@login_required
def journal_entries():
    """قيود اليومية"""
    entries = JournalEntry.query.order_by(JournalEntry.date.desc()).limit(50).all()
    return render_template('accounting/journal_entries.html', entries=entries)

@bp.route('/reports')
@login_required
def reports():
    """التقارير المالية"""
    return render_template('accounting/reports.html')
