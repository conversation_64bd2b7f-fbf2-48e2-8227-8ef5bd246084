from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from app import db

class SystemSetting(db.Model):
    """إعدادات النظام"""
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات الإعداد
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    default_value = db.Column(db.Text)
    
    # وصف الإعداد
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    description = db.Column(db.Text)
    
    # نوع الإعداد
    setting_type = db.Column(db.String(20), nullable=False)  # string, number, boolean, json, file
    category = db.Column(db.String(50), nullable=False)  # general, security, notifications, etc.
    
    # خصائص الإعداد
    is_required = db.Column(db.<PERSON>, default=False)
    is_system = db.Column(db.<PERSON>, default=False)  # إعدادات النظام الأساسية
    is_encrypted = db.Column(db.Boolean, default=False)
    
    # قيود الإعداد
    min_value = db.Column(db.Float)
    max_value = db.Column(db.Float)
    allowed_values = db.Column(db.Text)  # JSON array للقيم المسموحة
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # العلاقات
    updater = db.relationship('User', backref='updated_settings')
    
    def __repr__(self):
        return f'<SystemSetting {self.key}>'
    
    def get_value(self):
        """الحصول على قيمة الإعداد مع التحويل المناسب"""
        value = self.value if self.value is not None else self.default_value
        
        if value is None:
            return None
        
        if self.setting_type == 'boolean':
            return value.lower() in ['true', '1', 'yes', 'on']
        elif self.setting_type == 'number':
            try:
                return float(value) if '.' in value else int(value)
            except (ValueError, TypeError):
                return 0
        elif self.setting_type == 'json':
            try:
                import json
                return json.loads(value)
            except (ValueError, TypeError):
                return {}
        else:
            return value
    
    def set_value(self, new_value, user_id=None):
        """تعيين قيمة جديدة للإعداد"""
        if self.setting_type == 'json':
            import json
            self.value = json.dumps(new_value, ensure_ascii=False)
        else:
            self.value = str(new_value)
        
        self.updated_at = datetime.utcnow()
        if user_id:
            self.updated_by = user_id
        
        db.session.commit()

class UserPreference(db.Model):
    """تفضيلات المستخدم"""
    __tablename__ = 'user_preferences'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # معلومات التفضيل
    key = db.Column(db.String(100), nullable=False)
    value = db.Column(db.Text)
    
    # نوع التفضيل
    preference_type = db.Column(db.String(20), default='string')
    category = db.Column(db.String(50), default='general')
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    user = db.relationship('User', backref='preferences')
    
    # فهرس مركب لضمان عدم تكرار المفتاح لنفس المستخدم
    __table_args__ = (db.UniqueConstraint('user_id', 'key', name='unique_user_preference'),)
    
    def __repr__(self):
        return f'<UserPreference {self.key}>'
    
    def get_value(self):
        """الحصول على قيمة التفضيل مع التحويل المناسب"""
        if self.value is None:
            return None
        
        if self.preference_type == 'boolean':
            return self.value.lower() in ['true', '1', 'yes', 'on']
        elif self.preference_type == 'number':
            try:
                return float(self.value) if '.' in self.value else int(self.value)
            except (ValueError, TypeError):
                return 0
        elif self.preference_type == 'json':
            try:
                import json
                return json.loads(self.value)
            except (ValueError, TypeError):
                return {}
        else:
            return self.value

class ModuleConfiguration(db.Model):
    """إعدادات الوحدات"""
    __tablename__ = 'module_configurations'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات الوحدة
    module_name = db.Column(db.String(50), nullable=False)
    module_title = db.Column(db.String(100), nullable=False)
    
    # حالة الوحدة
    is_enabled = db.Column(db.Boolean, default=True)
    is_installed = db.Column(db.Boolean, default=True)
    
    # إعدادات الوحدة
    configuration = db.Column(db.Text)  # JSON configuration
    permissions = db.Column(db.Text)  # JSON permissions
    
    # معلومات الإصدار
    version = db.Column(db.String(20), default='1.0.0')
    min_system_version = db.Column(db.String(20))
    
    # تواريخ
    installed_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_used = db.Column(db.DateTime)
    
    def __repr__(self):
        return f'<ModuleConfiguration {self.module_name}>'
    
    def get_config(self):
        """الحصول على إعدادات الوحدة"""
        if self.configuration:
            try:
                import json
                return json.loads(self.configuration)
            except (ValueError, TypeError):
                return {}
        return {}
    
    def set_config(self, config_dict):
        """تعيين إعدادات الوحدة"""
        import json
        self.configuration = json.dumps(config_dict, ensure_ascii=False)
        self.updated_at = datetime.utcnow()
        db.session.commit()

class AuditLog(db.Model):
    """سجل التدقيق"""
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات الحدث
    action = db.Column(db.String(100), nullable=False)
    entity_type = db.Column(db.String(50), nullable=False)
    entity_id = db.Column(db.Integer)
    
    # تفاصيل التغيير
    old_values = db.Column(db.Text)  # JSON
    new_values = db.Column(db.Text)  # JSON
    changes_summary = db.Column(db.Text)
    
    # معلومات المستخدم
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    
    # معلومات إضافية
    severity = db.Column(db.String(10), default='info')  # info, warning, error, critical
    category = db.Column(db.String(50))
    
    # تاريخ الحدث
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    user = db.relationship('User', backref='audit_logs')
    
    def __repr__(self):
        return f'<AuditLog {self.action}>'
    
    @staticmethod
    def log_action(action, entity_type, entity_id=None, old_values=None, 
                   new_values=None, user_id=None, severity='info', category=None):
        """تسجيل حدث في سجل التدقيق"""
        import json
        from flask import request
        
        log_entry = AuditLog(
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=json.dumps(old_values, ensure_ascii=False) if old_values else None,
            new_values=json.dumps(new_values, ensure_ascii=False) if new_values else None,
            user_id=user_id,
            severity=severity,
            category=category,
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )
        
        db.session.add(log_entry)
        db.session.commit()
        
        return log_entry
