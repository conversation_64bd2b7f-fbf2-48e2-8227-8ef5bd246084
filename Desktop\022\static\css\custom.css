/* ألوان علم قطر */
:root {
    --qatar-primary: #8B1538;
    --qatar-secondary: #FFFFFF;
    --qatar-accent: #A91B47;
    --qatar-dark: #5D0E26;
    --qatar-light: #F8F9FA;
    --qatar-gradient: linear-gradient(135deg, var(--qatar-primary) 0%, var(--qatar-accent) 100%);
}

/* تحسينات عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--qatar-light);
    direction: rtl;
}

/* تحسينات الشريط العلوي */
.navbar {
    background: var(--qatar-gradient);
    box-shadow: 0 2px 10px rgba(139, 21, 56, 0.3);
    border-bottom: 3px solid var(--qatar-accent);
}

.navbar-brand {
    font-weight: 700;
    color: var(--qatar-secondary) !important;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: var(--qatar-secondary) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* تحسينات الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--qatar-primary) 0%, var(--qatar-dark) 100%);
    min-height: calc(100vh - 76px);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: var(--qatar-secondary);
    padding: 15px 20px;
    margin: 5px 10px;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
    border-left-color: var(--qatar-secondary);
}

.sidebar .nav-link.active {
    background-color: var(--qatar-accent);
    color: white;
    border-left-color: var(--qatar-secondary);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: var(--qatar-gradient);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border-bottom: none;
}

.stats-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-left: 5px solid var(--qatar-primary);
    transition: all 0.3s ease;
}

.stats-card:hover {
    border-left-width: 8px;
    transform: translateY(-3px);
}

/* تحسينات الأزرار */
.btn-primary {
    background: var(--qatar-gradient);
    border: none;
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--qatar-dark) 0%, var(--qatar-primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 21, 56, 0.3);
}

.btn-outline-primary {
    color: var(--qatar-primary);
    border-color: var(--qatar-primary);
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--qatar-primary);
    border-color: var(--qatar-primary);
    transform: translateY(-1px);
}

/* تحسينات الجداول */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.table thead th {
    background: var(--qatar-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(139, 21, 56, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.05);
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--qatar-primary);
    box-shadow: 0 0 0 0.2rem rgba(139, 21, 56, 0.25);
}

/* تحسينات التنبيهات */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 5px solid #28a745;
}

.alert-danger, .alert-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 5px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 5px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
    color: #004085;
    border-left: 5px solid #17a2b8;
}

/* تحسينات الشارات */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.85rem;
}

/* تحسينات الأفاتار */
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    font-weight: 600;
}

/* تحسينات التنقل */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: var(--qatar-primary);
    font-weight: bold;
}

.breadcrumb-item.active {
    color: var(--qatar-primary);
    font-weight: 600;
}

/* تحسينات القائمة المنسدلة */
.dropdown-menu {
    border-radius: 12px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(139, 21, 56, 0.1);
    color: var(--qatar-primary);
}

/* تحسينات التذييل */
.footer {
    background: var(--qatar-gradient);
    color: var(--qatar-secondary);
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: 3px solid var(--qatar-accent);
}

/* شريط علم قطر */
.qatar-flag {
    background: var(--qatar-gradient);
    height: 5px;
    width: 100%;
    position: relative;
}

.qatar-flag::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, var(--qatar-accent) 100%);
}

/* تحسينات الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.main-content > * {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        left: -100%;
        width: 280px;
        height: calc(100vh - 76px);
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        padding: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

/* تحسينات إضافية للنصوص العربية */
.arabic-text {
    font-family: 'Cairo', sans-serif;
    line-height: 1.8;
}

/* تحسينات الأيقونات */
.fas, .far, .fab {
    transition: all 0.3s ease;
}

.card-header .fas {
    margin-left: 0.5rem;
}

/* تحسينات المحتوى الفارغ */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تحسينات التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ألوان إضافية لأنواع الإجازات الجديدة */
.bg-purple {
    background-color: #6f42c1 !important;
    color: white !important;
}

.bg-teal {
    background-color: #20c997 !important;
    color: white !important;
}

.bg-orange {
    background-color: #fd7e14 !important;
    color: white !important;
}

.bg-indigo {
    background-color: #6610f2 !important;
    color: white !important;
}

.bg-pink {
    background-color: #e83e8c !important;
    color: white !important;
}

.bg-cyan {
    background-color: #0dcaf0 !important;
    color: white !important;
}

/* تحسينات خاصة بأنواع الإجازات */
.leave-type-badge {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحسينات لنموذج الإجازات */
.leave-form .form-label {
    font-weight: 600;
    color: var(--qatar-primary);
    margin-bottom: 0.5rem;
}

.leave-form .form-control:focus,
.leave-form .form-select:focus {
    border-color: var(--qatar-primary);
    box-shadow: 0 0 0 0.2rem rgba(139, 21, 56, 0.15);
}

/* تحسينات لجدول الإجازات */
.leave-table .badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
}

.leave-table .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
}

/* تحسينات للإحصائيات */
.leave-stats .stats-card {
    border-top: 4px solid var(--qatar-primary);
}

.leave-stats .stats-card:nth-child(2) {
    border-top-color: #28a745;
}

.leave-stats .stats-card:nth-child(3) {
    border-top-color: #dc3545;
}

.leave-stats .stats-card:nth-child(4) {
    border-top-color: #17a2b8;
}
