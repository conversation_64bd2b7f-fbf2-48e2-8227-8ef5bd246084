{% extends "base.html" %}

{% block title %}إدارة المشتريات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-shopping-bag me-2"></i>
                        إدارة المشتريات
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">المشتريات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="addNewPurchase()">
                        <i class="fas fa-plus me-2"></i>
                        طلب شراء جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-file-invoice fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ purchases|length }}</h4>
                    <p class="text-muted mb-0">إجمالي طلبات الشراء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ total_purchases|number_format }}</h4>
                    <p class="text-muted mb-0">إجمالي المشتريات (ر.ق)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ pending_purchases }}</h4>
                    <p class="text-muted mb-0">طلبات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-truck fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ suppliers_count }}</h4>
                    <p class="text-muted mb-0">الموردون</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="row mb-3">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchPurchases" placeholder="البحث في طلبات الشراء...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="draft">مسودة</option>
                <option value="pending">معلقة</option>
                <option value="approved">موافق عليها</option>
                <option value="ordered">مطلوبة</option>
                <option value="received">مستلمة</option>
                <option value="cancelled">ملغاة</option>
            </select>
        </div>
        <div class="col-md-2">
            <input type="date" class="form-control" id="dateFilter" placeholder="التاريخ">
        </div>
        <div class="col-md-4">
            <div class="btn-group w-100">
                <button class="btn btn-outline-danger" onclick="deleteSelectedPurchases()" id="deleteSelectedBtn" disabled>
                    <i class="fas fa-trash me-2"></i>
                    حذف المحدد
                </button>
                <button class="btn btn-outline-primary" onclick="exportPurchases()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- جدول المشتريات -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    طلبات الشراء
                </h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAllPurchases" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAllPurchases">
                        تحديد الكل
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if purchases %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllHeader" onchange="toggleSelectAll()">
                            </th>
                            <th>رقم الطلب</th>
                            <th>المورد</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>تاريخ التسليم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for purchase in purchases %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input purchase-checkbox" value="{{ purchase.id }}" onchange="updateDeleteButton()">
                            </td>
                            <td>
                                <strong>{{ purchase.order_number }}</strong>
                            </td>
                            <td>{{ purchase.supplier_name or 'غير محدد' }}</td>
                            <td>{{ purchase.order_date.strftime('%Y-%m-%d') if purchase.order_date else 'غير محدد' }}</td>
                            <td>{{ purchase.total_amount|number_format }} ر.ق</td>
                            <td>
                                {% if purchase.status == 'draft' %}
                                <span class="badge bg-secondary">مسودة</span>
                                {% elif purchase.status == 'pending' %}
                                <span class="badge bg-warning">معلقة</span>
                                {% elif purchase.status == 'approved' %}
                                <span class="badge bg-info">موافق عليها</span>
                                {% elif purchase.status == 'ordered' %}
                                <span class="badge bg-primary">مطلوبة</span>
                                {% elif purchase.status == 'received' %}
                                <span class="badge bg-success">مستلمة</span>
                                {% elif purchase.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغاة</span>
                                {% endif %}
                            </td>
                            <td>{{ purchase.delivery_date.strftime('%Y-%m-%d') if purchase.delivery_date else 'غير محدد' }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewPurchase({{ purchase.id }})" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="editPurchase({{ purchase.id }})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="printOrder({{ purchase.id }})" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deletePurchase({{ purchase.id }})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-shopping-bag fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات شراء</h5>
                <p class="text-muted">ابدأ بإنشاء طلب شراء جديد</p>
                <button class="btn btn-primary" onclick="addNewPurchase()">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء طلب شراء جديد
                </button>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportPurchases()">
                                <i class="fas fa-download me-2"></i>
                                تصدير المشتريات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="generatePurchaseReport()">
                                <i class="fas fa-chart-bar me-2"></i>
                                تقرير المشتريات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="manageSuppliers()">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="purchaseSettings()">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات المشتريات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة طلب شراء جديد -->
<div class="modal fade" id="addPurchaseModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء طلب شراء جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPurchaseForm">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">رقم الطلب *</label>
                            <input type="text" class="form-control" id="orderNumber" required readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الطلب *</label>
                            <input type="date" class="form-control" id="orderDate" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ التسليم المطلوب</label>
                            <input type="date" class="form-control" id="deliveryDate">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">المورد *</label>
                            <input type="text" class="form-control" id="supplierName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="purchaseStatus">
                                <option value="draft">مسودة</option>
                                <option value="pending">معلقة</option>
                                <option value="approved">موافق عليها</option>
                                <option value="ordered">مطلوبة</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول الأصناف -->
                    <div class="mt-4">
                        <h6>أصناف الطلب</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="itemsTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الوصف</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><input type="text" class="form-control" placeholder="اسم الصنف"></td>
                                        <td><input type="text" class="form-control" placeholder="وصف الصنف"></td>
                                        <td><input type="number" class="form-control quantity" min="1" value="1" onchange="calculateRowTotal(this)"></td>
                                        <td><input type="number" class="form-control price" step="0.01" onchange="calculateRowTotal(this)"></td>
                                        <td><span class="row-total">0.00</span> ر.ق</td>
                                        <td><button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">حذف</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <button type="button" class="btn btn-sm btn-success" onclick="addNewRow()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة صنف
                        </button>
                    </div>

                    <!-- الإجماليات -->
                    <div class="row mt-3">
                        <div class="col-md-8"></div>
                        <div class="col-md-4">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>الإجمالي:</strong></td>
                                    <td class="text-end"><strong><span id="totalAmount">0.00</span> ر.ق</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePurchase()">حفظ الطلب</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let selectedPurchases = [];

// وظائف إدارة المشتريات
function addNewPurchase() {
    // إنتاج رقم طلب تلقائي
    const orderNumber = 'PO' + new Date().getFullYear() + String(Date.now()).slice(-6);
    document.getElementById('orderNumber').value = orderNumber;

    // تحديد التاريخ الحالي
    document.getElementById('orderDate').value = new Date().toISOString().split('T')[0];

    // عرض النموذج
    new bootstrap.Modal(document.getElementById('addPurchaseModal')).show();
}

function savePurchase() {
    const form = document.getElementById('addPurchaseForm');

    // التحقق من الحقول المطلوبة
    if (!document.getElementById('orderNumber').value.trim()) {
        alert('يرجى إدخال رقم الطلب');
        return;
    }

    if (!document.getElementById('supplierName').value.trim()) {
        alert('يرجى إدخال اسم المورد');
        return;
    }

    // جمع بيانات الأصناف
    const items = [];
    const rows = document.querySelectorAll('#itemsTable tbody tr');

    rows.forEach(row => {
        const inputs = row.querySelectorAll('input');
        const itemName = inputs[0].value;
        const description = inputs[1].value;
        const quantity = inputs[2].value;
        const price = inputs[3].value;

        if (itemName && quantity && price) {
            items.push({
                name: itemName,
                description: description,
                quantity: parseFloat(quantity),
                price: parseFloat(price),
                total: parseFloat(quantity) * parseFloat(price)
            });
        }
    });

    if (items.length === 0) {
        alert('يرجى إضافة صنف واحد على الأقل');
        return;
    }

    // جمع البيانات
    const purchaseData = {
        order_number: document.getElementById('orderNumber').value,
        order_date: document.getElementById('orderDate').value,
        delivery_date: document.getElementById('deliveryDate').value,
        supplier_name: document.getElementById('supplierName').value,
        status: document.getElementById('purchaseStatus').value,
        notes: document.getElementById('notes').value,
        items: items,
        total_amount: parseFloat(document.getElementById('totalAmount').textContent)
    };

    // محاكاة حفظ البيانات
    console.log('بيانات طلب الشراء:', purchaseData);
    alert('تم حفظ طلب الشراء بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('addPurchaseModal')).hide();
    location.reload();
}

// وظائف جدول الأصناف
function addNewRow() {
    const tbody = document.querySelector('#itemsTable tbody');
    const newRow = tbody.rows[0].cloneNode(true);

    // مسح القيم
    newRow.querySelectorAll('input').forEach(input => {
        if (input.type === 'number' && input.classList.contains('quantity')) {
            input.value = '1';
        } else {
            input.value = '';
        }
    });
    newRow.querySelector('.row-total').textContent = '0.00';

    tbody.appendChild(newRow);
}

function removeRow(button) {
    const tbody = document.querySelector('#itemsTable tbody');
    if (tbody.rows.length > 1) {
        button.closest('tr').remove();
        calculateTotal();
    }
}

function calculateRowTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const price = parseFloat(row.querySelector('.price').value) || 0;
    const total = quantity * price;

    row.querySelector('.row-total').textContent = total.toFixed(2);
    calculateTotal();
}

function calculateTotal() {
    const rowTotals = document.querySelectorAll('.row-total');
    let total = 0;

    rowTotals.forEach(span => {
        total += parseFloat(span.textContent) || 0;
    });

    document.getElementById('totalAmount').textContent = total.toFixed(2);
}

// وظائف أخرى
function viewPurchase(purchaseId) {
    alert(`عرض تفاصيل طلب الشراء رقم ${purchaseId}`);
}

function editPurchase(purchaseId) {
    alert(`تعديل طلب الشراء رقم ${purchaseId}`);
}

function printOrder(purchaseId) {
    alert(`طباعة طلب الشراء رقم ${purchaseId}`);
}

function deletePurchase(purchaseId) {
    if (confirm('هل أنت متأكد من حذف طلب الشراء؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert(`تم حذف طلب الشراء رقم ${purchaseId}`);
        location.reload();
    }
}

// وظائف التحديد المتعدد
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllPurchases') || document.getElementById('selectAllHeader');
    const purchaseCheckboxes = document.querySelectorAll('.purchase-checkbox');

    purchaseCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.purchase-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    selectedPurchases = Array.from(checkedBoxes).map(cb => cb.value);

    if (selectedPurchases.length > 0) {
        deleteBtn.disabled = false;
        deleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>حذف المحدد (${selectedPurchases.length})`;
    } else {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-trash me-2"></i>حذف المحدد';
    }
}

function deleteSelectedPurchases() {
    if (selectedPurchases.length === 0) {
        alert('يرجى تحديد طلبات للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedPurchases.length} طلب شراء؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
        alert(`تم حذف ${selectedPurchases.length} طلب شراء بنجاح`);
        location.reload();
    }
}

// الإجراءات السريعة
function exportPurchases() {
    alert('تصدير طلبات الشراء');
}

function generatePurchaseReport() {
    alert('إنتاج تقرير المشتريات');
}

function manageSuppliers() {
    alert('إدارة الموردين');
}

function purchaseSettings() {
    alert('إعدادات إدارة المشتريات');
}

// تحديث أزرار الحذف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDeleteButton();
});
</script>
{% endblock %}
