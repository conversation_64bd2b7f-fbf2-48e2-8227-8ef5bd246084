from datetime import datetime, timedelta
from flask import current_app
from models.notifications import WorkflowDefinition, WorkflowStep, WorkflowInstance, WorkflowExecution
from models.user import User
from services.notification_service import WorkflowNotificationService
from app import db

class WorkflowService:
    """خدمة سير العمل"""
    
    @staticmethod
    def create_workflow_definition(name, description, workflow_type, steps_config):
        """إنشاء تعريف سير عمل جديد"""
        workflow = WorkflowDefinition(
            name=name,
            description=description,
            workflow_type=workflow_type
        )
        
        db.session.add(workflow)
        db.session.flush()  # للحصول على ID
        
        # إضافة الخطوات
        for i, step_config in enumerate(steps_config, 1):
            step = WorkflowStep(
                workflow_id=workflow.id,
                name=step_config['name'],
                description=step_config.get('description', ''),
                step_order=i,
                step_type=step_config['type'],
                assignee_type=step_config['assignee_type'],
                assignee_value=step_config['assignee_value'],
                is_required=step_config.get('is_required', True),
                timeout_hours=step_config.get('timeout_hours', 24)
            )
            db.session.add(step)
        
        db.session.commit()
        return workflow
    
    @staticmethod
    def start_workflow(workflow_type, entity_type, entity_id, title, description, initiated_by):
        """بدء سير عمل جديد"""
        # البحث عن تعريف سير العمل
        workflow_def = WorkflowDefinition.query.filter_by(
            workflow_type=workflow_type,
            is_active=True
        ).first()
        
        if not workflow_def:
            raise ValueError(f"تعريف سير العمل '{workflow_type}' غير موجود")
        
        # إنشاء مثيل سير العمل
        instance = WorkflowInstance(
            workflow_id=workflow_def.id,
            title=title,
            description=description,
            entity_type=entity_type,
            entity_id=entity_id,
            initiated_by=initiated_by,
            status='in_progress'
        )
        
        db.session.add(instance)
        db.session.flush()
        
        # بدء الخطوة الأولى
        first_step = WorkflowStep.query.filter_by(
            workflow_id=workflow_def.id,
            step_order=1
        ).first()
        
        if first_step:
            WorkflowService._assign_step(instance, first_step)
        
        db.session.commit()
        
        # إرسال إشعار ببدء سير العمل
        WorkflowNotificationService.notify_workflow_started(instance)
        
        return instance
    
    @staticmethod
    def _assign_step(instance, step):
        """تعيين خطوة في سير العمل"""
        # تحديد المنفذ حسب نوع التعيين
        assignee_users = WorkflowService._get_assignee_users(step)
        
        for user in assignee_users:
            execution = WorkflowExecution(
                instance_id=instance.id,
                step_id=step.id,
                executed_by=user.id,
                due_date=datetime.utcnow() + timedelta(hours=step.timeout_hours)
            )
            db.session.add(execution)
            
            # إرسال إشعار بالتعيين
            WorkflowNotificationService.notify_step_assigned(execution)
    
    @staticmethod
    def _get_assignee_users(step):
        """الحصول على المستخدمين المعينين لخطوة"""
        if step.assignee_type == 'user':
            # تعيين لمستخدم محدد
            user = User.query.filter_by(username=step.assignee_value).first()
            return [user] if user else []
        
        elif step.assignee_type == 'role':
            # تعيين لدور
            return User.query.filter_by(role=step.assignee_value, is_active=True).all()
        
        elif step.assignee_type == 'department':
            # تعيين لقسم
            return User.query.filter_by(department=step.assignee_value, is_active=True).all()
        
        return []
    
    @staticmethod
    def execute_step(execution_id, user_id, action, comments=None):
        """تنفيذ خطوة في سير العمل"""
        execution = WorkflowExecution.query.get(execution_id)
        
        if not execution:
            raise ValueError("تنفيذ الخطوة غير موجود")
        
        if execution.executed_by != user_id:
            raise ValueError("غير مصرح لك بتنفيذ هذه الخطوة")
        
        if execution.status != 'pending':
            raise ValueError("تم تنفيذ هذه الخطوة مسبقاً")
        
        # تحديث حالة التنفيذ
        execution.status = action  # approved, rejected, completed
        execution.comments = comments
        execution.executed_at = datetime.utcnow()
        
        # تحديث حالة سير العمل
        instance = execution.instance
        
        if action == 'approved':
            # الانتقال للخطوة التالية
            next_step = WorkflowStep.query.filter_by(
                workflow_id=instance.workflow_id,
                step_order=instance.current_step + 1
            ).first()
            
            if next_step:
                instance.current_step += 1
                WorkflowService._assign_step(instance, next_step)
            else:
                # إكمال سير العمل
                instance.status = 'completed'
                instance.completed_at = datetime.utcnow()
                WorkflowNotificationService.notify_workflow_completed(instance)
        
        elif action == 'rejected':
            # رفض سير العمل
            instance.status = 'cancelled'
            instance.completed_at = datetime.utcnow()
            WorkflowNotificationService.notify_workflow_rejected(instance, user_id)
        
        db.session.commit()
        return execution
    
    @staticmethod
    def get_user_tasks(user_id, status='pending'):
        """الحصول على مهام المستخدم في سير العمل"""
        return WorkflowExecution.query.filter_by(
            executed_by=user_id,
            status=status
        ).join(WorkflowInstance).filter(
            WorkflowInstance.status == 'in_progress'
        ).order_by(WorkflowExecution.due_date.asc()).all()
    
    @staticmethod
    def get_workflow_status(instance_id):
        """الحصول على حالة سير العمل"""
        instance = WorkflowInstance.query.get(instance_id)
        
        if not instance:
            return None
        
        executions = WorkflowExecution.query.filter_by(
            instance_id=instance_id
        ).join(WorkflowStep).order_by(WorkflowStep.step_order).all()
        
        return {
            'instance': instance,
            'executions': executions,
            'current_step': instance.current_step,
            'total_steps': len(instance.workflow.steps),
            'progress_percentage': (instance.current_step / len(instance.workflow.steps)) * 100
        }
    
    @staticmethod
    def cancel_workflow(instance_id, user_id, reason=None):
        """إلغاء سير العمل"""
        instance = WorkflowInstance.query.get(instance_id)
        
        if not instance:
            raise ValueError("سير العمل غير موجود")
        
        if instance.initiated_by != user_id:
            # فحص الصلاحيات - يمكن للمدير إلغاء أي سير عمل
            user = User.query.get(user_id)
            if not user or user.role != 'admin':
                raise ValueError("غير مصرح لك بإلغاء سير العمل")
        
        if instance.status not in ['pending', 'in_progress']:
            raise ValueError("لا يمكن إلغاء سير العمل في هذه الحالة")
        
        instance.status = 'cancelled'
        instance.completed_at = datetime.utcnow()
        
        # إلغاء جميع المهام المعلقة
        pending_executions = WorkflowExecution.query.filter_by(
            instance_id=instance_id,
            status='pending'
        ).all()
        
        for execution in pending_executions:
            execution.status = 'cancelled'
            execution.comments = reason or 'تم إلغاء سير العمل'
            execution.executed_at = datetime.utcnow()
        
        db.session.commit()
        return instance

class LeaveRequestWorkflow:
    """سير عمل طلبات الإجازات"""
    
    @staticmethod
    def setup_default_workflow():
        """إعداد سير العمل الافتراضي لطلبات الإجازات"""
        steps_config = [
            {
                'name': 'مراجعة المشرف المباشر',
                'description': 'مراجعة وموافقة المشرف المباشر',
                'type': 'approval',
                'assignee_type': 'role',
                'assignee_value': 'manager',
                'timeout_hours': 24
            },
            {
                'name': 'مراجعة الموارد البشرية',
                'description': 'مراجعة قسم الموارد البشرية',
                'type': 'approval',
                'assignee_type': 'role',
                'assignee_value': 'hr',
                'timeout_hours': 48
            },
            {
                'name': 'الموافقة النهائية',
                'description': 'الموافقة النهائية من الإدارة',
                'type': 'approval',
                'assignee_type': 'role',
                'assignee_value': 'admin',
                'timeout_hours': 72
            }
        ]
        
        return WorkflowService.create_workflow_definition(
            name='سير عمل طلبات الإجازات',
            description='سير العمل الافتراضي لمعالجة طلبات الإجازات',
            workflow_type='leave_request',
            steps_config=steps_config
        )
    
    @staticmethod
    def start_leave_request_workflow(leave_request):
        """بدء سير عمل طلب إجازة"""
        return WorkflowService.start_workflow(
            workflow_type='leave_request',
            entity_type='leave_request',
            entity_id=leave_request.id,
            title=f'طلب إجازة - {leave_request.employee.full_name_ar}',
            description=f'طلب إجازة {leave_request.leave_type} من {leave_request.start_date} إلى {leave_request.end_date}',
            initiated_by=leave_request.employee.user_account.id if leave_request.employee.user_account else 1
        )
