from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required, current_user
from datetime import datetime
from models.notifications import Notification, WorkflowInstance, WorkflowExecution
from models.settings import SystemSetting, UserPreference, AuditLog
from services.notification_service import NotificationService
from services.workflow_service import WorkflowService
from app import db

bp = Blueprint('api', __name__, url_prefix='/api')

# ===== APIs الإشعارات =====

@bp.route('/notifications')
@login_required
def get_notifications():
    """الحصول على إشعارات المستخدم"""
    try:
        notifications = NotificationService.get_user_notifications(
            user_id=current_user.id,
            unread_only=request.args.get('unread_only', 'false').lower() == 'true',
            limit=int(request.args.get('limit', 50))
        )
        
        return jsonify([notification.to_dict() for notification in notifications])
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب الإشعارات: {str(e)}")
        return jsonify({'error': 'خطأ في جلب الإشعارات'}), 500

@bp.route('/notifications/count')
@login_required
def get_notifications_count():
    """الحصول على عدد الإشعارات غير المقروءة"""
    try:
        count = NotificationService.get_unread_count(current_user.id)
        return jsonify({'count': count})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في عد الإشعارات: {str(e)}")
        return jsonify({'error': 'خطأ في عد الإشعارات'}), 500

@bp.route('/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    try:
        success = NotificationService.mark_as_read(notification_id, current_user.id)
        
        if success:
            return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})
        else:
            return jsonify({'error': 'الإشعار غير موجود'}), 404
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديد الإشعار كمقروء: {str(e)}")
        return jsonify({'error': 'خطأ في تحديد الإشعار كمقروء'}), 500

@bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """تحديد جميع الإشعارات كمقروءة"""
    try:
        count = NotificationService.mark_all_as_read(current_user.id)
        return jsonify({'success': True, 'message': f'تم تحديد {count} إشعار كمقروء'})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديد جميع الإشعارات كمقروءة: {str(e)}")
        return jsonify({'error': 'خطأ في تحديد الإشعارات كمقروءة'}), 500

@bp.route('/notifications', methods=['POST'])
@login_required
def create_notification():
    """إنشاء إشعار جديد (للمديرين فقط)"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإنشاء إشعارات'}), 403
    
    try:
        data = request.get_json()
        
        notification = NotificationService.create_notification(
            user_id=data['user_id'],
            title=data['title'],
            message=data['message'],
            notification_type=data.get('type', 'info'),
            priority=data.get('priority', 'normal'),
            action_url=data.get('action_url'),
            action_text=data.get('action_text'),
            icon=data.get('icon'),
            source_type=data.get('source_type'),
            source_id=data.get('source_id')
        )
        
        return jsonify(notification.to_dict()), 201
    
    except Exception as e:
        current_app.logger.error(f"خطأ في إنشاء الإشعار: {str(e)}")
        return jsonify({'error': 'خطأ في إنشاء الإشعار'}), 500

# ===== APIs سير العمل =====

@bp.route('/workflow/tasks')
@login_required
def get_workflow_tasks():
    """الحصول على مهام سير العمل للمستخدم"""
    try:
        tasks = WorkflowService.get_user_tasks(
            user_id=current_user.id,
            status=request.args.get('status', 'pending')
        )
        
        tasks_data = []
        for task in tasks:
            task_data = {
                'id': task.id,
                'title': task.step.name,
                'description': task.instance.description,
                'workflow_title': task.instance.title,
                'due_date': task.due_date.strftime('%Y-%m-%d') if task.due_date else None,
                'priority': 'high' if task.step.is_required else 'normal',
                'status': task.status,
                'assigned_at': task.assigned_at.isoformat() if task.assigned_at else None,
                'workflow_id': task.instance_id
            }
            tasks_data.append(task_data)
        
        return jsonify(tasks_data)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب مهام سير العمل: {str(e)}")
        return jsonify({'error': 'خطأ في جلب مهام سير العمل'}), 500

@bp.route('/workflow/task/<int:task_id>/execute', methods=['POST'])
@login_required
def execute_workflow_task(task_id):
    """تنفيذ مهمة في سير العمل"""
    try:
        data = request.get_json()
        action = data.get('action')  # approved, rejected, completed
        comments = data.get('comments', '')
        
        if action not in ['approved', 'rejected', 'completed']:
            return jsonify({'error': 'إجراء غير صحيح'}), 400
        
        execution = WorkflowService.execute_step(task_id, current_user.id, action, comments)
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action=f'workflow_task_{action}',
            entity_type='workflow_execution',
            entity_id=task_id,
            new_values={'action': action, 'comments': comments},
            user_id=current_user.id,
            category='workflow'
        )
        
        return jsonify({
            'success': True,
            'message': f'تم {action} المهمة بنجاح',
            'status': execution.status
        })
    
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        current_app.logger.error(f"خطأ في تنفيذ مهمة سير العمل: {str(e)}")
        return jsonify({'error': 'خطأ في تنفيذ المهمة'}), 500

@bp.route('/workflow/instance/<int:instance_id>/status')
@login_required
def get_workflow_status(instance_id):
    """الحصول على حالة سير العمل"""
    try:
        status = WorkflowService.get_workflow_status(instance_id)
        
        if not status:
            return jsonify({'error': 'سير العمل غير موجود'}), 404
        
        return jsonify({
            'instance_id': status['instance'].id,
            'title': status['instance'].title,
            'status': status['instance'].status,
            'current_step': status['current_step'],
            'total_steps': status['total_steps'],
            'progress_percentage': status['progress_percentage'],
            'started_at': status['instance'].started_at.isoformat(),
            'completed_at': status['instance'].completed_at.isoformat() if status['instance'].completed_at else None
        })
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب حالة سير العمل: {str(e)}")
        return jsonify({'error': 'خطأ في جلب حالة سير العمل'}), 500

# ===== APIs الإعدادات =====

@bp.route('/settings')
@login_required
def get_settings():
    """الحصول على إعدادات النظام"""
    try:
        category = request.args.get('category', 'general')
        
        settings = SystemSetting.query.filter_by(category=category).all()
        
        settings_data = []
        for setting in settings:
            # فحص الصلاحيات - الإعدادات الحساسة للمديرين فقط
            if setting.is_system and current_user.role != 'admin':
                continue
            
            setting_data = {
                'key': setting.key,
                'value': setting.get_value(),
                'name_ar': setting.name_ar,
                'name_en': setting.name_en,
                'description': setting.description,
                'setting_type': setting.setting_type,
                'category': setting.category,
                'is_required': setting.is_required,
                'is_system': setting.is_system
            }
            settings_data.append(setting_data)
        
        return jsonify(settings_data)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب الإعدادات: {str(e)}")
        return jsonify({'error': 'خطأ في جلب الإعدادات'}), 500

@bp.route('/settings/<setting_key>', methods=['PUT'])
@login_required
def update_setting(setting_key):
    """تحديث إعداد"""
    try:
        setting = SystemSetting.query.filter_by(key=setting_key).first()
        
        if not setting:
            return jsonify({'error': 'الإعداد غير موجود'}), 404
        
        # فحص الصلاحيات
        if setting.is_system and current_user.role != 'admin':
            return jsonify({'error': 'غير مصرح لك بتعديل هذا الإعداد'}), 403
        
        data = request.get_json()
        new_value = data.get('value')
        
        # حفظ القيمة القديمة للتدقيق
        old_value = setting.get_value()
        
        setting.set_value(new_value, current_user.id)
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='setting_updated',
            entity_type='system_setting',
            entity_id=setting.id,
            old_values={'value': old_value},
            new_values={'value': new_value},
            user_id=current_user.id,
            category='settings'
        )
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث الإعداد بنجاح',
            'value': setting.get_value()
        })
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديث الإعداد: {str(e)}")
        return jsonify({'error': 'خطأ في تحديث الإعداد'}), 500

@bp.route('/user/preferences')
@login_required
def get_user_preferences():
    """الحصول على تفضيلات المستخدم"""
    try:
        preferences = UserPreference.query.filter_by(user_id=current_user.id).all()
        
        preferences_data = {}
        for pref in preferences:
            preferences_data[pref.key] = pref.get_value()
        
        return jsonify(preferences_data)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب تفضيلات المستخدم: {str(e)}")
        return jsonify({'error': 'خطأ في جلب التفضيلات'}), 500

@bp.route('/user/preferences', methods=['PUT'])
@login_required
def update_user_preferences():
    """تحديث تفضيلات المستخدم"""
    try:
        data = request.get_json()
        
        for key, value in data.items():
            preference = UserPreference.query.filter_by(
                user_id=current_user.id,
                key=key
            ).first()
            
            if preference:
                preference.value = str(value)
                preference.updated_at = datetime.utcnow()
            else:
                preference = UserPreference(
                    user_id=current_user.id,
                    key=key,
                    value=str(value)
                )
                db.session.add(preference)
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم تحديث التفضيلات بنجاح'})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديث تفضيلات المستخدم: {str(e)}")
        return jsonify({'error': 'خطأ في تحديث التفضيلات'}), 500

# ===== APIs عامة =====

@bp.route('/system/status')
@login_required
def get_system_status():
    """الحصول على حالة النظام"""
    try:
        # إحصائيات أساسية
        stats = {
            'notifications_count': Notification.query.filter_by(user_id=current_user.id, is_read=False).count(),
            'workflow_tasks_count': WorkflowExecution.query.filter_by(executed_by=current_user.id, status='pending').count(),
            'system_uptime': '99.9%',  # يمكن حسابها من سجلات النظام
            'last_backup': '2024-12-19 10:00:00',  # من إعدادات النظام
            'database_size': '2.5 MB',  # يمكن حسابها
            'active_users': 15  # عدد المستخدمين النشطين
        }
        
        return jsonify(stats)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب حالة النظام: {str(e)}")
        return jsonify({'error': 'خطأ في جلب حالة النظام'}), 500
