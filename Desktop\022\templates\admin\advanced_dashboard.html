{% extends "base.html" %}

{% block title %}لوحة الإدارة المتقدمة - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-tachometer-alt text-primary me-2"></i>
                    لوحة الإدارة المتقدمة
                </h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                    <button class="btn btn-primary" onclick="sendAnnouncement()">
                        <i class="fas fa-bullhorn me-2"></i>إرسال إعلان
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-start border-primary border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">إجمالي المستخدمين</h6>
                            <h3 class="text-primary mb-0">{{ stats.total_users }}</h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                +{{ weekly_stats.new_users }} هذا الأسبوع
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-start border-warning border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">الإشعارات النشطة</h6>
                            <h3 class="text-warning mb-0">{{ stats.unread_notifications }}</h3>
                            <small class="text-info">
                                <i class="fas fa-bell me-1"></i>
                                {{ stats.total_notifications }} إجمالي
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x text-warning opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-start border-success border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">سير العمل النشط</h6>
                            <h3 class="text-success mb-0">{{ stats.active_workflows }}</h3>
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>
                                {{ stats.completed_workflows }} مكتمل
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-sitemap fa-2x text-success opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card border-start border-info border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">الوحدات المفعلة</h6>
                            <h3 class="text-info mb-0">{{ stats.enabled_modules }}</h3>
                            <small class="text-muted">
                                <i class="fas fa-puzzle-piece me-1"></i>
                                من أصل 10 وحدات
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-puzzle-piece fa-2x text-info opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحذيرات النظام -->
    {% if warnings %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        تحذيرات النظام
                    </h5>
                </div>
                <div class="card-body">
                    {% for warning in warnings %}
                    <div class="alert alert-{{ warning.type }} d-flex align-items-center" role="alert">
                        <i class="{{ warning.icon }} me-3"></i>
                        <div>{{ warning.message }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- حالة الوحدات -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-puzzle-piece me-2"></i>
                        حالة الوحدات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="modules-list">
                        {% for module in modules %}
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 rounded bg-light">
                            <div class="d-flex align-items-center">
                                <div class="module-status me-3">
                                    {% if module.is_enabled %}
                                    <i class="fas fa-check-circle text-success fa-lg"></i>
                                    {% else %}
                                    <i class="fas fa-times-circle text-danger fa-lg"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ module.module_title }}</h6>
                                    <small class="text-muted">{{ module.module_name }}</small>
                                </div>
                            </div>
                            <div class="module-actions">
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="toggleModule('{{ module.module_name }}')">
                                    {% if module.is_enabled %}إيقاف{% else %}تفعيل{% endif %}
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- آخر الأنشطة -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="activity-list">
                        {% for activity in recent_activities %}
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="activity-icon me-3">
                                <i class="fas fa-circle text-{{ 'success' if activity.severity == 'info' else 'warning' if activity.severity == 'warning' else 'danger' }} fa-sm"></i>
                            </div>
                            <div class="activity-content flex-grow-1">
                                <h6 class="mb-1">{{ activity.action }}</h6>
                                <p class="mb-1 text-muted small">{{ activity.entity_type }}</p>
                                <small class="text-muted">
                                    {{ activity.timestamp.strftime('%Y-%m-%d %H:%M') }}
                                    {% if activity.user %}
                                    - {{ activity.user.full_name }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('admin_advanced.logs') }}" class="btn btn-sm btn-outline-primary">
                            عرض جميع السجلات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-primary w-100" onclick="createBackup()">
                                <i class="fas fa-download me-2"></i>
                                إنشاء نسخة احتياطية
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-success w-100" onclick="systemCleanup()">
                                <i class="fas fa-broom me-2"></i>
                                تنظيف النظام
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin_advanced.users') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-users me-2"></i>
                                إدارة المستخدمين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات النظام
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إرسال الإعلان -->
<div class="modal fade" id="announcementModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال إعلان عام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="announcementForm">
                    <div class="mb-3">
                        <label for="announcementTitle" class="form-label">عنوان الإعلان</label>
                        <input type="text" class="form-control" id="announcementTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="announcementMessage" class="form-label">نص الإعلان</label>
                        <textarea class="form-control" id="announcementMessage" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="announcementType" class="form-label">نوع الإعلان</label>
                        <select class="form-select" id="announcementType">
                            <option value="info">معلومات</option>
                            <option value="success">نجاح</option>
                            <option value="warning">تحذير</option>
                            <option value="error">خطأ</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="announcementPriority" class="form-label">الأولوية</label>
                        <select class="form-select" id="announcementPriority">
                            <option value="normal">عادية</option>
                            <option value="high">عالية</option>
                            <option value="urgent">عاجلة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitAnnouncement()">إرسال الإعلان</button>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.activity-item {
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.modules-list .rounded:hover {
    background-color: #e9ecef !important;
    transition: background-color 0.3s ease;
}
</style>

<script>
// تحديث لوحة الإدارة
function refreshDashboard() {
    location.reload();
}

// إرسال إعلان
function sendAnnouncement() {
    const modal = new bootstrap.Modal(document.getElementById('announcementModal'));
    modal.show();
}

// إرسال الإعلان
async function submitAnnouncement() {
    const title = document.getElementById('announcementTitle').value;
    const message = document.getElementById('announcementMessage').value;
    const type = document.getElementById('announcementType').value;
    const priority = document.getElementById('announcementPriority').value;

    if (!title || !message) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    try {
        const response = await fetch('/admin/api/send-announcement', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: title,
                message: message,
                type: type,
                priority: priority
            })
        });

        if (response.ok) {
            const result = await response.json();
            alert(`تم إرسال الإعلان بنجاح لـ ${result.recipients_count} مستخدم`);

            // إغلاق المودال وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('announcementModal')).hide();
            document.getElementById('announcementForm').reset();
        } else {
            alert('خطأ في إرسال الإعلان');
        }
    } catch (error) {
        console.error('خطأ في إرسال الإعلان:', error);
        alert('خطأ في إرسال الإعلان');
    }
}

// تبديل حالة الوحدة
async function toggleModule(moduleName) {
    if (!confirm(`هل أنت متأكد من تبديل حالة وحدة ${moduleName}؟`)) {
        return;
    }

    try {
        const response = await fetch(`/settings/api/module/${moduleName}/toggle`, {
            method: 'POST'
        });

        if (response.ok) {
            location.reload();
        } else {
            alert('خطأ في تبديل حالة الوحدة');
        }
    } catch (error) {
        console.error('خطأ في تبديل حالة الوحدة:', error);
        alert('خطأ في تبديل حالة الوحدة');
    }
}

// إنشاء نسخة احتياطية
async function createBackup() {
    if (!confirm('هل تريد إنشاء نسخة احتياطية الآن؟')) {
        return;
    }

    try {
        const response = await fetch('/admin/api/backup/create', {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            alert(`تم إنشاء النسخة الاحتياطية بنجاح: ${result.filename}`);
        } else {
            alert('خطأ في إنشاء النسخة الاحتياطية');
        }
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        alert('خطأ في إنشاء النسخة الاحتياطية');
    }
}

// تنظيف النظام
async function systemCleanup() {
    if (!confirm('هل أنت متأكد من تنظيف النظام؟ سيتم حذف البيانات القديمة.')) {
        return;
    }

    try {
        const response = await fetch('/admin/api/system/cleanup', {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            alert(`تم تنظيف النظام بنجاح!\n\nالإشعارات المحذوفة: ${result.stats.old_notifications}\nالسجلات المحذوفة: ${result.stats.old_logs}\nالملفات المؤقتة: ${result.stats.temp_files}`);
        } else {
            alert('خطأ في تنظيف النظام');
        }
    } catch (error) {
        console.error('خطأ في تنظيف النظام:', error);
        alert('خطأ في تنظيف النظام');
    }
}
</script>
{% endblock %}
