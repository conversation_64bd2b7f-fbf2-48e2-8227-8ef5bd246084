{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-cog text-primary me-2"></i>
                    إعدادات النظام
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الإعدادات</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الإعدادات العامة -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 settings-card">
                <div class="card-body text-center">
                    <div class="settings-icon mb-3">
                        <i class="fas fa-sliders-h fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">الإعدادات العامة</h5>
                    <p class="card-text text-muted">
                        إعدادات النظام الأساسية والتفضيلات العامة
                    </p>
                    <a href="{{ url_for('settings.general') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 settings-card">
                <div class="card-body text-center">
                    <div class="settings-icon mb-3">
                        <i class="fas fa-bell fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">إعدادات الإشعارات</h5>
                    <p class="card-text text-muted">
                        إدارة الإشعارات وقوالب الرسائل والتنبيهات
                    </p>
                    <a href="{{ url_for('settings.notifications') }}" class="btn btn-warning">
                        <i class="fas fa-arrow-left me-2"></i>
                        إدارة الإشعارات
                    </a>
                </div>
            </div>
        </div>

        <!-- إعدادات الأمان -->
        {% if current_user.role == 'admin' %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 settings-card">
                <div class="card-body text-center">
                    <div class="settings-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">إعدادات الأمان</h5>
                    <p class="card-text text-muted">
                        إعدادات الأمان وسجلات التدقيق والصلاحيات
                    </p>
                    <a href="{{ url_for('settings.security') }}" class="btn btn-danger">
                        <i class="fas fa-arrow-left me-2"></i>
                        إدارة الأمان
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- إدارة الوحدات -->
        {% if current_user.role == 'admin' %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 settings-card">
                <div class="card-body text-center">
                    <div class="settings-icon mb-3">
                        <i class="fas fa-puzzle-piece fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">إدارة الوحدات</h5>
                    <p class="card-text text-muted">
                        تفعيل وإيقاف وحدات النظام وإعداداتها
                    </p>
                    <a href="{{ url_for('settings.modules') }}" class="btn btn-success">
                        <i class="fas fa-arrow-left me-2"></i>
                        إدارة الوحدات
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- التفضيلات الشخصية -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 settings-card">
                <div class="card-body text-center">
                    <div class="settings-icon mb-3">
                        <i class="fas fa-user-cog fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">التفضيلات الشخصية</h5>
                    <p class="card-text text-muted">
                        إعدادات المستخدم الشخصية والتفضيلات
                    </p>
                    <a href="{{ url_for('settings.preferences') }}" class="btn btn-info">
                        <i class="fas fa-arrow-left me-2"></i>
                        إدارة التفضيلات
                    </a>
                </div>
            </div>
        </div>

        <!-- النسخ الاحتياطي -->
        {% if current_user.role == 'admin' %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 settings-card">
                <div class="card-body text-center">
                    <div class="settings-icon mb-3">
                        <i class="fas fa-download fa-3x text-secondary"></i>
                    </div>
                    <h5 class="card-title">النسخ الاحتياطي</h5>
                    <p class="card-text text-muted">
                        إدارة النسخ الاحتياطية واستعادة البيانات
                    </p>
                    <a href="{{ url_for('settings.backup') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        إدارة النسخ
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4 class="mb-1" id="totalUsers">-</h4>
                                <small class="text-muted">إجمالي المستخدمين</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                                <h4 class="mb-1" id="totalNotifications">-</h4>
                                <small class="text-muted">إجمالي الإشعارات</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <i class="fas fa-cog fa-2x text-success mb-2"></i>
                                <h4 class="mb-1" id="totalSettings">-</h4>
                                <small class="text-muted">إعدادات النظام</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <i class="fas fa-puzzle-piece fa-2x text-info mb-2"></i>
                                <h4 class="mb-1" id="enabledModules">-</h4>
                                <small class="text-muted">الوحدات المفعلة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-primary w-100" onclick="testNotification()">
                                <i class="fas fa-bell me-2"></i>
                                اختبار إشعار
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-success w-100" onclick="refreshStats()">
                                <i class="fas fa-sync me-2"></i>
                                تحديث الإحصائيات
                            </button>
                        </div>
                        {% if current_user.role == 'admin' %}
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-warning w-100" onclick="systemCleanup()">
                                <i class="fas fa-broom me-2"></i>
                                تنظيف النظام
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
}

.settings-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.settings-icon {
    transition: transform 0.3s ease;
}

.settings-card:hover .settings-icon {
    transform: scale(1.1);
}

.stat-item {
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
</style>

<script>
// تحميل الإحصائيات
async function loadStats() {
    try {
        const response = await fetch('/api/system/status');
        if (response.ok) {
            const stats = await response.json();
            
            document.getElementById('totalUsers').textContent = stats.active_users || 0;
            document.getElementById('totalNotifications').textContent = stats.notifications_count || 0;
            document.getElementById('totalSettings').textContent = '25'; // يمكن جلبها من API
            document.getElementById('enabledModules').textContent = '8'; // يمكن جلبها من API
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// اختبار إشعار
async function testNotification() {
    try {
        const response = await fetch('/settings/api/test-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: 'إشعار تجريبي',
                message: 'هذا إشعار تجريبي لاختبار النظام',
                type: 'info'
            })
        });
        
        if (response.ok) {
            alert('تم إرسال الإشعار التجريبي بنجاح!');
            // تحديث الإشعارات في الشريط العلوي
            if (typeof loadNotifications === 'function') {
                loadNotifications();
            }
        } else {
            alert('خطأ في إرسال الإشعار التجريبي');
        }
    } catch (error) {
        console.error('خطأ في اختبار الإشعار:', error);
        alert('خطأ في اختبار الإشعار');
    }
}

// تحديث الإحصائيات
function refreshStats() {
    loadStats();
    alert('تم تحديث الإحصائيات');
}

// تنظيف النظام
async function systemCleanup() {
    if (!confirm('هل أنت متأكد من تنظيف النظام؟ سيتم حذف البيانات القديمة.')) {
        return;
    }
    
    try {
        const response = await fetch('/admin/api/system/cleanup', {
            method: 'POST'
        });
        
        if (response.ok) {
            const result = await response.json();
            alert(`تم تنظيف النظام بنجاح!\n\nالإشعارات المحذوفة: ${result.stats.old_notifications}\nالسجلات المحذوفة: ${result.stats.old_logs}\nالملفات المؤقتة: ${result.stats.temp_files}`);
        } else {
            alert('خطأ في تنظيف النظام');
        }
    } catch (error) {
        console.error('خطأ في تنظيف النظام:', error);
        alert('خطأ في تنظيف النظام');
    }
}

// تحميل الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
});
</script>
{% endblock %}
