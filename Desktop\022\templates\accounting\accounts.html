{% extends "base.html" %}

{% block title %}دليل الحسابات - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-list me-2 text-primary"></i>
            دليل الحسابات
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('accounting.index') }}">المحاسبة</a></li>
                <li class="breadcrumb-item active">دليل الحسابات</li>
            </ol>
        </nav>
    </div>
</div>

<!-- أدوات البحث والتصفية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="البحث في الحسابات..." id="searchInput">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="typeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="asset">الأصول</option>
                            <option value="liability">الخصوم</option>
                            <option value="equity">حقوق الملكية</option>
                            <option value="revenue">الإيرادات</option>
                            <option value="expense">المصروفات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="levelFilter">
                            <option value="">جميع المستويات</option>
                            <option value="1">المستوى الأول</option>
                            <option value="2">المستوى الثاني</option>
                            <option value="3">المستوى الثالث</option>
                            <option value="4">المستوى الرابع</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة حساب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الحسابات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                دليل الحسابات ({{ accounts|length if accounts else 0 }} حساب)
            </div>
            <div class="card-body">
                {% if accounts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>كود الحساب</th>
                                <th>اسم الحساب</th>
                                <th>نوع الحساب</th>
                                <th>المستوى</th>
                                <th>الرصيد الافتتاحي</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ account.code }}</strong>
                                </td>
                                <td>
                                    <div style="padding-right: {{ (account.level - 1) * 20 }}px;">
                                        {% if account.level > 1 %}
                                        <i class="fas fa-level-up-alt text-muted me-2" style="transform: rotate(90deg);"></i>
                                        {% endif %}
                                        <strong>{{ account.name_ar }}</strong>
                                        {% if account.name_en %}
                                        <br><small class="text-muted">{{ account.name_en }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if account.account_type == 'asset' %}
                                    <span class="badge bg-primary">أصول</span>
                                    {% elif account.account_type == 'liability' %}
                                    <span class="badge bg-danger">خصوم</span>
                                    {% elif account.account_type == 'equity' %}
                                    <span class="badge bg-info">حقوق ملكية</span>
                                    {% elif account.account_type == 'revenue' %}
                                    <span class="badge bg-success">إيرادات</span>
                                    {% elif account.account_type == 'expense' %}
                                    <span class="badge bg-warning">مصروفات</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ account.level }}</span>
                                </td>
                                <td>
                                    {% if account.opening_balance %}
                                    {{ "%.2f"|format(account.opening_balance) }} ر.ق
                                    {% else %}
                                    <span class="text-muted">0.00 ر.ق</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.current_balance %}
                                    <span class="fw-bold {% if account.current_balance > 0 %}text-success{% elif account.current_balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {{ "%.2f"|format(account.current_balance) }} ر.ق
                                    </span>
                                    {% else %}
                                    <span class="text-muted">0.00 ر.ق</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                    {% if account.is_system %}
                                    <span class="badge bg-info">نظام</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if not account.is_system %}
                                        <button class="btn btn-outline-success" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-outline-info" title="كشف الحساب">
                                            <i class="fas fa-file-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" title="الحركات">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-list"></i>
                    <h5 class="text-muted">لا يوجد حسابات مسجلة</h5>
                    <p class="text-muted">ابدأ بإنشاء دليل الحسابات</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة حساب جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if accounts %}
<div class="row mt-4">
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ accounts|selectattr("account_type", "equalto", "asset")|list|length }}</h5>
                <small class="text-muted">الأصول</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-danger">{{ accounts|selectattr("account_type", "equalto", "liability")|list|length }}</h5>
                <small class="text-muted">الخصوم</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ accounts|selectattr("account_type", "equalto", "equity")|list|length }}</h5>
                <small class="text-muted">حقوق الملكية</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ accounts|selectattr("account_type", "equalto", "revenue")|list|length }}</h5>
                <small class="text-muted">الإيرادات</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ accounts|selectattr("account_type", "equalto", "expense")|list|length }}</h5>
                <small class="text-muted">المصروفات</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-secondary">{{ accounts|selectattr("is_system", "equalto", true)|list|length }}</h5>
                <small class="text-muted">حسابات النظام</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// البحث في الجدول
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// تصفية حسب النوع
document.getElementById('typeFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        if (!filterValue) {
            row.style.display = '';
        } else {
            const typeCell = row.cells[2].textContent.toLowerCase();
            let showRow = false;
            
            if (filterValue === 'asset' && typeCell.includes('أصول')) {
                showRow = true;
            } else if (filterValue === 'liability' && typeCell.includes('خصوم')) {
                showRow = true;
            } else if (filterValue === 'equity' && typeCell.includes('ملكية')) {
                showRow = true;
            } else if (filterValue === 'revenue' && typeCell.includes('إيرادات')) {
                showRow = true;
            } else if (filterValue === 'expense' && typeCell.includes('مصروفات')) {
                showRow = true;
            }
            
            row.style.display = showRow ? '' : 'none';
        }
    });
});

// تصفية حسب المستوى
document.getElementById('levelFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        if (!filterValue) {
            row.style.display = '';
        } else {
            const levelCell = row.cells[3].textContent.trim();
            row.style.display = levelCell === filterValue ? '' : 'none';
        }
    });
});
</script>
{% endblock %}
