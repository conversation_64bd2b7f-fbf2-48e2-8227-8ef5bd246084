#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموارد - دولة قطر
Qatar ERP System

ملف التشغيل الرئيسي للنظام
"""

import os
import sys
from app import app, db, create_tables

def setup_system():
    """إعداد النظام للتشغيل لأول مرة"""
    print("🇶🇦 مرحباً بك في نظام إدارة الموارد - دولة قطر")
    print("=" * 50)
    
    # إنشاء المجلدات المطلوبة
    directories = ['database', 'static/uploads', 'static/css', 'static/js']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")
    
    # إنشاء قاعدة البيانات والجداول
    with app.app_context():
        try:
            create_tables()
            print("✅ تم إنشاء قاعدة البيانات والجداول بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    print("\n🎉 تم إعداد النظام بنجاح!")
    print("\n📋 بيانات تسجيل الدخول الافتراضية:")
    print("   المدير العام:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n🌐 رابط النظام: http://localhost:5000")
    print("=" * 50)
    
    return True

def run_system():
    """تشغيل النظام"""
    try:
        print("\n🚀 جاري تشغيل النظام...")
        print("📍 العنوان: http://localhost:5000")
        print("⏹️  للإيقاف: اضغط Ctrl+C")
        print("-" * 30)
        
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف النظام بنجاح")
        print("شكراً لاستخدام نظام إدارة الموارد - دولة قطر 🇶🇦")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")

def main():
    """الدالة الرئيسية"""
    # التحقق من وجود قاعدة البيانات
    db_exists = os.path.exists('erp_system.db')
    
    if not db_exists:
        print("🔧 إعداد النظام لأول مرة...")
        if not setup_system():
            sys.exit(1)
    
    # تشغيل النظام
    run_system()

if __name__ == '__main__':
    main()
