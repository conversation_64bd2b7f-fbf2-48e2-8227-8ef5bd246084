from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app import db

bp = Blueprint('admin_advanced', __name__, url_prefix='/admin')

def admin_required(f):
    """ديكوريتر للتأكد من صلاحيات المدير"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def dashboard():
    """لوحة الإدارة الرئيسية"""
    try:
        # إحصائيات عامة (محاكاة)
        stats = {
            'total_users': 15,
            'active_users': 12,
            'total_notifications': 45,
            'unread_notifications': 8,
            'active_workflows': 3,
            'completed_workflows': 12,
            'system_settings': 25,
            'enabled_modules': 8
        }
        
        # إحصائيات الأسبوع الماضي (محاكاة)
        weekly_stats = {
            'new_users': 2,
            'new_notifications': 15,
            'completed_workflows': 5,
            'audit_logs': 120
        }
        
        # آخر الأنشطة (محاكاة)
        recent_activities = []
        
        # حالة الوحدات (محاكاة)
        modules = [
            {'module_name': 'accounting', 'module_title': 'المحاسبة', 'is_enabled': True},
            {'module_name': 'hr', 'module_title': 'الموارد البشرية', 'is_enabled': True},
            {'module_name': 'inventory', 'module_title': 'المخزون', 'is_enabled': True},
            {'module_name': 'sales', 'module_title': 'المبيعات', 'is_enabled': True},
            {'module_name': 'procurement', 'module_title': 'المشتريات', 'is_enabled': True},
            {'module_name': 'projects', 'module_title': 'المشاريع', 'is_enabled': True},
            {'module_name': 'crm', 'module_title': 'إدارة العملاء', 'is_enabled': True},
            {'module_name': 'notifications', 'module_title': 'الإشعارات', 'is_enabled': True}
        ]
        
        # تحذيرات النظام (محاكاة)
        warnings = [
            {
                'type': 'info',
                'message': 'مساحة قاعدة البيانات: 2.5 MB (طبيعية)',
                'icon': 'fas fa-database'
            },
            {
                'type': 'warning',
                'message': 'آخر نسخة احتياطية: منذ 3 أيام',
                'icon': 'fas fa-exclamation-triangle'
            }
        ]
        
        return render_template('admin/advanced_dashboard.html',
                             stats=stats,
                             weekly_stats=weekly_stats,
                             recent_activities=recent_activities,
                             modules=modules,
                             warnings=warnings)
    
    except Exception as e:
        flash(f'خطأ في تحميل لوحة الإدارة: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/users')
@login_required
@admin_required
def users():
    """إدارة المستخدمين"""
    # محاكاة بيانات المستخدمين
    users = {
        'items': [],
        'pages': 1,
        'page': 1,
        'per_page': 20,
        'total': 0
    }
    
    # إحصائيات المستخدمين (محاكاة)
    user_stats = {
        'total': 15,
        'active': 12,
        'inactive': 3,
        'admins': 2,
        'managers': 3,
        'employees': 10
    }
    
    return render_template('admin/users.html',
                         users=users,
                         user_stats=user_stats,
                         search='',
                         role_filter='',
                         status_filter='')

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """سجلات النظام"""
    # محاكاة سجلات النظام
    logs = {
        'items': [],
        'pages': 1,
        'page': 1,
        'per_page': 50,
        'total': 0
    }
    
    # إحصائيات السجلات (محاكاة)
    log_stats = {
        'total': 1250,
        'today': 45,
        'this_week': 320,
        'critical': 2,
        'errors': 8,
        'warnings': 25
    }
    
    # فئات السجلات (محاكاة)
    categories = ['user_management', 'settings', 'modules', 'notifications', 'workflow']
    
    return render_template('admin/logs.html',
                         logs=logs,
                         log_stats=log_stats,
                         categories=categories,
                         category_filter='',
                         severity_filter='',
                         date_from='',
                         date_to='')

@bp.route('/system')
@login_required
@admin_required
def system():
    """معلومات النظام"""
    # معلومات النظام (محاكاة)
    system_info = {
        'version': '2.1.0',
        'database_size': '2.5 MB',
        'uptime': '15 يوم، 8 ساعات',
        'last_backup': '2024-12-19 10:00:00',
        'python_version': '3.9.0',
        'flask_version': '2.3.0',
        'database_type': 'SQLite',
        'server_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
    }
    
    # إحصائيات الأداء (محاكاة)
    performance_stats = {
        'avg_response_time': '250ms',
        'requests_per_minute': 45,
        'error_rate': '0.1%',
        'memory_usage': '128 MB',
        'cpu_usage': '15%',
        'disk_usage': '45%'
    }
    
    # حالة الخدمات (محاكاة)
    services_status = {
        'database': {'status': 'online', 'response_time': '5ms'},
        'file_system': {'status': 'online', 'response_time': '2ms'},
        'notifications': {'status': 'online', 'response_time': '15ms'},
        'workflow': {'status': 'online', 'response_time': '12ms'},
        'backup': {'status': 'warning', 'response_time': '3 days ago'}
    }
    
    return render_template('admin/system.html',
                         system_info=system_info,
                         performance_stats=performance_stats,
                         services_status=services_status)

@bp.route('/backup')
@login_required
@admin_required
def backup():
    """إدارة النسخ الاحتياطي"""
    # قائمة النسخ الاحتياطية (محاكاة)
    backups = [
        {
            'id': 1,
            'filename': 'backup_2024_12_19_10_00.db',
            'size': '2.5 MB',
            'created_at': '2024-12-19 10:00:00',
            'type': 'automatic',
            'status': 'completed'
        },
        {
            'id': 2,
            'filename': 'backup_2024_12_18_10_00.db',
            'size': '2.4 MB',
            'created_at': '2024-12-18 10:00:00',
            'type': 'automatic',
            'status': 'completed'
        }
    ]
    
    return render_template('admin/backup.html',
                         backups=backups,
                         backup_settings=[])

# ===== APIs الإدارة =====

@bp.route('/api/user/<int:user_id>/toggle', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """تفعيل/إيقاف مستخدم"""
    try:
        return jsonify({
            'success': True,
            'message': 'تم تبديل حالة المستخدم بنجاح',
            'is_active': True
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تبديل حالة المستخدم: {str(e)}'}), 500

@bp.route('/api/backup/create', methods=['POST'])
@login_required
@admin_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        backup_filename = f"backup_{datetime.utcnow().strftime('%Y_%m_%d_%H_%M')}.db"
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
            'filename': backup_filename
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'}), 500

@bp.route('/api/system/cleanup', methods=['POST'])
@login_required
@admin_required
def system_cleanup():
    """تنظيف النظام"""
    try:
        cleanup_stats = {
            'old_notifications': 15,
            'old_logs': 25,
            'temp_files': 8
        }
        
        return jsonify({
            'success': True,
            'message': 'تم تنظيف النظام بنجاح',
            'stats': cleanup_stats
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تنظيف النظام: {str(e)}'}), 500

@bp.route('/api/send-announcement', methods=['POST'])
@login_required
@admin_required
def send_announcement():
    """إرسال إعلان لجميع المستخدمين"""
    try:
        data = request.get_json()
        title = data.get('title')
        message = data.get('message')
        
        if not title or not message:
            return jsonify({'error': 'العنوان والرسالة مطلوبان'}), 400
        
        # محاكاة إرسال الإعلان
        recipients_count = 12  # عدد المستخدمين النشطين
        
        return jsonify({
            'success': True,
            'message': f'تم إرسال الإعلان لـ {recipients_count} مستخدم',
            'recipients_count': recipients_count
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إرسال الإعلان: {str(e)}'}), 500
