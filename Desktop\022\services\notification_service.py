from datetime import datetime, timedelta
from flask import current_app
from models.notifications import Notification, NotificationTemplate
from models.settings import SystemSetting
from app import db

class NotificationService:
    """خدمة الإشعارات"""
    
    @staticmethod
    def create_notification(user_id, title, message, notification_type='info', 
                          priority='normal', action_url=None, action_text=None,
                          icon=None, source_type=None, source_id=None, expires_in_days=30):
        """إنشاء إشعار جديد"""
        
        # تحديد الأيقونة حسب النوع
        if not icon:
            icon_map = {
                'info': 'fas fa-info-circle',
                'success': 'fas fa-check-circle',
                'warning': 'fas fa-exclamation-triangle',
                'error': 'fas fa-times-circle',
                'system': 'fas fa-cog'
            }
            icon = icon_map.get(notification_type, 'fas fa-bell')
        
        # حساب تاريخ انتهاء الصلاحية
        expires_at = datetime.utcnow() + timedelta(days=expires_in_days) if expires_in_days else None
        
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            type=notification_type,
            priority=priority,
            action_url=action_url,
            action_text=action_text,
            icon=icon,
            source_type=source_type,
            source_id=source_id,
            expires_at=expires_at
        )
        
        db.session.add(notification)
        db.session.commit()
        
        return notification
    
    @staticmethod
    def create_from_template(template_name, user_id, context=None):
        """إنشاء إشعار من قالب"""
        template = NotificationTemplate.query.filter_by(name=template_name, is_active=True).first()
        
        if not template:
            raise ValueError(f"قالب الإشعار '{template_name}' غير موجود")
        
        # تطبيق السياق على القالب
        title = template.title_template
        message = template.message_template
        
        if context:
            try:
                title = title.format(**context)
                message = message.format(**context)
            except KeyError as e:
                current_app.logger.warning(f"مفتاح مفقود في سياق الإشعار: {e}")
        
        return NotificationService.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=template.type,
            priority=template.priority,
            icon=template.icon
        )
    
    @staticmethod
    def send_to_multiple_users(user_ids, title, message, **kwargs):
        """إرسال إشعار لعدة مستخدمين"""
        notifications = []
        
        for user_id in user_ids:
            notification = NotificationService.create_notification(
                user_id=user_id,
                title=title,
                message=message,
                **kwargs
            )
            notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def send_to_role(role, title, message, **kwargs):
        """إرسال إشعار لجميع المستخدمين في دور معين"""
        from models.user import User
        
        users = User.query.filter_by(role=role, is_active=True).all()
        user_ids = [user.id for user in users]
        
        return NotificationService.send_to_multiple_users(user_ids, title, message, **kwargs)
    
    @staticmethod
    def send_to_department(department, title, message, **kwargs):
        """إرسال إشعار لجميع المستخدمين في قسم معين"""
        from models.user import User
        
        users = User.query.filter_by(department=department, is_active=True).all()
        user_ids = [user.id for user in users]
        
        return NotificationService.send_to_multiple_users(user_ids, title, message, **kwargs)
    
    @staticmethod
    def get_user_notifications(user_id, unread_only=False, limit=50):
        """الحصول على إشعارات المستخدم"""
        query = Notification.query.filter_by(user_id=user_id, is_archived=False)
        
        if unread_only:
            query = query.filter_by(is_read=False)
        
        # فلترة الإشعارات المنتهية الصلاحية
        query = query.filter(
            db.or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.utcnow()
            )
        )
        
        return query.order_by(Notification.created_at.desc()).limit(limit).all()
    
    @staticmethod
    def get_unread_count(user_id):
        """الحصول على عدد الإشعارات غير المقروءة"""
        return Notification.query.filter_by(
            user_id=user_id,
            is_read=False,
            is_archived=False
        ).filter(
            db.or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.utcnow()
            )
        ).count()
    
    @staticmethod
    def mark_as_read(notification_id, user_id):
        """تحديد إشعار كمقروء"""
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=user_id
        ).first()
        
        if notification:
            notification.mark_as_read()
            return True
        
        return False
    
    @staticmethod
    def mark_all_as_read(user_id):
        """تحديد جميع الإشعارات كمقروءة"""
        notifications = Notification.query.filter_by(
            user_id=user_id,
            is_read=False
        ).all()
        
        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.utcnow()
        
        db.session.commit()
        return len(notifications)
    
    @staticmethod
    def archive_notification(notification_id, user_id):
        """أرشفة إشعار"""
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=user_id
        ).first()
        
        if notification:
            notification.is_archived = True
            db.session.commit()
            return True
        
        return False
    
    @staticmethod
    def cleanup_old_notifications(days=90):
        """تنظيف الإشعارات القديمة"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # حذف الإشعارات القديمة المقروءة
        old_notifications = Notification.query.filter(
            Notification.created_at < cutoff_date,
            Notification.is_read == True
        ).all()
        
        for notification in old_notifications:
            db.session.delete(notification)
        
        # حذف الإشعارات المنتهية الصلاحية
        expired_notifications = Notification.query.filter(
            Notification.expires_at < datetime.utcnow()
        ).all()
        
        for notification in expired_notifications:
            db.session.delete(notification)
        
        db.session.commit()
        
        return len(old_notifications) + len(expired_notifications)

class WorkflowNotificationService:
    """خدمة إشعارات سير العمل"""
    
    @staticmethod
    def notify_workflow_started(workflow_instance):
        """إشعار ببدء سير العمل"""
        NotificationService.create_notification(
            user_id=workflow_instance.initiated_by,
            title="تم بدء سير العمل",
            message=f"تم بدء سير العمل: {workflow_instance.title}",
            notification_type="info",
            icon="fas fa-play-circle",
            source_type="workflow",
            source_id=workflow_instance.id
        )
    
    @staticmethod
    def notify_step_assigned(workflow_execution):
        """إشعار بتعيين خطوة في سير العمل"""
        if workflow_execution.executor:
            NotificationService.create_notification(
                user_id=workflow_execution.executor.id,
                title="مهمة جديدة في سير العمل",
                message=f"تم تعيين مهمة لك: {workflow_execution.step.name}",
                notification_type="warning",
                priority="high",
                icon="fas fa-tasks",
                action_url=f"/workflow/task/{workflow_execution.id}",
                action_text="عرض المهمة",
                source_type="workflow",
                source_id=workflow_execution.instance_id
            )
    
    @staticmethod
    def notify_workflow_completed(workflow_instance):
        """إشعار بإكمال سير العمل"""
        NotificationService.create_notification(
            user_id=workflow_instance.initiated_by,
            title="تم إكمال سير العمل",
            message=f"تم إكمال سير العمل بنجاح: {workflow_instance.title}",
            notification_type="success",
            icon="fas fa-check-circle",
            source_type="workflow",
            source_id=workflow_instance.id
        )
    
    @staticmethod
    def notify_workflow_rejected(workflow_instance, rejected_by):
        """إشعار برفض سير العمل"""
        NotificationService.create_notification(
            user_id=workflow_instance.initiated_by,
            title="تم رفض سير العمل",
            message=f"تم رفض سير العمل: {workflow_instance.title}",
            notification_type="error",
            icon="fas fa-times-circle",
            source_type="workflow",
            source_id=workflow_instance.id
        )
