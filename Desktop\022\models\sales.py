from datetime import datetime
from decimal import Decimal
from database_setup import db

class Customer(db.Model):
    __tablename__ = 'customers'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))

    # نوع العميل
    customer_type = db.Column(db.String(20), default='individual')  # individual, company, government

    # معلومات الاتصال
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    country = db.Column(db.String(50), default='Qatar')

    # معلومات تجارية
    tax_number = db.Column(db.String(50))
    commercial_register = db.Column(db.String(50))
    credit_limit = db.Column(db.Numeric(12, 2), default=0)
    payment_terms = db.Column(db.Integer, default=30)  # أيام الدفع

    # حالة العميل
    is_active = db.Column(db.Boolean, default=True)

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    sales_orders = db.relationship('SalesOrder', backref='customer', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='customer', lazy='dynamic')

    def __repr__(self):
        return f'<Customer {self.code} - {self.name_ar}>'

    def get_balance(self):
        """حساب رصيد العميل"""
        total_invoices = self.invoices.filter_by(status='posted').with_entities(
            db.func.sum(Invoice.total_amount)).scalar() or 0
        total_payments = self.invoices.filter_by(status='paid').with_entities(
            db.func.sum(Invoice.total_amount)).scalar() or 0
        return total_invoices - total_payments

class SalesOrder(db.Model):
    __tablename__ = 'sales_orders'

    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)

    # تواريخ الطلب
    order_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    delivery_date = db.Column(db.Date)

    # حالة الطلب
    status = db.Column(db.String(20), default='draft')  # draft, confirmed, delivered, cancelled

    # المبالغ
    subtotal = db.Column(db.Numeric(12, 2), default=0)
    tax_amount = db.Column(db.Numeric(12, 2), default=0)
    discount_amount = db.Column(db.Numeric(12, 2), default=0)
    total_amount = db.Column(db.Numeric(12, 2), default=0)

    # معلومات إضافية
    notes = db.Column(db.Text)
    terms_conditions = db.Column(db.Text)

    # المستخدم والتواريخ
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    order_items = db.relationship('SalesOrderItem', backref='sales_order', cascade='all, delete-orphan')
    creator = db.relationship('User')

    def __repr__(self):
        return f'<SalesOrder {self.order_number}>'

    def calculate_totals(self):
        """حساب إجماليات الطلب"""
        self.subtotal = sum(item.total_price for item in self.order_items)
        self.tax_amount = self.subtotal * Decimal('0.05')  # ضريبة 5%
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount

class SalesOrderItem(db.Model):
    __tablename__ = 'sales_order_items'

    id = db.Column(db.Integer, primary_key=True)
    sales_order_id = db.Column(db.Integer, db.ForeignKey('sales_orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)

    # تفاصيل الصنف
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    discount_percent = db.Column(db.Numeric(5, 2), default=0)
    total_price = db.Column(db.Numeric(12, 2))

    # معلومات إضافية
    description = db.Column(db.Text)

    # العلاقات
    product = db.relationship('Product')

    def __repr__(self):
        return f'<SalesOrderItem {self.product_id} - {self.quantity}>'

    def calculate_total(self):
        """حساب إجمالي الصنف"""
        discount_amount = (self.unit_price * self.quantity) * (self.discount_percent / 100)
        self.total_price = (self.unit_price * self.quantity) - discount_amount

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    sales_order_id = db.Column(db.Integer, db.ForeignKey('sales_orders.id'))

    # تواريخ الفاتورة
    invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    due_date = db.Column(db.Date)

    # حالة الفاتورة
    status = db.Column(db.String(20), default='draft')  # draft, posted, paid, cancelled

    # المبالغ
    subtotal = db.Column(db.Numeric(12, 2), default=0)
    tax_amount = db.Column(db.Numeric(12, 2), default=0)
    discount_amount = db.Column(db.Numeric(12, 2), default=0)
    total_amount = db.Column(db.Numeric(12, 2), default=0)
    paid_amount = db.Column(db.Numeric(12, 2), default=0)

    # معلومات الدفع
    payment_method = db.Column(db.String(20))  # cash, bank_transfer, check, card
    payment_reference = db.Column(db.String(50))

    # معلومات إضافية
    notes = db.Column(db.Text)

    # المستخدم والتواريخ
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    invoice_items = db.relationship('InvoiceItem', backref='invoice', cascade='all, delete-orphan')
    sales_order = db.relationship('SalesOrder')
    creator = db.relationship('User')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

    @property
    def balance(self):
        """الرصيد المتبقي"""
        return self.total_amount - self.paid_amount

    def is_overdue(self):
        """فحص إذا كانت الفاتورة متأخرة"""
        if self.due_date and self.status != 'paid':
            return datetime.utcnow().date() > self.due_date
        return False

class InvoiceItem(db.Model):
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)

    # تفاصيل الصنف
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    discount_percent = db.Column(db.Numeric(5, 2), default=0)
    total_price = db.Column(db.Numeric(12, 2))

    # معلومات إضافية
    description = db.Column(db.Text)

    # العلاقات
    product = db.relationship('Product')

    def __repr__(self):
        return f'<InvoiceItem {self.product_id} - {self.quantity}>'
