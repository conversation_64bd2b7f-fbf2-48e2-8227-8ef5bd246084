<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الموارد - دولة قطر{% endblock %}</title>

    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">

    <style>
        :root {
            --qatar-primary: #8B1538;
            --qatar-secondary: #FFFFFF;
            --qatar-accent: #A91B47;
            --qatar-dark: #5D0E26;
            --qatar-light: #F8F9FA;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--qatar-light);
            direction: rtl;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--qatar-secondary) !important;
        }

        .navbar {
            background: linear-gradient(135deg, var(--qatar-primary) 0%, var(--qatar-accent) 100%);
            box-shadow: 0 2px 10px rgba(139, 21, 56, 0.3);
        }

        .navbar-nav .nav-link {
            color: var(--qatar-secondary) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--qatar-light) !important;
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: var(--qatar-primary);
            border-color: var(--qatar-primary);
        }

        .btn-primary:hover {
            background-color: var(--qatar-dark);
            border-color: var(--qatar-dark);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--qatar-primary) 0%, var(--qatar-accent) 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }

        .sidebar {
            background: linear-gradient(180deg, var(--qatar-primary) 0%, var(--qatar-dark) 100%);
            min-height: calc(100vh - 76px);
        }

        .sidebar .nav-link {
            color: var(--qatar-secondary);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }

        .sidebar .nav-link.active {
            background-color: var(--qatar-accent);
            color: white;
        }

        .main-content {
            padding: 30px;
        }

        .stats-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-left: 5px solid var(--qatar-primary);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }

        .alert-error, .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .alert-info {
            background-color: #cce7ff;
            color: #004085;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .table thead th {
            background-color: var(--qatar-primary);
            color: white;
            border: none;
            font-weight: 600;
        }

        .footer {
            background-color: var(--qatar-dark);
            color: var(--qatar-secondary);
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }

        .qatar-flag {
            background: linear-gradient(90deg, var(--qatar-primary) 0%, var(--qatar-primary) 100%);
            height: 5px;
            width: 100%;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Qatar Flag Strip -->
    <div class="qatar-flag"></div>

    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building me-2"></i>
                نظام إدارة الموارد - دولة قطر
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <!-- الإشعارات -->
                    <li class="nav-item dropdown me-3">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" id="notificationsDropdown">
                            <i class="fas fa-bell fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount">
                                0
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                            <div class="dropdown-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">الإشعارات</h6>
                                <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                                    تحديد الكل كمقروء
                                </button>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div id="notificationsList">
                                <div class="text-center p-3 text-muted">
                                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                                    <p class="mb-0">لا توجد إشعارات جديدة</p>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div class="dropdown-footer text-center">
                                <a href="{{ url_for('notifications.index') }}" class="btn btn-sm btn-primary">عرض جميع الإشعارات</a>
                            </div>
                        </div>
                    </li>

                    <!-- سير العمل -->
                    <li class="nav-item dropdown me-3">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" id="workflowDropdown">
                            <i class="fas fa-tasks fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning" id="workflowCount">
                                0
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <div class="dropdown-header">
                                <h6 class="mb-0">المهام المعلقة</h6>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div id="workflowList">
                                <div class="text-center p-3 text-muted">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <p class="mb-0">لا توجد مهام معلقة</p>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div class="dropdown-footer text-center">
                                <a href="/workflow/tasks" class="btn btn-sm btn-warning">عرض جميع المهام</a>
                            </div>
                        </div>
                    </li>

                    <!-- الإعدادات السريعة -->
                    <li class="nav-item dropdown me-3">
                        <a class="nav-link" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog fs-5"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/settings/general">
                                <i class="fas fa-sliders-h me-2"></i>الإعدادات العامة
                            </a></li>
                            <li><a class="dropdown-item" href="/settings/notifications">
                                <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                            </a></li>
                            <li><a class="dropdown-item" href="/settings/security">
                                <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/backup">
                                <i class="fas fa-download me-2"></i>النسخ الاحتياطي
                            </a></li>
                        </ul>
                    </li>

                    <!-- ملف المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-circle me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </a></li>
                            <li><a class="dropdown-item" href="/settings/preferences">
                                <i class="fas fa-cog me-2"></i>التفضيلات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <div class="container-fluid">
        {% if current_user.is_authenticated %}
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <nav class="nav flex-column pt-3">
                        {% set dashboard_data = current_user.get_dashboard_data() %}

                        {% if 'accounting' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('accounting.index') }}">
                            <i class="fas fa-calculator me-2"></i>
                            المحاسبة
                        </a>
                        {% endif %}

                        {% if 'hr' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('hr.index') }}">
                            <i class="fas fa-users me-2"></i>
                            الموارد البشرية
                        </a>

                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                إدارة الرواتب
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('payroll.index') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('payroll.employees') }}">
                                    <i class="fas fa-users me-2"></i>إدارة رواتب الموظفين
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('payroll.payslips') }}">
                                    <i class="fas fa-file-invoice me-2"></i>قوائم الرواتب الشهرية
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('payroll.reports') }}">
                                    <i class="fas fa-chart-bar me-2"></i>تقارير الرواتب
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('payroll.settings') }}">
                                    <i class="fas fa-cog me-2"></i>إعدادات الرواتب
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}

                        {% if 'inventory' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('inventory.index') }}">
                            <i class="fas fa-boxes me-2"></i>
                            المخزون
                        </a>
                        {% endif %}

                        {% if 'sales' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('sales.index') }}">
                            <i class="fas fa-shopping-cart me-2"></i>
                            المبيعات
                        </a>
                        {% endif %}

                        {% if 'procurement' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('procurement.index') }}">
                            <i class="fas fa-truck me-2"></i>
                            المشتريات
                        </a>
                        {% endif %}

                        {% if 'projects' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('projects.index') }}">
                            <i class="fas fa-project-diagram me-2"></i>
                            المشاريع
                        </a>
                        {% endif %}

                        {% if 'crm' in dashboard_data.modules %}
                        <a class="nav-link" href="{{ url_for('crm.index') }}">
                            <i class="fas fa-handshake me-2"></i>
                            إدارة العملاء
                        </a>
                        {% endif %}

                        <!-- الوحدات المتقدمة -->
                        <hr class="text-white-50 mx-3">

                        {% if current_user.role == 'admin' %}
                        <a class="nav-link" href="{{ url_for('admin_advanced.dashboard') }}">
                            <i class="fas fa-cogs me-2"></i>
                            لوحة الإدارة
                        </a>
                        {% endif %}

                        <a class="nav-link" href="/reports">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير المتقدمة
                        </a>

                        <a class="nav-link" href="#" onclick="showNotifications()">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                            <span class="badge bg-danger ms-2">3</span>
                        </a>

                        <a class="nav-link" href="#" onclick="showWorkflow()">
                            <i class="fas fa-sitemap me-2"></i>
                            سير العمل
                        </a>

                        <a class="nav-link" href="#" onclick="showSettings()">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-10">
                <div class="main-content">
        {% else %}
        <div class="row justify-content-center">
            <div class="col-md-6">
        {% endif %}

                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <!-- Page Content -->
                    {% block content %}{% endblock %}

        {% if current_user.is_authenticated %}
                </div>
            </div>
        </div>
        {% else %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 نظام إدارة الموارد - دولة قطر. جميع الحقوق محفوظة.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- وظائف النظام المتقدمة -->
    <script>
        // متغيرات عامة
        let notificationsData = [];
        let workflowData = [];

        // تحميل الإشعارات
        async function loadNotifications() {
            try {
                const response = await fetch('/api/notifications');
                if (response.ok) {
                    notificationsData = await response.json();
                    updateNotificationsUI();
                }
            } catch (error) {
                console.error('خطأ في تحميل الإشعارات:', error);
                // بيانات تجريبية في حالة الخطأ
                notificationsData = [
                    {
                        id: 1,
                        title: 'موظف جديد',
                        message: 'تم إضافة موظف جديد: أحمد محمد',
                        type: 'success',
                        time_ago: 'منذ 5 دقائق',
                        is_read: false,
                        icon: 'fas fa-user-plus'
                    },
                    {
                        id: 2,
                        title: 'فاتورة جديدة',
                        message: 'فاتورة جديدة تحتاج موافقة - 15,000 ر.ق',
                        type: 'warning',
                        time_ago: 'منذ 15 دقيقة',
                        is_read: false,
                        icon: 'fas fa-file-invoice'
                    },
                    {
                        id: 3,
                        title: 'تذكير اجتماع',
                        message: 'اجتماع فريق المبيعات الساعة 3:00 م',
                        type: 'info',
                        time_ago: 'منذ ساعة',
                        is_read: true,
                        icon: 'fas fa-calendar'
                    }
                ];
                updateNotificationsUI();
            }
        }

        // تحديث واجهة الإشعارات
        function updateNotificationsUI() {
            const notificationCount = document.getElementById('notificationCount');
            const notificationsList = document.getElementById('notificationsList');

            // عدد الإشعارات غير المقروءة
            const unreadCount = notificationsData.filter(n => !n.is_read).length;
            notificationCount.textContent = unreadCount;
            notificationCount.style.display = unreadCount > 0 ? 'block' : 'none';

            // قائمة الإشعارات
            if (notificationsData.length === 0) {
                notificationsList.innerHTML = `
                    <div class="text-center p-3 text-muted">
                        <i class="fas fa-bell-slash fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد إشعارات جديدة</p>
                    </div>
                `;
            } else {
                notificationsList.innerHTML = notificationsData.slice(0, 5).map(notification => `
                    <div class="dropdown-item notification-item ${notification.is_read ? 'read' : 'unread'}"
                         onclick="markNotificationAsRead(${notification.id})">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon me-3">
                                <i class="${notification.icon} text-${getNotificationColor(notification.type)}"></i>
                            </div>
                            <div class="notification-content flex-grow-1">
                                <h6 class="notification-title mb-1">${notification.title}</h6>
                                <p class="notification-message mb-1 text-muted small">${notification.message}</p>
                                <small class="text-muted">${notification.time_ago}</small>
                            </div>
                            ${!notification.is_read ? '<div class="notification-badge bg-primary rounded-circle"></div>' : ''}
                        </div>
                    </div>
                `).join('');
            }
        }

        // تحميل مهام سير العمل
        async function loadWorkflowTasks() {
            try {
                const response = await fetch('/api/workflow/tasks');
                if (response.ok) {
                    workflowData = await response.json();
                    updateWorkflowUI();
                }
            } catch (error) {
                console.error('خطأ في تحميل مهام سير العمل:', error);
                // بيانات تجريبية
                workflowData = [
                    {
                        id: 1,
                        title: 'موافقة طلب إجازة',
                        description: 'طلب إجازة - سارة أحمد',
                        due_date: '2024-12-20',
                        priority: 'high'
                    },
                    {
                        id: 2,
                        title: 'مراجعة أمر شراء',
                        description: 'أمر شراء معدات مكتبية - 5,000 ر.ق',
                        due_date: '2024-12-21',
                        priority: 'normal'
                    }
                ];
                updateWorkflowUI();
            }
        }

        // تحديث واجهة سير العمل
        function updateWorkflowUI() {
            const workflowCount = document.getElementById('workflowCount');
            const workflowList = document.getElementById('workflowList');

            workflowCount.textContent = workflowData.length;
            workflowCount.style.display = workflowData.length > 0 ? 'block' : 'none';

            if (workflowData.length === 0) {
                workflowList.innerHTML = `
                    <div class="text-center p-3 text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد مهام معلقة</p>
                    </div>
                `;
            } else {
                workflowList.innerHTML = workflowData.map(task => `
                    <div class="dropdown-item workflow-item" onclick="openWorkflowTask(${task.id})">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${task.title}</h6>
                                <p class="mb-1 text-muted small">${task.description}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>${task.due_date}
                                </small>
                            </div>
                            <span class="badge bg-${getPriorityColor(task.priority)}">${getPriorityText(task.priority)}</span>
                        </div>
                    </div>
                `).join('');
            }
        }

        // وظائف مساعدة
        function getNotificationColor(type) {
            const colors = {
                'success': 'success',
                'warning': 'warning',
                'error': 'danger',
                'info': 'info',
                'system': 'secondary'
            };
            return colors[type] || 'primary';
        }

        function getPriorityColor(priority) {
            const colors = {
                'high': 'danger',
                'normal': 'primary',
                'low': 'secondary'
            };
            return colors[priority] || 'primary';
        }

        function getPriorityText(priority) {
            const texts = {
                'high': 'عالية',
                'normal': 'عادية',
                'low': 'منخفضة'
            };
            return texts[priority] || 'عادية';
        }

        // تحديد إشعار كمقروء
        async function markNotificationAsRead(notificationId) {
            try {
                const response = await fetch(`/api/notifications/${notificationId}/read`, {
                    method: 'POST'
                });

                if (response.ok) {
                    // تحديث البيانات المحلية
                    const notification = notificationsData.find(n => n.id === notificationId);
                    if (notification) {
                        notification.is_read = true;
                        updateNotificationsUI();
                    }
                }
            } catch (error) {
                console.error('خطأ في تحديد الإشعار كمقروء:', error);
            }
        }

        // تحديد جميع الإشعارات كمقروءة
        async function markAllAsRead() {
            try {
                const response = await fetch('/api/notifications/mark-all-read', {
                    method: 'POST'
                });

                if (response.ok) {
                    notificationsData.forEach(n => n.is_read = true);
                    updateNotificationsUI();
                }
            } catch (error) {
                console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', error);
            }
        }

        // فتح مهمة سير العمل
        function openWorkflowTask(taskId) {
            window.location.href = `/workflow/task/${taskId}`;
        }

        // تحديث دوري للإشعارات ومهام سير العمل
        setInterval(function() {
            loadNotifications();
            loadWorkflowTasks();
        }, 30000); // كل 30 ثانية

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل البيانات الأولية
            loadNotifications();
            loadWorkflowTasks();

            // تأثيرات بصرية للشريط الجانبي
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            const currentPath = window.location.pathname;

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }

                // تأثير hover متقدم
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(-10px)';
                    this.style.boxShadow = '0 4px 15px rgba(255,255,255,0.2)';
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateX(0)';
                        this.style.boxShadow = 'none';
                    }
                });
            });
        });

        // وظيفة البحث السريع
        function quickSearch() {
            const searchTerm = prompt('البحث السريع في النظام:');
            if (searchTerm) {
                alert(`نتائج البحث عن: "${searchTerm}"\n\n• 3 موظفين\n• 2 منتجات\n• 1 عميل\n• 4 فواتير`);
            }
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Ctrl + K للبحث السريع
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                quickSearch();
            }

            // Ctrl + H للذهاب للرئيسية
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                window.location.href = '/dashboard';
            }

            // Ctrl + R للتقارير
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                window.location.href = '/reports';
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
