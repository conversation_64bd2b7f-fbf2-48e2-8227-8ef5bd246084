{% extends "base.html" %}

{% block title %}قائمة الموظفين - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-users me-2 text-primary"></i>
            قائمة الموظفين
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('hr.index') }}">الموارد البشرية</a></li>
                <li class="breadcrumb-item active">الموظفين</li>
            </ol>
        </nav>
    </div>
</div>

<!-- أدوات البحث والتصفية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="البحث في الموظفين..." id="searchInput">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                            <option value="hr">الموارد البشرية</option>
                            <option value="finance">المالية</option>
                            <option value="it">تقنية المعلومات</option>
                            <option value="operations">العمليات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="terminated">منتهي الخدمة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" onclick="addNewEmployee()">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة موظف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                قائمة الموظفين ({{ employees|length }} موظف)
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الموظف</th>
                                <th>الاسم</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>تاريخ التوظيف</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>
                                    <strong>{{ employee.employee_number }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            {{ employee.first_name_ar[0] if employee.first_name_ar else 'م' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ employee.full_name_ar }}</div>
                                            {% if employee.user_account %}
                                            <small class="text-muted">{{ employee.user_account.email }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ employee.department or 'غير محدد' }}</span>
                                </td>
                                <td>{{ employee.position or 'غير محدد' }}</td>
                                <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد' }}</td>
                                <td>
                                    {% if employee.employment_status == 'active' %}
                                    <span class="badge bg-success">نشط</span>
                                    {% elif employee.employment_status == 'inactive' %}
                                    <span class="badge bg-warning">غير نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">منتهي الخدمة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل" onclick="viewEmployee({{ employee.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" title="تعديل" onclick="editEmployee({{ employee.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" title="سجل الحضور" onclick="viewAttendance({{ employee.id }})">
                                            <i class="fas fa-clock"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" title="كشف الراتب" onclick="viewPayroll({{ employee.id }})">
                                            <i class="fas fa-money-bill"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="حذف" onclick="deleteEmployee({{ employee.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد موظفين مسجلين</h5>
                    <p class="text-muted">ابدأ بإضافة الموظفين إلى النظام</p>
                    <button class="btn btn-primary" onclick="addNewEmployee()">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة موظف جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if employees %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ employees|selectattr("employment_status", "equalto", "active")|list|length }}</h5>
                <small class="text-muted">موظف نشط</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ employees|selectattr("department", "equalto", "hr")|list|length }}</h5>
                <small class="text-muted">موارد بشرية</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ employees|selectattr("department", "equalto", "finance")|list|length }}</h5>
                <small class="text-muted">مالية</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ employees|selectattr("department", "equalto", "it")|list|length }}</h5>
                <small class="text-muted">تقنية معلومات</small>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="exportEmployees()">
                            <i class="fas fa-download me-2"></i>
                            تصدير قائمة الموظفين
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="generateEmployeeReport()">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير الموظفين
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="bulkActions()">
                            <i class="fas fa-tasks me-2"></i>
                            إجراءات جماعية
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="employeeSettings()">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات الموظفين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Modal لإضافة موظف جديد -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <!-- البيانات الشخصية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">البيانات الشخصية</h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الاسم الأول (عربي) *</label>
                            <input type="text" class="form-control" id="firstNameAr" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">اسم العائلة (عربي) *</label>
                            <input type="text" class="form-control" id="lastNameAr" required>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">الاسم الأول (إنجليزي)</label>
                            <input type="text" class="form-control" id="firstNameEn">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">اسم العائلة (إنجليزي)</label>
                            <input type="text" class="form-control" id="lastNameEn">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">رقم الهوية *</label>
                            <input type="text" class="form-control" id="nationalId" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="birthDate">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الجنس</label>
                            <select class="form-select" id="gender">
                                <option value="">اختر الجنس</option>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>
                    </div>

                    <!-- بيانات التوظيف -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">بيانات التوظيف</h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">رقم الموظف *</label>
                            <input type="text" class="form-control" id="employeeNumber" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">القسم *</label>
                            <select class="form-select" id="department" required>
                                <option value="">اختر القسم</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="finance">المالية</option>
                                <option value="it">تقنية المعلومات</option>
                                <option value="operations">العمليات</option>
                                <option value="marketing">التسويق</option>
                                <option value="sales">المبيعات</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المنصب *</label>
                            <input type="text" class="form-control" id="position" required>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">تاريخ التوظيف *</label>
                            <input type="date" class="form-control" id="hireDate" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">حالة التوظيف</label>
                            <select class="form-select" id="employmentStatus">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="terminated">منتهي الخدمة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المدير المباشر</label>
                            <select class="form-select" id="managerId">
                                <option value="">اختر المدير</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                    </div>

                    <!-- بيانات الراتب -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">بيانات الراتب</h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الراتب الأساسي (ر.ق)</label>
                            <input type="number" class="form-control" id="basicSalary" step="0.01">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البدلات (ر.ق)</label>
                            <input type="number" class="form-control" id="allowances" step="0.01">
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="row mb-4 mt-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">معلومات الاتصال</h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveEmployee()">حفظ الموظف</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الموظف -->
<div class="modal fade" id="viewEmployeeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الموظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="employeeDetails">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editEmployeeFromView()">تعديل</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// البحث في الجدول
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// تصفية حسب القسم
document.getElementById('departmentFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        if (!filterValue) {
            row.style.display = '';
        } else {
            const departmentCell = row.cells[2].textContent.toLowerCase();
            row.style.display = departmentCell.includes(filterValue) ? '' : 'none';
        }
    });
});

// تصفية حسب الحالة
document.getElementById('statusFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        if (!filterValue) {
            row.style.display = '';
        } else {
            const statusCell = row.cells[5].textContent.toLowerCase();
            const showRow = (filterValue === 'active' && statusCell.includes('نشط')) ||
                           (filterValue === 'inactive' && statusCell.includes('غير نشط')) ||
                           (filterValue === 'terminated' && statusCell.includes('منتهي'));
            row.style.display = showRow ? '' : 'none';
        }
    });
});

// وظائف إدارة الموظفين
function addNewEmployee() {
    // تحديد التاريخ الافتراضي لتاريخ التوظيف
    document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

    // تحديد حالة التوظيف الافتراضية
    document.getElementById('employmentStatus').value = 'active';

    // تحميل قائمة المديرين
    loadManagers();

    // إنتاج رقم موظف تلقائي
    generateEmployeeNumber();

    // عرض النموذج
    new bootstrap.Modal(document.getElementById('addEmployeeModal')).show();
}

function generateEmployeeNumber() {
    // إنتاج رقم موظف تلقائي
    const currentYear = new Date().getFullYear();
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    document.getElementById('employeeNumber').value = `EMP${currentYear}${randomNum}`;
}

function loadManagers() {
    // هنا يمكن إضافة استدعاء AJAX لجلب قائمة المديرين
    const managerSelect = document.getElementById('managerId');
    managerSelect.innerHTML = '<option value="">اختر المدير</option>';

    // مديرين تجريبيين
    const managers = [
        {id: 1, name: 'أحمد محمد الكعبي - مدير الموارد البشرية'},
        {id: 2, name: 'فاطمة علي النعيمي - مدير المالية'},
        {id: 3, name: 'محمد سالم الثاني - مدير العمليات'}
    ];

    managers.forEach(manager => {
        const option = document.createElement('option');
        option.value = manager.id;
        option.textContent = manager.name;
        managerSelect.appendChild(option);
    });
}

function saveEmployee() {
    const form = document.getElementById('addEmployeeForm');

    // التحقق من الحقول المطلوبة
    const requiredFields = ['firstNameAr', 'lastNameAr', 'nationalId', 'employeeNumber', 'department', 'position', 'hireDate'];
    let isValid = true;

    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // جمع البيانات
    const employeeData = {
        first_name_ar: document.getElementById('firstNameAr').value,
        last_name_ar: document.getElementById('lastNameAr').value,
        first_name_en: document.getElementById('firstNameEn').value,
        last_name_en: document.getElementById('lastNameEn').value,
        national_id: document.getElementById('nationalId').value,
        birth_date: document.getElementById('birthDate').value,
        gender: document.getElementById('gender').value,
        employee_number: document.getElementById('employeeNumber').value,
        department: document.getElementById('department').value,
        position: document.getElementById('position').value,
        hire_date: document.getElementById('hireDate').value,
        employment_status: document.getElementById('employmentStatus').value,
        manager_id: document.getElementById('managerId').value || null,
        basic_salary: parseFloat(document.getElementById('basicSalary').value) || 0,
        allowances: parseFloat(document.getElementById('allowances').value) || 0,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        address: document.getElementById('address').value
    };

    // إرسال البيانات إلى الخادم
    fetch('/hr/api/add_employee', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(employeeData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('addEmployeeModal')).hide();
            location.reload(); // إعادة تحميل الصفحة لإظهار الموظف الجديد
        } else {
            alert(data.message || 'حدث خطأ في حفظ البيانات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function viewEmployee(employeeId) {
    // جلب بيانات الموظف من الخادم
    fetch(`/hr/api/employee/${employeeId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const emp = data.employee;
            const employeeDetails = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">البيانات الشخصية</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم الكامل:</strong></td><td>${emp.full_name_ar}</td></tr>
                            <tr><td><strong>رقم الهوية:</strong></td><td>${emp.national_id || 'غير محدد'}</td></tr>
                            <tr><td><strong>تاريخ الميلاد:</strong></td><td>${emp.birth_date || 'غير محدد'}</td></tr>
                            <tr><td><strong>الجنس:</strong></td><td>${emp.gender === 'male' ? 'ذكر' : emp.gender === 'female' ? 'أنثى' : 'غير محدد'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">بيانات التوظيف</h6>
                        <table class="table table-sm">
                            <tr><td><strong>رقم الموظف:</strong></td><td>${emp.employee_number}</td></tr>
                            <tr><td><strong>القسم:</strong></td><td>${emp.department || 'غير محدد'}</td></tr>
                            <tr><td><strong>المنصب:</strong></td><td>${emp.position || 'غير محدد'}</td></tr>
                            <tr><td><strong>تاريخ التوظيف:</strong></td><td>${emp.hire_date || 'غير محدد'}</td></tr>
                            <tr><td><strong>الحالة:</strong></td><td>
                                ${emp.employment_status === 'active' ? '<span class="badge bg-success">نشط</span>' :
                                  emp.employment_status === 'inactive' ? '<span class="badge bg-warning">غير نشط</span>' :
                                  '<span class="badge bg-danger">منتهي الخدمة</span>'}
                            </td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-primary">بيانات الراتب</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الراتب الأساسي:</strong></td><td>${emp.basic_salary ? emp.basic_salary.toLocaleString('ar-QA') + ' ر.ق' : 'غير محدد'}</td></tr>
                            <tr><td><strong>البدلات:</strong></td><td>${emp.allowances ? emp.allowances.toLocaleString('ar-QA') + ' ر.ق' : 'غير محدد'}</td></tr>
                            <tr><td><strong>إجمالي الراتب:</strong></td><td>${(emp.basic_salary + emp.allowances).toLocaleString('ar-QA')} ر.ق</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات الاتصال</h6>
                        <table class="table table-sm">
                            <tr><td><strong>البريد الإلكتروني:</strong></td><td>${emp.email || 'غير محدد'}</td></tr>
                            <tr><td><strong>رقم الهاتف:</strong></td><td>${emp.phone || 'غير محدد'}</td></tr>
                            <tr><td><strong>العنوان:</strong></td><td>${emp.address || 'غير محدد'}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('employeeDetails').innerHTML = employeeDetails;
            // حفظ معرف الموظف للاستخدام في التعديل
            document.getElementById('viewEmployeeModal').dataset.employeeId = employeeId;
            new bootstrap.Modal(document.getElementById('viewEmployeeModal')).show();
        } else {
            alert('حدث خطأ في جلب بيانات الموظف');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function editEmployee(employeeId) {
    // هنا يمكن إضافة استدعاء AJAX لجلب بيانات الموظف وملء النموذج
    alert(`تعديل الموظف رقم ${employeeId}`);
    // يمكن إعادة استخدام نموذج الإضافة مع ملء البيانات الحالية
}

function viewAttendance(employeeId) {
    // الانتقال إلى صفحة الحضور مع تصفية الموظف
    window.location.href = `/hr/attendance?employee_id=${employeeId}`;
}

function viewPayroll(employeeId) {
    // الانتقال إلى صفحة الرواتب مع تصفية الموظف
    window.location.href = `/hr/payroll?employee_id=${employeeId}`;
}

function deleteEmployee(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/hr/api/delete_employee/${employeeId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في حذف الموظف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function editEmployeeFromView() {
    // إغلاق نافذة العرض وفتح نافذة التعديل
    bootstrap.Modal.getInstance(document.getElementById('viewEmployeeModal')).hide();
    // هنا يمكن فتح نموذج التعديل
    alert('سيتم فتح نموذج التعديل');
}

// الإجراءات السريعة
function exportEmployees() {
    // عرض مؤشر التحميل
    const originalText = event.target.innerHTML;
    event.target.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    event.target.disabled = true;

    fetch('/hr/api/export_employees')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحويل البيانات إلى JSON وتنزيلها
            const jsonData = JSON.stringify(data.data, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert(`تم تصدير ${data.total_records} موظف بنجاح`);
        } else {
            alert('حدث خطأ في تصدير البيانات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        // إعادة النص الأصلي
        event.target.innerHTML = originalText;
        event.target.disabled = false;
    });
}

function generateEmployeeReport() {
    alert('سيتم إنتاج تقرير شامل للموظفين');
    // هنا يمكن إضافة وظيفة إنتاج التقرير
}

function bulkActions() {
    alert('الإجراءات الجماعية للموظفين');
    // هنا يمكن إضافة نافذة للإجراءات الجماعية
}

function employeeSettings() {
    alert('إعدادات إدارة الموظفين');
    // هنا يمكن إضافة صفحة الإعدادات
}
</script>
{% endblock %}
