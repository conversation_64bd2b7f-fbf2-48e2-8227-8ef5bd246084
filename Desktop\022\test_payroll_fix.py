#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح صفحة الرواتب
"""

import requests
import sys

def test_payroll_page():
    """اختبار صفحة الرواتب"""
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار إصلاح صفحة الرواتب...")
    print("=" * 50)
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # 1. تسجيل الدخول
        print("🔑 تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
        
        # 2. اختبار صفحة الرواتب
        print("💰 اختبار صفحة الرواتب...")
        payroll_response = session.get(f"{base_url}/hr/payroll")
        
        if payroll_response.status_code == 200:
            print("✅ صفحة الرواتب تعمل بنجاح!")
            
            # التحقق من وجود العناصر المهمة
            content = payroll_response.text
            
            checks = [
                ("إدارة الرواتب", "عنوان الصفحة"),
                ("إجمالي كشوف الرواتب", "إحصائيات"),
                ("إنتاج كشف راتب", "زر الإضافة"),
                ("number_format", "فلتر تنسيق الأرقام")
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"  ✅ {description}: موجود")
                else:
                    print(f"  ⚠️ {description}: غير موجود")
            
            return True
            
        else:
            print(f"❌ خطأ في صفحة الرواتب: {payroll_response.status_code}")
            
            # طباعة تفاصيل الخطأ
            if payroll_response.status_code == 500:
                print("📋 تفاصيل الخطأ:")
                error_lines = payroll_response.text.split('\n')
                for line in error_lines[:10]:  # أول 10 أسطر
                    if 'TypeError' in line or 'Error' in line:
                        print(f"  🔍 {line.strip()}")
            
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل النظام على المنفذ 5000")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

def test_hr_module():
    """اختبار وحدة الموارد البشرية"""
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    print("\n👥 اختبار وحدة الموارد البشرية...")
    print("=" * 50)
    
    try:
        # تسجيل الدخول
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # اختبار صفحات الموارد البشرية
        hr_pages = [
            ("/hr/", "الصفحة الرئيسية"),
            ("/hr/employees", "الموظفين"),
            ("/hr/attendance", "الحضور والانصراف"),
            ("/hr/payroll", "الرواتب"),
            ("/hr/leave_requests", "طلبات الإجازات")
        ]
        
        results = []
        
        for url, name in hr_pages:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    print(f"  ✅ {name}: يعمل بنجاح")
                    results.append(True)
                else:
                    print(f"  ❌ {name}: خطأ {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"  ❌ {name}: خطأ - {str(e)}")
                results.append(False)
        
        success_rate = (sum(results) / len(results)) * 100
        print(f"\n📊 معدل النجاح: {success_rate:.1f}% ({sum(results)}/{len(results)})")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة الموارد البشرية: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات النظام")
    print("=" * 60)
    
    # اختبار صفحة الرواتب
    payroll_success = test_payroll_page()
    
    # اختبار وحدة الموارد البشرية
    hr_success = test_hr_module()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    print("=" * 60)
    
    if payroll_success:
        print("✅ صفحة الرواتب: تم إصلاحها بنجاح")
    else:
        print("❌ صفحة الرواتب: تحتاج مزيد من الإصلاح")
    
    if hr_success:
        print("✅ وحدة الموارد البشرية: تعمل بشكل جيد")
    else:
        print("❌ وحدة الموارد البشرية: تحتاج إصلاحات")
    
    overall_success = payroll_success and hr_success
    
    if overall_success:
        print("\n🎉 تم إصلاح المشاكل بنجاح!")
        print("💡 يمكنك الآن استخدام صفحة الرواتب بدون أخطاء")
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إصلاح")
        print("💡 راجع الأخطاء أعلاه وقم بالإصلاحات اللازمة")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
