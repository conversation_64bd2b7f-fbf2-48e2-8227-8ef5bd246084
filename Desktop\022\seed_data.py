from app import app
from database_setup import db
from models.user import User
from models.hr import Employee
from models.inventory import Product, Warehouse
from models.sales import Customer
from models.procurement import Supplier
from models.accounting import Account
from werkzeug.security import generate_password_hash
from datetime import datetime, date
from decimal import Decimal

def create_sample_data():
    """إنشاء بيانات تجريبية للنظام"""
    
    with app.app_context():
        # إنشاء المستخدمين
        users_data = [
            {
                'username': 'hr_manager',
                'email': '<EMAIL>',
                'full_name': 'أحمد محمد الكعبي',
                'role': 'hr',
                'department': 'الموارد البشرية',
                'position': 'مدير الموارد البشرية'
            },
            {
                'username': 'accountant',
                'email': '<EMAIL>',
                'full_name': 'فاطمة علي النعيمي',
                'role': 'accountant',
                'department': 'المالية',
                'position': 'محاسب رئيسي'
            },
            {
                'username': 'inventory_manager',
                'email': '<EMAIL>',
                'full_name': 'محمد سالم الثاني',
                'role': 'manager',
                'department': 'المخزون',
                'position': 'مدير المخزون'
            }
        ]
        
        for user_data in users_data:
            if not User.query.filter_by(username=user_data['username']).first():
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    full_name=user_data['full_name'],
                    role=user_data['role'],
                    department=user_data['department'],
                    position=user_data['position'],
                    password_hash=generate_password_hash('123456'),
                    is_active=True
                )
                db.session.add(user)
        
        # إنشاء الموظفين
        employees_data = [
            {
                'employee_number': 'EMP001',
                'first_name_ar': 'أحمد',
                'last_name_ar': 'محمد الكعبي',
                'department': 'الموارد البشرية',
                'position': 'مدير الموارد البشرية',
                'hire_date': date(2020, 1, 15),
                'basic_salary': Decimal('15000.00'),
                'employment_status': 'active'
            },
            {
                'employee_number': 'EMP002',
                'first_name_ar': 'فاطمة',
                'last_name_ar': 'علي النعيمي',
                'department': 'المالية',
                'position': 'محاسب رئيسي',
                'hire_date': date(2019, 3, 10),
                'basic_salary': Decimal('12000.00'),
                'employment_status': 'active'
            },
            {
                'employee_number': 'EMP003',
                'first_name_ar': 'محمد',
                'last_name_ar': 'سالم الثاني',
                'department': 'المخزون',
                'position': 'مدير المخزون',
                'hire_date': date(2021, 6, 1),
                'basic_salary': Decimal('13000.00'),
                'employment_status': 'active'
            },
            {
                'employee_number': 'EMP004',
                'first_name_ar': 'عائشة',
                'last_name_ar': 'خالد المري',
                'department': 'تقنية المعلومات',
                'position': 'مطور نظم',
                'hire_date': date(2022, 2, 20),
                'basic_salary': Decimal('11000.00'),
                'employment_status': 'active'
            },
            {
                'employee_number': 'EMP005',
                'first_name_ar': 'يوسف',
                'last_name_ar': 'أحمد الدوسري',
                'department': 'العمليات',
                'position': 'منسق عمليات',
                'hire_date': date(2023, 1, 5),
                'basic_salary': Decimal('9000.00'),
                'employment_status': 'active'
            }
        ]
        
        for emp_data in employees_data:
            if not Employee.query.filter_by(employee_number=emp_data['employee_number']).first():
                employee = Employee(**emp_data)
                db.session.add(employee)
        
        # إنشاء المستودعات
        warehouses_data = [
            {
                'code': 'WH001',
                'name_ar': 'المستودع الرئيسي',
                'name_en': 'Main Warehouse',
                'location': 'الدوحة - المنطقة الصناعية',
                'is_main': True,
                'is_active': True
            },
            {
                'code': 'WH002',
                'name_ar': 'مستودع الفرع الشمالي',
                'name_en': 'North Branch Warehouse',
                'location': 'الشمال - منطقة الصناعات',
                'is_main': False,
                'is_active': True
            }
        ]
        
        for wh_data in warehouses_data:
            if not Warehouse.query.filter_by(code=wh_data['code']).first():
                warehouse = Warehouse(**wh_data)
                db.session.add(warehouse)
        
        # إنشاء المنتجات
        products_data = [
            {
                'code': 'PROD001',
                'name_ar': 'أجهزة كمبيوتر محمولة',
                'name_en': 'Laptops',
                'category': 'electronics',
                'unit_of_measure': 'قطعة',
                'current_stock': Decimal('25'),
                'minimum_stock': Decimal('10'),
                'maximum_stock': Decimal('100'),
                'cost_price': Decimal('3500.00'),
                'selling_price': Decimal('4200.00'),
                'is_active': True
            },
            {
                'code': 'PROD002',
                'name_ar': 'طابعات ليزر',
                'name_en': 'Laser Printers',
                'category': 'electronics',
                'unit_of_measure': 'قطعة',
                'current_stock': Decimal('8'),
                'minimum_stock': Decimal('5'),
                'maximum_stock': Decimal('50'),
                'cost_price': Decimal('800.00'),
                'selling_price': Decimal('1000.00'),
                'is_active': True
            },
            {
                'code': 'PROD003',
                'name_ar': 'مكاتب خشبية',
                'name_en': 'Wooden Desks',
                'category': 'furniture',
                'unit_of_measure': 'قطعة',
                'current_stock': Decimal('15'),
                'minimum_stock': Decimal('5'),
                'maximum_stock': Decimal('30'),
                'cost_price': Decimal('1200.00'),
                'selling_price': Decimal('1500.00'),
                'is_active': True
            },
            {
                'code': 'PROD004',
                'name_ar': 'أوراق A4',
                'name_en': 'A4 Papers',
                'category': 'supplies',
                'unit_of_measure': 'علبة',
                'current_stock': Decimal('3'),
                'minimum_stock': Decimal('20'),
                'maximum_stock': Decimal('200'),
                'cost_price': Decimal('25.00'),
                'selling_price': Decimal('30.00'),
                'is_active': True
            },
            {
                'code': 'PROD005',
                'name_ar': 'كراسي مكتبية',
                'name_en': 'Office Chairs',
                'category': 'furniture',
                'unit_of_measure': 'قطعة',
                'current_stock': Decimal('20'),
                'minimum_stock': Decimal('10'),
                'maximum_stock': Decimal('50'),
                'cost_price': Decimal('450.00'),
                'selling_price': Decimal('600.00'),
                'is_active': True
            }
        ]
        
        for prod_data in products_data:
            if not Product.query.filter_by(code=prod_data['code']).first():
                product = Product(**prod_data)
                db.session.add(product)
        
        # إنشاء العملاء
        customers_data = [
            {
                'code': 'CUST001',
                'name_ar': 'وزارة التعليم والتعليم العالي',
                'customer_type': 'government',
                'phone': '+974-4444-1111',
                'email': '<EMAIL>',
                'address': 'الدوحة - منطقة الخليج الغربي',
                'credit_limit': Decimal('100000.00'),
                'is_active': True
            },
            {
                'code': 'CUST002',
                'name_ar': 'شركة قطر للبترول',
                'customer_type': 'company',
                'phone': '+974-4444-2222',
                'email': '<EMAIL>',
                'address': 'الدوحة - منطقة الدفنة',
                'credit_limit': Decimal('500000.00'),
                'is_active': True
            },
            {
                'code': 'CUST003',
                'name_ar': 'مؤسسة حمد الطبية',
                'customer_type': 'government',
                'phone': '+974-4444-3333',
                'email': '<EMAIL>',
                'address': 'الدوحة - منطقة بن عمران',
                'credit_limit': Decimal('200000.00'),
                'is_active': True
            }
        ]
        
        for cust_data in customers_data:
            if not Customer.query.filter_by(code=cust_data['code']).first():
                customer = Customer(**cust_data)
                db.session.add(customer)
        
        # إنشاء الموردين
        suppliers_data = [
            {
                'code': 'SUPP001',
                'name_ar': 'شركة التقنية المتقدمة',
                'name_en': 'Advanced Technology Company',
                'supplier_type': 'company',
                'contact_person': 'أحمد محمد',
                'phone': '+974-5555-1111',
                'email': '<EMAIL>',
                'address': 'الدوحة - منطقة الصناعات',
                'is_active': True,
                'is_approved': True
            },
            {
                'code': 'SUPP002',
                'name_ar': 'مؤسسة الأثاث الحديث',
                'name_en': 'Modern Furniture Est.',
                'supplier_type': 'company',
                'contact_person': 'فاطمة علي',
                'phone': '+974-5555-2222',
                'email': '<EMAIL>',
                'address': 'الدوحة - الصناعية الجديدة',
                'is_active': True,
                'is_approved': True
            }
        ]
        
        for supp_data in suppliers_data:
            if not Supplier.query.filter_by(code=supp_data['code']).first():
                supplier = Supplier(**supp_data)
                db.session.add(supplier)
        
        # إنشاء الحسابات المحاسبية الأساسية
        accounts_data = [
            {
                'code': '1000',
                'name_ar': 'الأصول',
                'account_type': 'asset',
                'level': 1,
                'is_system': True
            },
            {
                'code': '1100',
                'name_ar': 'الأصول المتداولة',
                'account_type': 'asset',
                'level': 2,
                'is_system': True
            },
            {
                'code': '1110',
                'name_ar': 'النقدية',
                'account_type': 'asset',
                'level': 3,
                'opening_balance': Decimal('100000.00')
            },
            {
                'code': '1120',
                'name_ar': 'المخزون',
                'account_type': 'asset',
                'level': 3,
                'opening_balance': Decimal('50000.00')
            },
            {
                'code': '2000',
                'name_ar': 'الخصوم',
                'account_type': 'liability',
                'level': 1,
                'is_system': True
            },
            {
                'code': '3000',
                'name_ar': 'حقوق الملكية',
                'account_type': 'equity',
                'level': 1,
                'is_system': True
            },
            {
                'code': '4000',
                'name_ar': 'الإيرادات',
                'account_type': 'revenue',
                'level': 1,
                'is_system': True
            },
            {
                'code': '5000',
                'name_ar': 'المصروفات',
                'account_type': 'expense',
                'level': 1,
                'is_system': True
            }
        ]
        
        for acc_data in accounts_data:
            if not Account.query.filter_by(code=acc_data['code']).first():
                account = Account(**acc_data)
                db.session.add(account)
        
        # حفظ جميع البيانات
        db.session.commit()
        print("تم إنشاء البيانات التجريبية بنجاح!")

if __name__ == '__main__':
    create_sample_data()
