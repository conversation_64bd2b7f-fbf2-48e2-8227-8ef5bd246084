#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام المتقدم - نظام إدارة الموارد المتكامل
دولة قطر 🇶🇦
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🇶🇦  نظام إدارة الموارد المتكامل - دولة قطر  🇶🇦         ║
    ║                                                              ║
    ║              النسخة المتقدمة 2.1.0                          ║
    ║                                                              ║
    ║    ✨ الوحدات المتاحة:                                      ║
    ║    • المحاسبة والتقارير المالية                            ║
    ║    • الموارد البشرية والرواتب                             ║
    ║    • إدارة المخزون والمنتجات                              ║
    ║    • المبيعات وإدارة العملاء                               ║
    ║    • المشتريات والموردين                                   ║
    ║    • إدارة المشاريع والمهام                                ║
    ║    • نظام CRM المتقدم                                      ║
    ║    • لوحة الإدارة المتقدمة                                 ║
    ║    • التقارير والتحليلات المتقدمة                          ║
    ║    • نظام الإشعارات الذكية                                 ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy', 
        'flask-login',
        'werkzeug',
        'matplotlib',
        'pandas',
        'reportlab'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} - مفقود")
    
    if missing_packages:
        print(f"\n⚠️  المتطلبات المفقودة: {', '.join(missing_packages)}")
        print("💡 تشغيل: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️  إعداد قاعدة البيانات...")
    
    # إنشاء مجلدات ضرورية
    directories = ['database', 'static/uploads', 'instance']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"  📁 تم إنشاء مجلد: {directory}")
    
    # فحص قاعدة البيانات
    db_path = 'instance/erp_system.db'
    if os.path.exists(db_path):
        size = os.path.getsize(db_path)
        print(f"  ✅ قاعدة البيانات موجودة ({size} bytes)")
    else:
        print("  🔧 سيتم إنشاء قاعدة البيانات عند التشغيل")
    
    return True

def check_port(port=5000):
    """فحص توفر المنفذ"""
    import socket
    
    print(f"\n🌐 فحص المنفذ {port}...")
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', port))
    sock.close()
    
    if result == 0:
        print(f"  ⚠️  المنفذ {port} مستخدم")
        return False
    else:
        print(f"  ✅ المنفذ {port} متاح")
        return True

def start_system():
    """تشغيل النظام"""
    print("\n🚀 بدء تشغيل النظام...")
    
    # متغيرات البيئة
    os.environ['FLASK_APP'] = 'app.py'
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    print("  🔧 إعداد متغيرات البيئة")
    print("  📱 تشغيل خادم Flask...")
    print("  🌍 الرابط: http://localhost:5000")
    print("  👤 المستخدم الافتراضي: admin")
    print("  🔐 كلمة المرور: admin123")
    print("\n" + "="*60)
    print("🎯 النظام جاهز للاستخدام!")
    print("="*60)
    
    # تشغيل التطبيق
    try:
        import app
        app.app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📊 معلومات النظام:")
    print(f"  🐍 Python: {sys.version.split()[0]}")
    print(f"  💻 نظام التشغيل: {os.name}")
    print(f"  📁 مجلد العمل: {os.getcwd()}")
    print(f"  🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # فحص النظام
    if not check_dependencies():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    if not setup_database():
        print("\n❌ فشل في إعداد قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return
    
    if not check_port():
        print("\n⚠️  المنفذ 5000 مستخدم. جرب منفذ آخر أو أوقف التطبيق الآخر")
        choice = input("هل تريد المتابعة؟ (y/n): ")
        if choice.lower() != 'y':
            return
    
    show_system_info()
    
    # تأكيد التشغيل
    print("\n" + "="*60)
    input("اضغط Enter لبدء تشغيل النظام...")
    
    # تشغيل النظام
    start_system()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 وداعاً!")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
