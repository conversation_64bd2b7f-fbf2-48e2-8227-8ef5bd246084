#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أنواع الإجازات الجديدة
"""

from app import app
from database_setup import db
from models.hr import Employee, LeaveRequest
from datetime import datetime, date, timedelta
import json

def test_new_leave_types():
    """اختبار أنواع الإجازات الجديدة"""
    
    print("🧪 اختبار أنواع الإجازات الجديدة...")
    print("=" * 60)
    
    with app.app_context():
        # الحصول على موظف للاختبار
        employee = Employee.query.first()
        if not employee:
            print("❌ لا يوجد موظفين في النظام")
            return
        
        print(f"👤 اختبار الإجازات للموظف: {employee.full_name_ar}")
        print(f"📧 البريد الإلكتروني: {employee.email or 'غير محدد'}")
        
        # أنواع الإجازات الجديدة للاختبار
        new_leave_types = [
            {
                'type': 'administrative',
                'name': 'إجازة إدارية',
                'days': 3,
                'reason': 'إجازة إدارية لحضور دورة تدريبية',
                'color': 'success'
            },
            {
                'type': 'holiday_compensation',
                'name': 'إجازة بدل أعياد',
                'days': 2,
                'reason': 'إجازة بدل العمل في العيد الوطني',
                'color': 'purple'
            },
            {
                'type': 'grant_compensation',
                'name': 'إجازة بدل منح',
                'days': 1,
                'reason': 'إجازة بدل منحة الأداء المتميز',
                'color': 'teal'
            },
            {
                'type': 'casual',
                'name': 'إجازة عرضية',
                'days': 2,
                'reason': 'إجازة عرضية لظروف شخصية',
                'color': 'orange'
            }
        ]
        
        print(f"\n📋 إضافة {len(new_leave_types)} طلبات إجازة جديدة...")
        
        created_requests = []
        start_date = date.today() + timedelta(days=7)  # بداية من الأسبوع القادم
        
        for i, leave_type in enumerate(new_leave_types):
            try:
                # حساب تواريخ الإجازة
                request_start = start_date + timedelta(days=i * 5)  # فجوة 5 أيام بين كل إجازة
                request_end = request_start + timedelta(days=leave_type['days'] - 1)
                
                # إنشاء طلب إجازة جديد
                leave_request = LeaveRequest(
                    employee_id=employee.id,
                    leave_type=leave_type['type'],
                    start_date=request_start,
                    end_date=request_end,
                    days_requested=leave_type['days'],
                    reason=leave_type['reason'],
                    status='pending',
                    created_at=datetime.now()
                )
                
                db.session.add(leave_request)
                db.session.commit()
                
                created_requests.append({
                    'id': leave_request.id,
                    'type': leave_type['type'],
                    'name': leave_type['name'],
                    'start_date': request_start.strftime('%Y-%m-%d'),
                    'end_date': request_end.strftime('%Y-%m-%d'),
                    'days': leave_type['days'],
                    'status': 'pending'
                })
                
                print(f"✅ تم إنشاء طلب {leave_type['name']}")
                print(f"   📅 من {request_start} إلى {request_end} ({leave_type['days']} أيام)")
                
            except Exception as e:
                print(f"❌ خطأ في إنشاء طلب {leave_type['name']}: {e}")
                db.session.rollback()
        
        # اختبار رصيد الإجازات
        print(f"\n💰 اختبار رصيد الإجازات للموظف...")
        
        # محاكاة استدعاء API رصيد الإجازات
        from routes.hr import api_employee_leave_balance
        
        try:
            with app.test_request_context():
                balance_response = api_employee_leave_balance(employee.id)
                
                if balance_response.get('success'):
                    balances = balance_response['balances']
                    
                    print("📊 أرصدة الإجازات:")
                    for leave_type, balance in balances.items():
                        print(f"   {balance['type_name']}:")
                        print(f"      المتاح: {balance['available']} يوم")
                        print(f"      المستخدم: {balance['used']} يوم")
                        print(f"      المتبقي: {balance['remaining']} يوم")
                        print()
                else:
                    print("❌ خطأ في جلب رصيد الإجازات")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار رصيد الإجازات: {e}")
        
        # اختبار إعدادات الإجازات
        print("⚙️ اختبار إعدادات الإجازات الجديدة...")
        
        try:
            from routes.hr import api_leave_settings
            
            with app.test_request_context():
                settings_response = api_leave_settings()
                
                if settings_response.get('success'):
                    settings = settings_response['settings']
                    
                    print("📋 إعدادات الإجازات:")
                    print(f"   الإجازة السنوية: {settings['annual_leave_days']} يوم")
                    print(f"   الإجازة المرضية: {settings['sick_leave_days']} يوم")
                    print(f"   الإجازة الطارئة: {settings['emergency_leave_days']} يوم")
                    print(f"   إجازة الأمومة: {settings['maternity_leave_days']} يوم")
                    print(f"   الإجازة الإدارية: {settings['administrative_leave_days']} يوم")
                    print(f"   إجازة بدل أعياد: {settings['holiday_compensation_days']} يوم")
                    print(f"   إجازة بدل منح: {settings['grant_compensation_days']} يوم")
                    print(f"   الإجازة العرضية: {settings['casual_leave_days']} يوم")
                else:
                    print("❌ خطأ في جلب إعدادات الإجازات")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار إعدادات الإجازات: {e}")
        
        # إحصائيات الطلبات
        print(f"\n📈 إحصائيات طلبات الإجازات...")
        
        all_requests = LeaveRequest.query.filter_by(employee_id=employee.id).all()
        
        # تجميع حسب النوع
        type_counts = {}
        for request in all_requests:
            type_name = {
                'annual': 'إجازة سنوية',
                'sick': 'إجازة مرضية',
                'emergency': 'إجازة طارئة',
                'maternity': 'إجازة أمومة',
                'administrative': 'إجازة إدارية',
                'holiday_compensation': 'إجازة بدل أعياد',
                'grant_compensation': 'إجازة بدل منح',
                'casual': 'إجازة عرضية',
                'unpaid': 'إجازة بدون راتب'
            }.get(request.leave_type, request.leave_type)
            
            if type_name not in type_counts:
                type_counts[type_name] = {'count': 0, 'days': 0}
            
            type_counts[type_name]['count'] += 1
            type_counts[type_name]['days'] += request.days_requested
        
        print("📊 توزيع الطلبات حسب النوع:")
        for type_name, stats in type_counts.items():
            print(f"   {type_name}: {stats['count']} طلب، {stats['days']} يوم")
        
        # تجميع حسب الحالة
        status_counts = {}
        for request in all_requests:
            status_name = {
                'pending': 'معلقة',
                'approved': 'موافق عليها',
                'rejected': 'مرفوضة',
                'cancelled': 'ملغاة'
            }.get(request.status, request.status)
            
            if status_name not in status_counts:
                status_counts[status_name] = 0
            status_counts[status_name] += 1
        
        print("\n📊 توزيع الطلبات حسب الحالة:")
        for status_name, count in status_counts.items():
            print(f"   {status_name}: {count} طلب")
        
        # تصدير البيانات
        print(f"\n📤 تصدير بيانات الطلبات...")
        
        export_data = []
        for request in all_requests:
            export_data.append({
                'رقم_الطلب': request.id,
                'الموظف': employee.full_name_ar,
                'نوع_الإجازة': {
                    'annual': 'إجازة سنوية',
                    'sick': 'إجازة مرضية',
                    'emergency': 'إجازة طارئة',
                    'maternity': 'إجازة أمومة',
                    'administrative': 'إجازة إدارية',
                    'holiday_compensation': 'إجازة بدل أعياد',
                    'grant_compensation': 'إجازة بدل منح',
                    'casual': 'إجازة عرضية',
                    'unpaid': 'إجازة بدون راتب'
                }.get(request.leave_type, request.leave_type),
                'من_تاريخ': request.start_date.strftime('%Y-%m-%d'),
                'إلى_تاريخ': request.end_date.strftime('%Y-%m-%d'),
                'عدد_الأيام': request.days_requested,
                'السبب': request.reason,
                'الحالة': {
                    'pending': 'معلقة',
                    'approved': 'موافق عليها',
                    'rejected': 'مرفوضة',
                    'cancelled': 'ملغاة'
                }.get(request.status, request.status),
                'تاريخ_الطلب': request.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # حفظ البيانات المصدرة
        export_filename = f'test_leave_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(export_filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم تصدير {len(export_data)} طلب إجازة إلى ملف: {export_filename}")
        
        print("\n" + "=" * 60)
        print("🎯 ملخص الاختبار:")
        print(f"✅ تم إضافة {len(created_requests)} أنواع إجازات جديدة")
        print("📊 تم اختبار رصيد الإجازات")
        print("⚙️ تم اختبار إعدادات الإجازات")
        print("📈 تم إنتاج إحصائيات شاملة")
        print("💾 تم تصدير البيانات بتنسيق JSON")
        
        print("\n🌐 يمكنك الآن استخدام أنواع الإجازات الجديدة في:")
        print("   http://localhost:5000/hr/leave_requests")
        print("\n🔑 استخدم: hr_manager / 123456 للدخول")
        
        print("\n📋 أنواع الإجازات المتاحة الآن:")
        print("   1. إجازة سنوية (30 يوم)")
        print("   2. إجازة مرضية (15 يوم)")
        print("   3. إجازة طارئة (5 أيام)")
        print("   4. إجازة أمومة (90 يوم)")
        print("   5. إجازة إدارية (10 أيام) 🆕")
        print("   6. إجازة بدل أعياد (15 يوم) 🆕")
        print("   7. إجازة بدل منح (7 أيام) 🆕")
        print("   8. إجازة عرضية (12 يوم) 🆕")
        print("   9. إجازة بدون راتب (365 يوم)")

if __name__ == '__main__':
    print("🚀 بدء اختبار أنواع الإجازات الجديدة")
    print("=" * 60)
    
    test_new_leave_types()
    
    print("\n🎉 انتهى الاختبار بنجاح!")
    print("💡 تلميح: تأكد من تشغيل النظام باستخدام 'python app.py' لاختبار الواجهة")
