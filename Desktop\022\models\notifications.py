from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from app import db

class Notification(db.Model):
    """نموذج الإشعارات"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات الإشعار
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), nullable=False)  # info, success, warning, error, system
    priority = db.Column(db.String(10), default='normal')  # low, normal, high, urgent
    
    # المستخدم المستهدف
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # حالة الإشعار
    is_read = db.Column(db.<PERSON>, default=False)
    is_archived = db.Column(db.<PERSON><PERSON><PERSON>, default=False)
    
    # معلومات إضافية
    action_url = db.Column(db.String(255))  # رابط الإجراء
    action_text = db.Column(db.String(50))  # نص الإجراء
    icon = db.Column(db.String(50))  # أيقونة الإشعار
    
    # مصدر الإشعار
    source_type = db.Column(db.String(50))  # hr, accounting, sales, etc.
    source_id = db.Column(db.Integer)  # معرف المصدر
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)
    expires_at = db.Column(db.DateTime)  # تاريخ انتهاء الصلاحية
    
    # العلاقات
    user = db.relationship('User', backref='notifications')
    
    def __repr__(self):
        return f'<Notification {self.title}>'
    
    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """تحويل الإشعار إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'priority': self.priority,
            'is_read': self.is_read,
            'action_url': self.action_url,
            'action_text': self.action_text,
            'icon': self.icon,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'time_ago': self.get_time_ago()
        }
    
    def get_time_ago(self):
        """حساب الوقت المنقضي"""
        if not self.created_at:
            return 'غير محدد'
        
        now = datetime.utcnow()
        diff = now - self.created_at
        
        if diff.days > 0:
            return f'منذ {diff.days} يوم'
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f'منذ {hours} ساعة'
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f'منذ {minutes} دقيقة'
        else:
            return 'الآن'

class NotificationTemplate(db.Model):
    """قوالب الإشعارات"""
    __tablename__ = 'notification_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    title_template = db.Column(db.String(200), nullable=False)
    message_template = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), nullable=False)
    priority = db.Column(db.String(10), default='normal')
    icon = db.Column(db.String(50))
    
    # إعدادات القالب
    is_active = db.Column(db.Boolean, default=True)
    auto_send = db.Column(db.Boolean, default=False)
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<NotificationTemplate {self.name}>'

class WorkflowDefinition(db.Model):
    """تعريف سير العمل"""
    __tablename__ = 'workflow_definitions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # نوع سير العمل
    workflow_type = db.Column(db.String(50), nullable=False)  # leave_request, purchase_order, etc.
    
    # إعدادات سير العمل
    is_active = db.Column(db.Boolean, default=True)
    auto_start = db.Column(db.Boolean, default=False)
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    steps = db.relationship('WorkflowStep', backref='workflow', cascade='all, delete-orphan')
    instances = db.relationship('WorkflowInstance', backref='workflow', lazy='dynamic')
    
    def __repr__(self):
        return f'<WorkflowDefinition {self.name}>'

class WorkflowStep(db.Model):
    """خطوة في سير العمل"""
    __tablename__ = 'workflow_steps'
    
    id = db.Column(db.Integer, primary_key=True)
    workflow_id = db.Column(db.Integer, db.ForeignKey('workflow_definitions.id'), nullable=False)
    
    # معلومات الخطوة
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    step_order = db.Column(db.Integer, nullable=False)
    
    # نوع الخطوة
    step_type = db.Column(db.String(20), nullable=False)  # approval, notification, action
    
    # المسؤول عن الخطوة
    assignee_type = db.Column(db.String(20), nullable=False)  # user, role, department
    assignee_value = db.Column(db.String(100), nullable=False)
    
    # إعدادات الخطوة
    is_required = db.Column(db.Boolean, default=True)
    timeout_hours = db.Column(db.Integer, default=24)
    
    # العلاقات
    executions = db.relationship('WorkflowExecution', backref='step', lazy='dynamic')
    
    def __repr__(self):
        return f'<WorkflowStep {self.name}>'

class WorkflowInstance(db.Model):
    """مثيل سير العمل"""
    __tablename__ = 'workflow_instances'
    
    id = db.Column(db.Integer, primary_key=True)
    workflow_id = db.Column(db.Integer, db.ForeignKey('workflow_definitions.id'), nullable=False)
    
    # معلومات المثيل
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # الكائن المرتبط
    entity_type = db.Column(db.String(50), nullable=False)  # leave_request, purchase_order
    entity_id = db.Column(db.Integer, nullable=False)
    
    # حالة سير العمل
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    current_step = db.Column(db.Integer, default=1)
    
    # المستخدم المبدئ
    initiated_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # تواريخ
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # العلاقات
    initiator = db.relationship('User', backref='initiated_workflows')
    executions = db.relationship('WorkflowExecution', backref='instance', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<WorkflowInstance {self.title}>'

class WorkflowExecution(db.Model):
    """تنفيذ خطوة في سير العمل"""
    __tablename__ = 'workflow_executions'
    
    id = db.Column(db.Integer, primary_key=True)
    instance_id = db.Column(db.Integer, db.ForeignKey('workflow_instances.id'), nullable=False)
    step_id = db.Column(db.Integer, db.ForeignKey('workflow_steps.id'), nullable=False)
    
    # معلومات التنفيذ
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, completed
    comments = db.Column(db.Text)
    
    # المنفذ
    executed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # تواريخ
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    executed_at = db.Column(db.DateTime)
    due_date = db.Column(db.DateTime)
    
    # العلاقات
    executor = db.relationship('User', backref='workflow_executions')
    
    def __repr__(self):
        return f'<WorkflowExecution {self.status}>'
