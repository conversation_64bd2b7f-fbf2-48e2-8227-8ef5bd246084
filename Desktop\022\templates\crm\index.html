{% extends "base.html" %}

{% block title %}إدارة العملاء{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-handshake me-2"></i>
                        إدارة العملاء
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">إدارة العملاء</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="addNewCustomer()">
                        <i class="fas fa-plus me-2"></i>
                        عميل جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ customers|length }}</h4>
                    <p class="text-muted mb-0">إجمالي العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ active_customers }}</h4>
                    <p class="text-muted mb-0">عملاء نشطون</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-handshake fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ potential_customers }}</h4>
                    <p class="text-muted mb-0">عملاء محتملون</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-dollar-sign fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ total_revenue|number_format }}</h4>
                    <p class="text-muted mb-0">إجمالي الإيرادات (ر.ق)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchCustomers" placeholder="البحث في العملاء...">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="potential">محتمل</option>
                <option value="inactive">غير نشط</option>
                <option value="blocked">محظور</option>
            </select>
        </div>
        <div class="col-md-3">
            <div class="btn-group w-100">
                <button class="btn btn-outline-danger" onclick="deleteSelectedCustomers()" id="deleteSelectedBtn" disabled>
                    <i class="fas fa-trash me-2"></i>
                    حذف المحدد
                </button>
                <button class="btn btn-outline-primary" onclick="exportCustomers()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- جدول العملاء -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة العملاء
                </h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAllCustomers" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAllCustomers">
                        تحديد الكل
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if customers %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllHeader" onchange="toggleSelectAll()">
                            </th>
                            <th>اسم العميل</th>
                            <th>الشركة</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input customer-checkbox" value="{{ customer.id }}" onchange="updateDeleteButton()">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ customer.name[0].upper() }}
                                    </div>
                                    <div>
                                        <strong>{{ customer.name }}</strong>
                                        {% if customer.title %}
                                        <br><small class="text-muted">{{ customer.title }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ customer.company or 'غير محدد' }}</td>
                            <td>{{ customer.email or 'غير محدد' }}</td>
                            <td>{{ customer.phone or 'غير محدد' }}</td>
                            <td>
                                {% if customer.status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                                {% elif customer.status == 'potential' %}
                                <span class="badge bg-info">محتمل</span>
                                {% elif customer.status == 'inactive' %}
                                <span class="badge bg-warning">غير نشط</span>
                                {% elif customer.status == 'blocked' %}
                                <span class="badge bg-danger">محظور</span>
                                {% endif %}
                            </td>
                            <td>{{ customer.created_at.strftime('%Y-%m-%d') if customer.created_at else 'غير محدد' }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewCustomer({{ customer.id }})" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="editCustomer({{ customer.id }})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="viewInteractions({{ customer.id }})" title="التفاعلات">
                                        <i class="fas fa-comments"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="viewOrders({{ customer.id }})" title="الطلبات">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteCustomer({{ customer.id }})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد عملاء</h5>
                <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                <button class="btn btn-primary" onclick="addNewCustomer()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عميل جديد
                </button>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportCustomers()">
                                <i class="fas fa-download me-2"></i>
                                تصدير العملاء
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="generateCustomerReport()">
                                <i class="fas fa-chart-bar me-2"></i>
                                تقرير العملاء
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="bulkCustomerActions()">
                                <i class="fas fa-tasks me-2"></i>
                                إجراءات جماعية
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="customerSettings()">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات العملاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">المسمى الوظيفي</label>
                            <input type="text" class="form-control" id="customerTitle">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="companyName">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="customerStatus">
                                <option value="potential">محتمل</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="blocked">محظور</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="customerPhone">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="customerNotes" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">حفظ العميل</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let selectedCustomers = [];

// وظائف إدارة العملاء
function addNewCustomer() {
    // عرض النموذج
    new bootstrap.Modal(document.getElementById('addCustomerModal')).show();
}

function saveCustomer() {
    const form = document.getElementById('addCustomerForm');

    // التحقق من الحقول المطلوبة
    if (!document.getElementById('customerName').value.trim()) {
        alert('يرجى إدخال اسم العميل');
        return;
    }

    // جمع البيانات
    const customerData = {
        name: document.getElementById('customerName').value,
        title: document.getElementById('customerTitle').value,
        company: document.getElementById('companyName').value,
        status: document.getElementById('customerStatus').value,
        email: document.getElementById('customerEmail').value,
        phone: document.getElementById('customerPhone').value,
        address: document.getElementById('customerAddress').value,
        notes: document.getElementById('customerNotes').value
    };

    // محاكاة حفظ البيانات
    console.log('بيانات العميل:', customerData);
    alert('تم حفظ العميل بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();
    location.reload();
}

function viewCustomer(customerId) {
    alert(`عرض تفاصيل العميل رقم ${customerId}`);
}

function editCustomer(customerId) {
    alert(`تعديل العميل رقم ${customerId}`);
}

function viewInteractions(customerId) {
    alert(`عرض تفاعلات العميل رقم ${customerId}`);
}

function viewOrders(customerId) {
    alert(`عرض طلبات العميل رقم ${customerId}`);
}

function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert(`تم حذف العميل رقم ${customerId}`);
        location.reload();
    }
}

// وظائف التحديد المتعدد
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCustomers') || document.getElementById('selectAllHeader');
    const customerCheckboxes = document.querySelectorAll('.customer-checkbox');

    customerCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.customer-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    selectedCustomers = Array.from(checkedBoxes).map(cb => cb.value);

    if (selectedCustomers.length > 0) {
        deleteBtn.disabled = false;
        deleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>حذف المحدد (${selectedCustomers.length})`;
    } else {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-trash me-2"></i>حذف المحدد';
    }
}

function deleteSelectedCustomers() {
    if (selectedCustomers.length === 0) {
        alert('يرجى تحديد عملاء للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedCustomers.length} عميل؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
        alert(`تم حذف ${selectedCustomers.length} عميل بنجاح`);
        location.reload();
    }
}

// الإجراءات السريعة
function exportCustomers() {
    alert('تصدير قائمة العملاء');
}

function generateCustomerReport() {
    alert('إنتاج تقرير العملاء');
}

function bulkCustomerActions() {
    alert('الإجراءات الجماعية للعملاء');
}

function customerSettings() {
    alert('إعدادات إدارة العملاء');
}

// البحث والتصفية
document.getElementById('searchCustomers').addEventListener('input', function() {
    console.log('البحث:', this.value);
});

document.getElementById('statusFilter').addEventListener('change', function() {
    console.log('التصفية:', this.value);
});

// تحديث أزرار الحذف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDeleteButton();
});
</script>
{% endblock %}
