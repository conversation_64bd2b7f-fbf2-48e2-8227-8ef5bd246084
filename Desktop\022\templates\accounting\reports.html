{% extends "base.html" %}

{% block title %}التقارير المالية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير المالية
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('accounting.index') }}">المحاسبة</a></li>
                            <li class="breadcrumb-item active">التقارير المالية</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="generateCustomReport()">
                        <i class="fas fa-plus me-2"></i>
                        تقرير مخصص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- أنواع التقارير -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('balance_sheet')">
                <div class="card-body text-center">
                    <i class="fas fa-balance-scale fa-3x text-primary mb-3"></i>
                    <h5>الميزانية العمومية</h5>
                    <p class="text-muted">عرض الأصول والخصوم وحقوق الملكية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('income_statement')">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                    <h5>قائمة الدخل</h5>
                    <p class="text-muted">عرض الإيرادات والمصروفات والأرباح</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('cash_flow')">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-3x text-info mb-3"></i>
                    <h5>قائمة التدفقات النقدية</h5>
                    <p class="text-muted">عرض التدفقات النقدية الداخلة والخارجة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('trial_balance')">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-3x text-warning mb-3"></i>
                    <h5>ميزان المراجعة</h5>
                    <p class="text-muted">عرض أرصدة جميع الحسابات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تقارير إضافية -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('accounts_receivable')">
                <div class="card-body text-center">
                    <i class="fas fa-user-clock fa-3x text-danger mb-3"></i>
                    <h5>الذمم المدينة</h5>
                    <p class="text-muted">عرض المبالغ المستحقة من العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('accounts_payable')">
                <div class="card-body text-center">
                    <i class="fas fa-user-times fa-3x text-secondary mb-3"></i>
                    <h5>الذمم الدائنة</h5>
                    <p class="text-muted">عرض المبالغ المستحقة للموردين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('general_ledger')">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-3x text-dark mb-3"></i>
                    <h5>دفتر الأستاذ العام</h5>
                    <p class="text-muted">عرض تفاصيل حساب معين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card h-100" onclick="generateReport('journal_entries')">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
                    <h5>دفتر اليومية</h5>
                    <p class="text-muted">عرض جميع القيود اليومية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر التقارير -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر التقارير
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="fromDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="toDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع الحساب</label>
                    <select class="form-select" id="accountType">
                        <option value="">جميع الأنواع</option>
                        <option value="asset">الأصول</option>
                        <option value="liability">الخصوم</option>
                        <option value="equity">حقوق الملكية</option>
                        <option value="revenue">الإيرادات</option>
                        <option value="expense">المصروفات</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">تنسيق التصدير</label>
                    <select class="form-select" id="exportFormat">
                        <option value="pdf">PDF</option>
                        <option value="excel">Excel</option>
                        <option value="csv">CSV</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- منطقة عرض التقرير -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-pdf me-2"></i>
                    <span id="reportTitle">اختر نوع التقرير</span>
                </h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="printReport()" id="printBtn" disabled>
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportReport()" id="exportBtn" disabled>
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="reportContent" class="text-center py-5">
                <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">اختر نوع التقرير من الأعلى</h5>
                <p class="text-muted">سيتم عرض التقرير هنا بعد اختيار النوع والفترة الزمنية</p>
            </div>
        </div>
    </div>
</div>

<style>
.report-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: var(--bs-primary);
}

.report-card:hover .card-body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

#reportContent {
    min-height: 400px;
}

.report-table {
    font-size: 0.9rem;
}

.report-table th {
    background-color: #8B1538;
    color: white;
    font-weight: bold;
}

.report-summary {
    background: linear-gradient(135deg, #8B1538 0%, #A91B47 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>

<script>
// متغيرات عامة
let currentReportType = null;
let reportData = null;

// وظائف التقارير
function generateReport(reportType) {
    currentReportType = reportType;
    
    // تحديث عنوان التقرير
    const titles = {
        'balance_sheet': 'الميزانية العمومية',
        'income_statement': 'قائمة الدخل',
        'cash_flow': 'قائمة التدفقات النقدية',
        'trial_balance': 'ميزان المراجعة',
        'accounts_receivable': 'الذمم المدينة',
        'accounts_payable': 'الذمم الدائنة',
        'general_ledger': 'دفتر الأستاذ العام',
        'journal_entries': 'دفتر اليومية'
    };
    
    document.getElementById('reportTitle').textContent = titles[reportType] || 'تقرير مالي';
    
    // تفعيل أزرار الطباعة والتصدير
    document.getElementById('printBtn').disabled = false;
    document.getElementById('exportBtn').disabled = false;
    
    // محاكاة تحميل البيانات
    showLoadingReport();
    
    setTimeout(() => {
        loadReportData(reportType);
    }, 1500);
}

function showLoadingReport() {
    document.getElementById('reportContent').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <h5 class="text-muted">جاري إنتاج التقرير...</h5>
            <p class="text-muted">يرجى الانتظار</p>
        </div>
    `;
}

function loadReportData(reportType) {
    // محاكاة بيانات التقرير
    const sampleData = generateSampleReportData(reportType);
    displayReport(sampleData, reportType);
}

function generateSampleReportData(reportType) {
    // بيانات تجريبية للتقارير
    switch(reportType) {
        case 'balance_sheet':
            return {
                assets: [
                    {name: 'النقدية في الصندوق', amount: 50000},
                    {name: 'النقدية في البنك', amount: 150000},
                    {name: 'الذمم المدينة', amount: 75000},
                    {name: 'المخزون', amount: 100000}
                ],
                liabilities: [
                    {name: 'الذمم الدائنة', amount: 45000},
                    {name: 'القروض قصيرة الأجل', amount: 30000}
                ],
                equity: [
                    {name: 'رأس المال', amount: 200000},
                    {name: 'الأرباح المحتجزة', amount: 100000}
                ]
            };
        case 'income_statement':
            return {
                revenues: [
                    {name: 'إيرادات المبيعات', amount: 500000},
                    {name: 'إيرادات أخرى', amount: 25000}
                ],
                expenses: [
                    {name: 'تكلفة البضاعة المباعة', amount: 300000},
                    {name: 'مصروفات التشغيل', amount: 100000},
                    {name: 'مصروفات إدارية', amount: 50000}
                ]
            };
        default:
            return {
                items: [
                    {name: 'عنصر تجريبي 1', amount: 10000},
                    {name: 'عنصر تجريبي 2', amount: 20000},
                    {name: 'عنصر تجريبي 3', amount: 15000}
                ]
            };
    }
}

function displayReport(data, reportType) {
    let html = '';
    
    switch(reportType) {
        case 'balance_sheet':
            html = generateBalanceSheetHTML(data);
            break;
        case 'income_statement':
            html = generateIncomeStatementHTML(data);
            break;
        default:
            html = generateGenericReportHTML(data);
    }
    
    document.getElementById('reportContent').innerHTML = html;
}

function generateBalanceSheetHTML(data) {
    const totalAssets = data.assets.reduce((sum, item) => sum + item.amount, 0);
    const totalLiabilities = data.liabilities.reduce((sum, item) => sum + item.amount, 0);
    const totalEquity = data.equity.reduce((sum, item) => sum + item.amount, 0);
    
    return `
        <div class="report-summary">
            <div class="row text-center">
                <div class="col-md-4">
                    <h4>${totalAssets.toLocaleString()} ر.ق</h4>
                    <p class="mb-0">إجمالي الأصول</p>
                </div>
                <div class="col-md-4">
                    <h4>${totalLiabilities.toLocaleString()} ر.ق</h4>
                    <p class="mb-0">إجمالي الخصوم</p>
                </div>
                <div class="col-md-4">
                    <h4>${totalEquity.toLocaleString()} ر.ق</h4>
                    <p class="mb-0">حقوق الملكية</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h5 class="text-primary">الأصول</h5>
                <table class="table table-striped report-table">
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th class="text-end">المبلغ (ر.ق)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.assets.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td class="text-end">${item.amount.toLocaleString()}</td>
                            </tr>
                        `).join('')}
                        <tr class="table-primary">
                            <td><strong>إجمالي الأصول</strong></td>
                            <td class="text-end"><strong>${totalAssets.toLocaleString()}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-md-6">
                <h5 class="text-danger">الخصوم وحقوق الملكية</h5>
                <table class="table table-striped report-table">
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th class="text-end">المبلغ (ر.ق)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.liabilities.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td class="text-end">${item.amount.toLocaleString()}</td>
                            </tr>
                        `).join('')}
                        ${data.equity.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td class="text-end">${item.amount.toLocaleString()}</td>
                            </tr>
                        `).join('')}
                        <tr class="table-danger">
                            <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                            <td class="text-end"><strong>${(totalLiabilities + totalEquity).toLocaleString()}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function generateIncomeStatementHTML(data) {
    const totalRevenues = data.revenues.reduce((sum, item) => sum + item.amount, 0);
    const totalExpenses = data.expenses.reduce((sum, item) => sum + item.amount, 0);
    const netIncome = totalRevenues - totalExpenses;
    
    return `
        <div class="report-summary">
            <div class="row text-center">
                <div class="col-md-3">
                    <h4>${totalRevenues.toLocaleString()} ر.ق</h4>
                    <p class="mb-0">إجمالي الإيرادات</p>
                </div>
                <div class="col-md-3">
                    <h4>${totalExpenses.toLocaleString()} ر.ق</h4>
                    <p class="mb-0">إجمالي المصروفات</p>
                </div>
                <div class="col-md-3">
                    <h4>${netIncome.toLocaleString()} ر.ق</h4>
                    <p class="mb-0">صافي الدخل</p>
                </div>
                <div class="col-md-3">
                    <h4>${((netIncome/totalRevenues)*100).toFixed(1)}%</h4>
                    <p class="mb-0">هامش الربح</p>
                </div>
            </div>
        </div>
        
        <table class="table table-striped report-table">
            <thead>
                <tr>
                    <th>البيان</th>
                    <th class="text-end">المبلغ (ر.ق)</th>
                </tr>
            </thead>
            <tbody>
                <tr class="table-success">
                    <td><strong>الإيرادات</strong></td>
                    <td></td>
                </tr>
                ${data.revenues.map(item => `
                    <tr>
                        <td class="ps-4">${item.name}</td>
                        <td class="text-end">${item.amount.toLocaleString()}</td>
                    </tr>
                `).join('')}
                <tr class="table-success">
                    <td><strong>إجمالي الإيرادات</strong></td>
                    <td class="text-end"><strong>${totalRevenues.toLocaleString()}</strong></td>
                </tr>
                <tr class="table-danger">
                    <td><strong>المصروفات</strong></td>
                    <td></td>
                </tr>
                ${data.expenses.map(item => `
                    <tr>
                        <td class="ps-4">${item.name}</td>
                        <td class="text-end">${item.amount.toLocaleString()}</td>
                    </tr>
                `).join('')}
                <tr class="table-danger">
                    <td><strong>إجمالي المصروفات</strong></td>
                    <td class="text-end"><strong>${totalExpenses.toLocaleString()}</strong></td>
                </tr>
                <tr class="table-primary">
                    <td><strong>صافي الدخل</strong></td>
                    <td class="text-end"><strong>${netIncome.toLocaleString()}</strong></td>
                </tr>
            </tbody>
        </table>
    `;
}

function generateGenericReportHTML(data) {
    const total = data.items.reduce((sum, item) => sum + item.amount, 0);
    
    return `
        <table class="table table-striped report-table">
            <thead>
                <tr>
                    <th>البيان</th>
                    <th class="text-end">المبلغ (ر.ق)</th>
                </tr>
            </thead>
            <tbody>
                ${data.items.map(item => `
                    <tr>
                        <td>${item.name}</td>
                        <td class="text-end">${item.amount.toLocaleString()}</td>
                    </tr>
                `).join('')}
                <tr class="table-primary">
                    <td><strong>الإجمالي</strong></td>
                    <td class="text-end"><strong>${total.toLocaleString()}</strong></td>
                </tr>
            </tbody>
        </table>
    `;
}

function printReport() {
    if (!currentReportType) {
        alert('يرجى اختيار نوع التقرير أولاً');
        return;
    }
    
    window.print();
}

function exportReport() {
    if (!currentReportType) {
        alert('يرجى اختيار نوع التقرير أولاً');
        return;
    }
    
    const format = document.getElementById('exportFormat').value;
    alert(`سيتم تصدير التقرير بصيغة ${format.toUpperCase()}`);
}

function generateCustomReport() {
    alert('سيتم فتح معالج إنشاء التقارير المخصصة');
}

// تحديد التواريخ الافتراضية
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('fromDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('toDate').value = today.toISOString().split('T')[0];
});
</script>
{% endblock %}
