from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required, current_user
from datetime import datetime
from app import db

bp = Blueprint('api', __name__, url_prefix='/api')

# ===== APIs الإشعارات =====

@bp.route('/notifications')
@login_required
def get_notifications():
    """الحصول على إشعارات المستخدم"""
    try:
        # محاكاة بيانات الإشعارات
        notifications = [
            {
                'id': 1,
                'title': 'موظف جديد',
                'message': 'تم إضافة موظف جديد: أحمد محمد',
                'type': 'success',
                'time_ago': 'منذ 5 دقائق',
                'is_read': False,
                'icon': 'fas fa-user-plus',
                'created_at': datetime.utcnow().isoformat()
            },
            {
                'id': 2,
                'title': 'فاتورة جديدة',
                'message': 'فاتورة جديدة تحتاج موافقة - 15,000 ر.ق',
                'type': 'warning',
                'time_ago': 'منذ 15 دقيقة',
                'is_read': False,
                'icon': 'fas fa-file-invoice',
                'created_at': datetime.utcnow().isoformat()
            },
            {
                'id': 3,
                'title': 'تذكير اجتماع',
                'message': 'اجتماع فريق المبيعات الساعة 3:00 م',
                'type': 'info',
                'time_ago': 'منذ ساعة',
                'is_read': True,
                'icon': 'fas fa-calendar',
                'created_at': datetime.utcnow().isoformat()
            }
        ]
        
        return jsonify(notifications)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب الإشعارات: {str(e)}")
        return jsonify({'error': 'خطأ في جلب الإشعارات'}), 500

@bp.route('/notifications/count')
@login_required
def get_notifications_count():
    """الحصول على عدد الإشعارات غير المقروءة"""
    try:
        # محاكاة عدد الإشعارات غير المقروءة
        count = 2
        return jsonify({'count': count})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في عد الإشعارات: {str(e)}")
        return jsonify({'error': 'خطأ في عد الإشعارات'}), 500

@bp.route('/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    try:
        # محاكاة تحديد الإشعار كمقروء
        return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديد الإشعار كمقروء: {str(e)}")
        return jsonify({'error': 'خطأ في تحديد الإشعار كمقروء'}), 500

@bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """تحديد جميع الإشعارات كمقروءة"""
    try:
        # محاكاة تحديد جميع الإشعارات كمقروءة
        count = 2
        return jsonify({'success': True, 'message': f'تم تحديد {count} إشعار كمقروء'})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديد جميع الإشعارات كمقروءة: {str(e)}")
        return jsonify({'error': 'خطأ في تحديد الإشعارات كمقروءة'}), 500

# ===== APIs سير العمل =====

@bp.route('/workflow/tasks')
@login_required
def get_workflow_tasks():
    """الحصول على مهام سير العمل للمستخدم"""
    try:
        # محاكاة مهام سير العمل
        tasks = [
            {
                'id': 1,
                'title': 'موافقة طلب إجازة',
                'description': 'طلب إجازة - سارة أحمد',
                'workflow_title': 'سير عمل طلبات الإجازات',
                'due_date': '2024-12-20',
                'priority': 'high',
                'status': 'pending',
                'assigned_at': datetime.utcnow().isoformat(),
                'workflow_id': 1
            },
            {
                'id': 2,
                'title': 'مراجعة أمر شراء',
                'description': 'أمر شراء معدات مكتبية - 5,000 ر.ق',
                'workflow_title': 'سير عمل أوامر الشراء',
                'due_date': '2024-12-21',
                'priority': 'normal',
                'status': 'pending',
                'assigned_at': datetime.utcnow().isoformat(),
                'workflow_id': 2
            }
        ]
        
        return jsonify(tasks)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب مهام سير العمل: {str(e)}")
        return jsonify({'error': 'خطأ في جلب مهام سير العمل'}), 500

@bp.route('/workflow/task/<int:task_id>/execute', methods=['POST'])
@login_required
def execute_workflow_task(task_id):
    """تنفيذ مهمة في سير العمل"""
    try:
        data = request.get_json()
        action = data.get('action')  # approved, rejected, completed
        comments = data.get('comments', '')
        
        if action not in ['approved', 'rejected', 'completed']:
            return jsonify({'error': 'إجراء غير صحيح'}), 400
        
        # محاكاة تنفيذ المهمة
        return jsonify({
            'success': True,
            'message': f'تم {action} المهمة بنجاح',
            'status': action
        })
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تنفيذ مهمة سير العمل: {str(e)}")
        return jsonify({'error': 'خطأ في تنفيذ المهمة'}), 500

@bp.route('/workflow/instance/<int:instance_id>/status')
@login_required
def get_workflow_status(instance_id):
    """الحصول على حالة سير العمل"""
    try:
        # محاكاة حالة سير العمل
        status = {
            'instance_id': instance_id,
            'title': 'طلب إجازة - سارة أحمد',
            'status': 'in_progress',
            'current_step': 2,
            'total_steps': 3,
            'progress_percentage': 66.7,
            'started_at': datetime.utcnow().isoformat(),
            'completed_at': None
        }
        
        return jsonify(status)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب حالة سير العمل: {str(e)}")
        return jsonify({'error': 'خطأ في جلب حالة سير العمل'}), 500

# ===== APIs عامة =====

@bp.route('/system/status')
@login_required
def get_system_status():
    """الحصول على حالة النظام"""
    try:
        # إحصائيات أساسية (محاكاة)
        stats = {
            'notifications_count': 2,
            'workflow_tasks_count': 2,
            'system_uptime': '99.9%',
            'last_backup': '2024-12-19 10:00:00',
            'database_size': '2.5 MB',
            'active_users': 12
        }
        
        return jsonify(stats)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب حالة النظام: {str(e)}")
        return jsonify({'error': 'خطأ في جلب حالة النظام'}), 500

# ===== APIs الإعدادات =====

@bp.route('/settings')
@login_required
def get_settings():
    """الحصول على إعدادات النظام"""
    try:
        category = request.args.get('category', 'general')
        
        # محاكاة إعدادات النظام
        settings_data = [
            {
                'key': 'notifications_enabled',
                'value': True,
                'name_ar': 'تفعيل الإشعارات',
                'name_en': 'Enable Notifications',
                'description': 'تفعيل أو إيقاف نظام الإشعارات',
                'setting_type': 'boolean',
                'category': 'notifications',
                'is_required': True,
                'is_system': False
            },
            {
                'key': 'workflow_enabled',
                'value': True,
                'name_ar': 'تفعيل سير العمل',
                'name_en': 'Enable Workflow',
                'description': 'تفعيل أو إيقاف نظام سير العمل',
                'setting_type': 'boolean',
                'category': 'workflow',
                'is_required': True,
                'is_system': False
            }
        ]
        
        return jsonify(settings_data)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب الإعدادات: {str(e)}")
        return jsonify({'error': 'خطأ في جلب الإعدادات'}), 500

@bp.route('/settings/<setting_key>', methods=['PUT'])
@login_required
def update_setting(setting_key):
    """تحديث إعداد"""
    try:
        data = request.get_json()
        new_value = data.get('value')
        
        # محاكاة تحديث الإعداد
        return jsonify({
            'success': True,
            'message': 'تم تحديث الإعداد بنجاح',
            'value': new_value
        })
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديث الإعداد: {str(e)}")
        return jsonify({'error': 'خطأ في تحديث الإعداد'}), 500

@bp.route('/user/preferences')
@login_required
def get_user_preferences():
    """الحصول على تفضيلات المستخدم"""
    try:
        # محاكاة تفضيلات المستخدم
        preferences_data = {
            'language': 'ar',
            'theme': 'light',
            'notifications_email': True,
            'notifications_browser': True
        }
        
        return jsonify(preferences_data)
    
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب تفضيلات المستخدم: {str(e)}")
        return jsonify({'error': 'خطأ في جلب التفضيلات'}), 500

@bp.route('/user/preferences', methods=['PUT'])
@login_required
def update_user_preferences():
    """تحديث تفضيلات المستخدم"""
    try:
        data = request.get_json()
        
        # محاكاة تحديث التفضيلات
        return jsonify({'success': True, 'message': 'تم تحديث التفضيلات بنجاح'})
    
    except Exception as e:
        current_app.logger.error(f"خطأ في تحديث تفضيلات المستخدم: {str(e)}")
        return jsonify({'error': 'خطأ في تحديث التفضيلات'}), 500
