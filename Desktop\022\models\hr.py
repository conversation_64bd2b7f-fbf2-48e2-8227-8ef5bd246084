from datetime import datetime, date
from decimal import Decimal
from database_setup import db

class Employee(db.Model):
    __tablename__ = 'employees'

    id = db.Column(db.Integer, primary_key=True)
    employee_number = db.Column(db.String(20), unique=True, nullable=False)
    user_id = db.Column(db.<PERSON>, db.ForeignKey('users.id'), unique=True)

    # البيانات الشخصية
    first_name_ar = db.Column(db.String(50), nullable=False)
    last_name_ar = db.Column(db.String(50), nullable=False)
    first_name_en = db.Column(db.String(50))
    last_name_en = db.Column(db.String(50))

    # معلومات الهوية
    national_id = db.Column(db.String(20), unique=True)
    passport_number = db.Column(db.String(20))
    nationality = db.Column(db.String(50))

    # معلومات الاتصال
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    emergency_contact = db.Column(db.String(100))
    emergency_phone = db.Column(db.String(20))

    # معلومات شخصية
    birth_date = db.Column(db.Date)
    gender = db.Column(db.String(10))  # male, female
    marital_status = db.Column(db.String(20))  # single, married, divorced, widowed

    # معلومات الوظيفة
    department = db.Column(db.String(50))
    position = db.Column(db.String(50))
    hire_date = db.Column(db.Date, nullable=False)
    contract_type = db.Column(db.String(20))  # permanent, temporary, contract
    employment_status = db.Column(db.String(20), default='active')  # active, inactive, terminated

    # معلومات الراتب
    basic_salary = db.Column(db.Numeric(10, 2))
    allowances = db.Column(db.Numeric(10, 2), default=0)
    bank_account = db.Column(db.String(50))
    bank_name = db.Column(db.String(50))

    # تواريخ مهمة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    termination_date = db.Column(db.Date)

    # العلاقات
    attendance_records = db.relationship('Attendance', backref='employee', lazy='dynamic')
    payroll_records = db.relationship('Payroll', backref='employee', lazy='dynamic')
    leave_requests = db.relationship('LeaveRequest', backref='employee', lazy='dynamic')

    def __repr__(self):
        return f'<Employee {self.employee_number} - {self.first_name_ar} {self.last_name_ar}>'

    @property
    def full_name_ar(self):
        return f"{self.first_name_ar} {self.last_name_ar}"

    @property
    def full_name_en(self):
        if self.first_name_en and self.last_name_en:
            return f"{self.first_name_en} {self.last_name_en}"
        return self.full_name_ar

    def get_current_month_attendance(self):
        """الحصول على حضور الشهر الحالي"""
        today = date.today()
        start_of_month = date(today.year, today.month, 1)
        return self.attendance_records.filter(
            Attendance.date >= start_of_month,
            Attendance.date <= today
        ).all()

class Attendance(db.Model):
    __tablename__ = 'attendance'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)

    # أوقات الحضور والانصراف
    check_in = db.Column(db.Time)
    check_out = db.Column(db.Time)
    break_start = db.Column(db.Time)
    break_end = db.Column(db.Time)

    # حالة الحضور
    status = db.Column(db.String(20), default='present')  # present, absent, late, half_day, sick_leave, vacation

    # ساعات العمل
    regular_hours = db.Column(db.Numeric(4, 2), default=0)
    overtime_hours = db.Column(db.Numeric(4, 2), default=0)
    break_hours = db.Column(db.Numeric(4, 2), default=0)

    # ملاحظات
    notes = db.Column(db.Text)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Attendance {self.employee_id} - {self.date}>'

    def calculate_hours(self):
        """حساب ساعات العمل"""
        if not self.check_in or not self.check_out:
            return

        # تحويل الأوقات إلى دقائق
        check_in_minutes = self.check_in.hour * 60 + self.check_in.minute
        check_out_minutes = self.check_out.hour * 60 + self.check_out.minute

        # حساب إجمالي الدقائق
        total_minutes = check_out_minutes - check_in_minutes

        # طرح وقت الاستراحة
        if self.break_start and self.break_end:
            break_start_minutes = self.break_start.hour * 60 + self.break_start.minute
            break_end_minutes = self.break_end.hour * 60 + self.break_end.minute
            break_minutes = break_end_minutes - break_start_minutes
            total_minutes -= break_minutes
            self.break_hours = break_minutes / 60

        # تحويل إلى ساعات
        total_hours = total_minutes / 60

        # تحديد الساعات العادية والإضافية
        standard_hours = 8  # ساعات العمل القياسية
        if total_hours <= standard_hours:
            self.regular_hours = total_hours
            self.overtime_hours = 0
        else:
            self.regular_hours = standard_hours
            self.overtime_hours = total_hours - standard_hours

class Payroll(db.Model):
    __tablename__ = 'payroll'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)

    # فترة الراتب
    pay_period_start = db.Column(db.Date, nullable=False)
    pay_period_end = db.Column(db.Date, nullable=False)
    pay_date = db.Column(db.Date)

    # مكونات الراتب
    basic_salary = db.Column(db.Numeric(10, 2), nullable=False)
    allowances = db.Column(db.Numeric(10, 2), default=0)
    overtime_amount = db.Column(db.Numeric(10, 2), default=0)
    bonus = db.Column(db.Numeric(10, 2), default=0)

    # الخصومات
    tax_deduction = db.Column(db.Numeric(10, 2), default=0)
    insurance_deduction = db.Column(db.Numeric(10, 2), default=0)
    loan_deduction = db.Column(db.Numeric(10, 2), default=0)
    other_deductions = db.Column(db.Numeric(10, 2), default=0)

    # المجاميع
    gross_salary = db.Column(db.Numeric(10, 2))
    total_deductions = db.Column(db.Numeric(10, 2))
    net_salary = db.Column(db.Numeric(10, 2))

    # حالة الراتب
    status = db.Column(db.String(20), default='draft')  # draft, approved, paid

    # معلومات إضافية
    notes = db.Column(db.Text)
    processed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Payroll {self.employee_id} - {self.pay_period_start}>'

    def calculate_totals(self):
        """حساب إجماليات الراتب"""
        self.gross_salary = (self.basic_salary + self.allowances +
                           self.overtime_amount + self.bonus)
        self.total_deductions = (self.tax_deduction + self.insurance_deduction +
                               self.loan_deduction + self.other_deductions)
        self.net_salary = self.gross_salary - self.total_deductions

class LeaveRequest(db.Model):
    __tablename__ = 'leave_requests'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)

    # تفاصيل الإجازة
    leave_type = db.Column(db.String(20), nullable=False)  # annual, sick, emergency, maternity, unpaid
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_requested = db.Column(db.Integer, nullable=False)

    # السبب والوصف
    reason = db.Column(db.Text, nullable=False)
    attachment = db.Column(db.String(255))  # مرفق طبي أو غيره

    # حالة الطلب
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, cancelled

    # معلومات الموافقة
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    review_date = db.Column(db.DateTime)
    review_notes = db.Column(db.Text)

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<LeaveRequest {self.employee_id} - {self.leave_type}>'
