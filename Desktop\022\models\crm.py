from datetime import datetime
from database_setup import db

class Lead(db.Model):
    __tablename__ = 'leads'

    id = db.<PERSON>umn(db.Integer, primary_key=True)

    # معلومات العميل المحتمل
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    company = db.Column(db.String(100))
    position = db.Column(db.String(50))

    # معلومات الاتصال
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)

    # معلومات الفرصة
    source = db.Column(db.String(50))  # website, referral, cold_call, social_media, exhibition
    status = db.Column(db.String(20), default='new')  # new, contacted, qualified, proposal, negotiation, won, lost
    priority = db.Column(db.String(10), default='medium')  # low, medium, high

    # القيمة المتوقعة
    estimated_value = db.Column(db.Numeric(12, 2))
    probability = db.Column(db.Integer, default=50)  # نسبة احتمالية الإغلاق
    expected_close_date = db.Column(db.Date)

    # المسؤول عن المتابعة
    assigned_to = db.Column(db.Integer, db.ForeignKey('employees.id'))

    # معلومات إضافية
    notes = db.Column(db.Text)
    last_contact_date = db.Column(db.Date)
    next_follow_up = db.Column(db.Date)

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    assignee = db.relationship('Employee', backref='assigned_leads')
    creator = db.relationship('User')
    activities = db.relationship('Activity', backref='lead', lazy='dynamic')

    def __repr__(self):
        return f'<Lead {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def convert_to_customer(self):
        """تحويل العميل المحتمل إلى عميل فعلي"""
        from models.sales import Customer

        customer = Customer(
            name_ar=self.full_name,
            name_en=self.full_name,
            phone=self.phone,
            email=self.email,
            address=self.address,
            customer_type='individual' if not self.company else 'company'
        )

        # إنشاء كود العميل
        customer_count = Customer.query.count() + 1
        customer.code = f"CUST{customer_count:06d}"

        db.session.add(customer)

        # تحديث حالة العميل المحتمل
        self.status = 'won'

        return customer

class Contact(db.Model):
    __tablename__ = 'contacts'

    id = db.Column(db.Integer, primary_key=True)

    # معلومات جهة الاتصال
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    company = db.Column(db.String(100))
    position = db.Column(db.String(50))
    department = db.Column(db.String(50))

    # معلومات الاتصال
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    email = db.Column(db.String(120))
    website = db.Column(db.String(255))
    address = db.Column(db.Text)

    # تصنيف جهة الاتصال
    contact_type = db.Column(db.String(20), default='prospect')  # prospect, customer, supplier, partner
    category = db.Column(db.String(50))

    # معلومات إضافية
    birth_date = db.Column(db.Date)
    notes = db.Column(db.Text)

    # حالة جهة الاتصال
    is_active = db.Column(db.Boolean, default=True)

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    creator = db.relationship('User')
    activities = db.relationship('Activity', backref='contact', lazy='dynamic')

    def __repr__(self):
        return f'<Contact {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

class Activity(db.Model):
    __tablename__ = 'activities'

    id = db.Column(db.Integer, primary_key=True)

    # نوع النشاط
    activity_type = db.Column(db.String(20), nullable=False)  # call, email, meeting, task, note
    subject = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)

    # التوقيت
    activity_date = db.Column(db.DateTime, nullable=False)
    duration = db.Column(db.Integer)  # بالدقائق

    # الحالة
    status = db.Column(db.String(20), default='planned')  # planned, completed, cancelled
    priority = db.Column(db.String(10), default='medium')  # low, medium, high

    # الربط مع العملاء المحتملين أو جهات الاتصال
    lead_id = db.Column(db.Integer, db.ForeignKey('leads.id'))
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'))

    # المسؤول عن النشاط
    assigned_to = db.Column(db.Integer, db.ForeignKey('employees.id'))

    # معلومات إضافية
    location = db.Column(db.String(255))
    outcome = db.Column(db.Text)  # نتيجة النشاط
    next_action = db.Column(db.Text)  # الإجراء التالي

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    assignee = db.relationship('Employee', backref='assigned_activities')
    creator = db.relationship('User')

    def __repr__(self):
        return f'<Activity {self.activity_type} - {self.subject}>'

    def mark_completed(self, outcome=None):
        """تحديد النشاط كمكتمل"""
        self.status = 'completed'
        if outcome:
            self.outcome = outcome

        # تحديث تاريخ آخر اتصال للعميل المحتمل أو جهة الاتصال
        if self.lead:
            self.lead.last_contact_date = self.activity_date.date()
        elif self.contact:
            # يمكن إضافة حقل last_contact_date لجهات الاتصال
            pass
