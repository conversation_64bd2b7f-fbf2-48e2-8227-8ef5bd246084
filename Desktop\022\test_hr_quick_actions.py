#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإجراءات السريعة لوحدة الموارد البشرية
"""

from app import app
from database_setup import db
from models.hr import Employee, LeaveRequest
from datetime import datetime, date, timedelta
import requests
import json

def test_hr_quick_actions():
    """اختبار الإجراءات السريعة للموارد البشرية"""
    
    print("🧪 اختبار الإجراءات السريعة لوحدة الموارد البشرية...")
    print("=" * 60)
    
    # تشغيل الخادم في الخلفية للاختبار
    base_url = "http://localhost:5000"
    
    # اختبار تسجيل الدخول أولاً
    session = requests.Session()
    
    # محاولة الوصول للصفحة الرئيسية
    try:
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
        else:
            print("❌ مشكلة في الوصول للخادم")
            return
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل النظام أولاً")
        print("   استخدم: python app.py")
        return
    
    print("\n📊 اختبار API الموارد البشرية...")
    
    # اختبار تقرير الإجازات
    print("\n1️⃣ اختبار تقرير الإجازات...")
    try:
        response = session.get(f"{base_url}/hr/api/leave_report")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ تقرير الإجازات يعمل بشكل صحيح")
                report_data = data.get('data', {})
                summary = report_data.get('summary', {})
                print(f"   📈 إجمالي الطلبات: {summary.get('total_requests', 0)}")
                print(f"   ⏳ طلبات معلقة: {summary.get('pending_requests', 0)}")
                print(f"   ✅ طلبات موافق عليها: {summary.get('approved_requests', 0)}")
                print(f"   📊 معدل الموافقة: {summary.get('approval_rate', 0)}%")
            else:
                print("❌ خطأ في تقرير الإجازات")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في اختبار تقرير الإجازات: {e}")
    
    # اختبار تصدير البيانات
    print("\n2️⃣ اختبار تصدير بيانات الإجازات...")
    try:
        response = session.get(f"{base_url}/hr/api/export_leaves")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ تصدير البيانات يعمل بشكل صحيح")
                print(f"   📁 اسم الملف: {data.get('filename')}")
                print(f"   📊 عدد السجلات: {data.get('total_records', 0)}")
                
                # عرض عينة من البيانات
                export_data = data.get('data', [])
                if export_data:
                    print("   📋 عينة من البيانات:")
                    sample = export_data[0]
                    print(f"      - الموظف: {sample.get('اسم_الموظف')}")
                    print(f"      - نوع الإجازة: {sample.get('نوع_الإجازة')}")
                    print(f"      - عدد الأيام: {sample.get('عدد_الأيام')}")
            else:
                print("❌ خطأ في تصدير البيانات")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في اختبار تصدير البيانات: {e}")
    
    # اختبار إعدادات الإجازات
    print("\n3️⃣ اختبار إعدادات الإجازات...")
    try:
        response = session.get(f"{base_url}/hr/api/leave_settings")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ إعدادات الإجازات تعمل بشكل صحيح")
                settings = data.get('settings', {})
                print(f"   📅 أيام الإجازة السنوية: {settings.get('annual_leave_days')}")
                print(f"   🏥 أيام الإجازة المرضية: {settings.get('sick_leave_days')}")
                print(f"   👶 أيام إجازة الأمومة: {settings.get('maternity_leave_days')}")
                print(f"   🚨 أيام الإجازة الطارئة: {settings.get('emergency_leave_days')}")
            else:
                print("❌ خطأ في إعدادات الإجازات")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات الإجازات: {e}")
    
    # اختبار رصيد إجازات الموظف
    print("\n4️⃣ اختبار رصيد إجازات الموظف...")
    try:
        # جلب أول موظف للاختبار
        with app.app_context():
            first_employee = Employee.query.first()
            if first_employee:
                employee_id = first_employee.id
                response = session.get(f"{base_url}/hr/api/employee_leave_balance/{employee_id}")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print("✅ رصيد إجازات الموظف يعمل بشكل صحيح")
                        employee_data = data.get('employee', {})
                        balances = data.get('balances', {})
                        print(f"   👤 الموظف: {employee_data.get('name')}")
                        print(f"   🆔 رقم الموظف: {employee_data.get('employee_number')}")
                        print(f"   📅 السنة: {data.get('year')}")
                        print("   💼 أرصدة الإجازات:")
                        for leave_type, balance in balances.items():
                            print(f"      - {balance.get('type_name')}: متاح {balance.get('available')} | مستخدم {balance.get('used')} | متبقي {balance.get('remaining')}")
                    else:
                        print("❌ خطأ في رصيد إجازات الموظف")
                else:
                    print(f"❌ خطأ HTTP: {response.status_code}")
            else:
                print("❌ لا يوجد موظفين في النظام للاختبار")
    except Exception as e:
        print(f"❌ خطأ في اختبار رصيد إجازات الموظف: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 ملخص الاختبار:")
    print("✅ تم اختبار جميع الإجراءات السريعة للموارد البشرية")
    print("📊 تقرير الإجازات - متاح ويعمل")
    print("📁 تصدير البيانات - متاح ويعمل") 
    print("⚙️ إعدادات الإجازات - متاحة وتعمل")
    print("💰 رصيد إجازات الموظف - متاح ويعمل")
    print("\n🌐 يمكنك الآن استخدام جميع الإجراءات السريعة في:")
    print("   http://localhost:5000/hr/leave_requests")
    print("\n🔑 استخدم: hr_manager / 123456 للدخول")

def test_database_data():
    """اختبار البيانات في قاعدة البيانات"""
    
    print("\n🗄️ اختبار البيانات في قاعدة البيانات...")
    
    with app.app_context():
        # عدد الموظفين
        employees_count = Employee.query.count()
        print(f"👥 عدد الموظفين: {employees_count}")
        
        # عدد طلبات الإجازات
        leave_requests_count = LeaveRequest.query.count()
        print(f"🏖️ عدد طلبات الإجازات: {leave_requests_count}")
        
        # طلبات معلقة
        pending_requests = LeaveRequest.query.filter_by(status='pending').count()
        print(f"⏳ طلبات معلقة: {pending_requests}")
        
        # طلبات موافق عليها
        approved_requests = LeaveRequest.query.filter_by(status='approved').count()
        print(f"✅ طلبات موافق عليها: {approved_requests}")
        
        # أنواع الإجازات
        leave_types = db.session.query(LeaveRequest.leave_type).distinct().all()
        print(f"📋 أنواع الإجازات المتاحة: {[lt[0] for lt in leave_types]}")

if __name__ == '__main__':
    print("🚀 بدء اختبار الإجراءات السريعة للموارد البشرية")
    print("=" * 60)
    
    # اختبار البيانات أولاً
    test_database_data()
    
    # ثم اختبار API
    test_hr_quick_actions()
    
    print("\n🎉 انتهى الاختبار بنجاح!")
    print("💡 تلميح: تأكد من تشغيل النظام باستخدام 'python app.py' قبل الاختبار")
