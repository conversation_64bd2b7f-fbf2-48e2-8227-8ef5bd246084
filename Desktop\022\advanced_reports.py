#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المتقدمة - نظام إدارة الموارد المتكامل
دولة قطر 🇶🇦
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from database_setup import db
from models.user import User
from models.hr import Employee, Attendance, Payroll
from models.accounting import Account, JournalEntry, Transaction
from models.inventory import Product, StockMovement
from models.sales import Customer, SalesOrder, Invoice
from models.procurement import Supplier, PurchaseOrder
from models.projects import Project, Task
from models.crm import Lead, Contact, Activity
import json
from datetime import datetime, timedelta
import io
import base64
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import pandas as pd
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors

# إنشاء Blueprint للتقارير المتقدمة
reports_bp = Blueprint('reports', __name__, url_prefix='/reports')

@reports_bp.route('/')
@login_required
def dashboard():
    """لوحة التقارير الرئيسية"""
    # إحصائيات سريعة
    stats = {
        'total_reports': 25,
        'scheduled_reports': 8,
        'automated_reports': 12,
        'custom_reports': 5
    }
    
    # التقارير الأكثر استخداماً
    popular_reports = [
        {'name': 'تقرير الرواتب الشهري', 'usage': 45, 'module': 'HR'},
        {'name': 'الميزانية العمومية', 'usage': 38, 'module': 'المحاسبة'},
        {'name': 'تقرير المبيعات اليومي', 'usage': 32, 'module': 'المبيعات'},
        {'name': 'حركة المخزون', 'usage': 28, 'module': 'المخزون'},
        {'name': 'تقرير المشاريع', 'usage': 22, 'module': 'المشاريع'}
    ]
    
    return render_template('reports/dashboard.html', stats=stats, popular_reports=popular_reports)

@reports_bp.route('/financial')
@login_required
def financial_reports():
    """التقارير المالية المتقدمة"""
    return render_template('reports/financial.html')

@reports_bp.route('/hr')
@login_required
def hr_reports():
    """تقارير الموارد البشرية"""
    return render_template('reports/hr.html')

@reports_bp.route('/operational')
@login_required
def operational_reports():
    """التقارير التشغيلية"""
    return render_template('reports/operational.html')

@reports_bp.route('/custom')
@login_required
def custom_reports():
    """منشئ التقارير المخصصة"""
    return render_template('reports/custom.html')

@reports_bp.route('/analytics')
@login_required
def analytics():
    """تحليلات البيانات المتقدمة"""
    return render_template('reports/analytics.html')

# API endpoints للتقارير
@reports_bp.route('/api/generate/<report_type>')
@login_required
def generate_report(report_type):
    """إنتاج التقارير"""
    try:
        if report_type == 'employee_summary':
            return generate_employee_summary()
        elif report_type == 'financial_overview':
            return generate_financial_overview()
        elif report_type == 'sales_analysis':
            return generate_sales_analysis()
        elif report_type == 'inventory_status':
            return generate_inventory_status()
        elif report_type == 'project_progress':
            return generate_project_progress()
        else:
            return jsonify({'error': 'نوع التقرير غير مدعوم'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def generate_employee_summary():
    """تقرير ملخص الموظفين"""
    try:
        employees = Employee.query.all()
        
        # إحصائيات الموظفين
        total_employees = len(employees)
        active_employees = len([e for e in employees if e.is_active])
        departments = {}
        
        for emp in employees:
            dept = emp.department or 'غير محدد'
            departments[dept] = departments.get(dept, 0) + 1
        
        # إنشاء الرسم البياني
        plt.figure(figsize=(10, 6))
        plt.bar(departments.keys(), departments.values(), color='#8B1538')
        plt.title('توزيع الموظفين حسب الأقسام', fontsize=16)
        plt.xlabel('الأقسام')
        plt.ylabel('عدد الموظفين')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # حفظ الرسم البياني
        img = io.BytesIO()
        plt.savefig(img, format='png', dpi=150, bbox_inches='tight')
        img.seek(0)
        chart_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        data = {
            'total_employees': total_employees,
            'active_employees': active_employees,
            'departments': departments,
            'chart': chart_url,
            'employees': [
                {
                    'name': f"{emp.first_name} {emp.last_name}",
                    'department': emp.department,
                    'position': emp.position,
                    'hire_date': emp.hire_date.strftime('%Y-%m-%d') if emp.hire_date else 'غير محدد',
                    'status': 'نشط' if emp.is_active else 'غير نشط'
                }
                for emp in employees[:20]  # أول 20 موظف
            ]
        }
        
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': f'خطأ في إنتاج تقرير الموظفين: {str(e)}'}), 500

def generate_financial_overview():
    """نظرة عامة مالية"""
    try:
        accounts = Account.query.filter_by(is_active=True).all()
        
        # تصنيف الحسابات
        account_types = {}
        total_balance = 0
        
        for account in accounts:
            acc_type = account.account_type
            balance = float(account.current_balance or 0)
            
            if acc_type not in account_types:
                account_types[acc_type] = {'count': 0, 'balance': 0}
            
            account_types[acc_type]['count'] += 1
            account_types[acc_type]['balance'] += balance
            total_balance += balance
        
        # إنشاء رسم بياني دائري
        plt.figure(figsize=(10, 8))
        labels = []
        sizes = []
        colors_list = ['#8B1538', '#A91B47', '#5D0E26', '#F8F9FA', '#E9ECEF']
        
        for acc_type, data in account_types.items():
            type_names = {
                'asset': 'الأصول',
                'liability': 'الخصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'الإيرادات',
                'expense': 'المصروفات'
            }
            labels.append(type_names.get(acc_type, acc_type))
            sizes.append(abs(data['balance']))
        
        plt.pie(sizes, labels=labels, colors=colors_list[:len(labels)], autopct='%1.1f%%', startangle=90)
        plt.title('توزيع الأرصدة حسب نوع الحساب', fontsize=16)
        plt.axis('equal')
        
        # حفظ الرسم البياني
        img = io.BytesIO()
        plt.savefig(img, format='png', dpi=150, bbox_inches='tight')
        img.seek(0)
        chart_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        data = {
            'total_accounts': len(accounts),
            'total_balance': total_balance,
            'account_types': account_types,
            'chart': chart_url,
            'accounts': [
                {
                    'code': acc.code,
                    'name': acc.name_ar,
                    'type': acc.account_type,
                    'balance': float(acc.current_balance or 0)
                }
                for acc in accounts[:15]  # أول 15 حساب
            ]
        }
        
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': f'خطأ في إنتاج النظرة المالية: {str(e)}'}), 500

def generate_sales_analysis():
    """تحليل المبيعات"""
    try:
        orders = SalesOrder.query.all()
        customers = Customer.query.all()
        
        # إحصائيات المبيعات
        total_orders = len(orders)
        total_customers = len(customers)
        total_revenue = sum(float(order.total_amount or 0) for order in orders)
        
        # المبيعات حسب الشهر (آخر 6 أشهر)
        monthly_sales = {}
        for i in range(6):
            month_date = datetime.now() - timedelta(days=30*i)
            month_key = month_date.strftime('%Y-%m')
            monthly_sales[month_key] = 0
        
        for order in orders:
            if order.order_date:
                month_key = order.order_date.strftime('%Y-%m')
                if month_key in monthly_sales:
                    monthly_sales[month_key] += float(order.total_amount or 0)
        
        # إنشاء رسم بياني خطي
        plt.figure(figsize=(12, 6))
        months = list(monthly_sales.keys())
        values = list(monthly_sales.values())
        
        plt.plot(months, values, marker='o', linewidth=2, color='#8B1538')
        plt.title('اتجاه المبيعات - آخر 6 أشهر', fontsize=16)
        plt.xlabel('الشهر')
        plt.ylabel('المبيعات (ر.ق)')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # حفظ الرسم البياني
        img = io.BytesIO()
        plt.savefig(img, format='png', dpi=150, bbox_inches='tight')
        img.seek(0)
        chart_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        data = {
            'total_orders': total_orders,
            'total_customers': total_customers,
            'total_revenue': total_revenue,
            'monthly_sales': monthly_sales,
            'chart': chart_url,
            'top_customers': [
                {
                    'name': customer.name_ar,
                    'orders_count': len([o for o in orders if o.customer_id == customer.id]),
                    'total_spent': sum(float(o.total_amount or 0) for o in orders if o.customer_id == customer.id)
                }
                for customer in customers[:10]  # أفضل 10 عملاء
            ]
        }
        
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': f'خطأ في تحليل المبيعات: {str(e)}'}), 500

def generate_inventory_status():
    """حالة المخزون"""
    try:
        products = Product.query.all()
        
        # إحصائيات المخزون
        total_products = len(products)
        low_stock_products = len([p for p in products if (p.current_stock or 0) <= (p.minimum_stock or 0)])
        out_of_stock = len([p for p in products if (p.current_stock or 0) == 0])
        
        # قيمة المخزون
        total_value = sum((p.current_stock or 0) * (p.unit_price or 0) for p in products)
        
        # المنتجات حسب الفئة
        categories = {}
        for product in products:
            category = product.category or 'غير مصنف'
            if category not in categories:
                categories[category] = {'count': 0, 'value': 0}
            categories[category]['count'] += 1
            categories[category]['value'] += (product.current_stock or 0) * (product.unit_price or 0)
        
        # إنشاء رسم بياني
        plt.figure(figsize=(10, 6))
        cat_names = list(categories.keys())
        cat_counts = [categories[cat]['count'] for cat in cat_names]
        
        plt.bar(cat_names, cat_counts, color='#8B1538')
        plt.title('توزيع المنتجات حسب الفئة', fontsize=16)
        plt.xlabel('الفئة')
        plt.ylabel('عدد المنتجات')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # حفظ الرسم البياني
        img = io.BytesIO()
        plt.savefig(img, format='png', dpi=150, bbox_inches='tight')
        img.seek(0)
        chart_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        data = {
            'total_products': total_products,
            'low_stock_products': low_stock_products,
            'out_of_stock': out_of_stock,
            'total_value': total_value,
            'categories': categories,
            'chart': chart_url,
            'products': [
                {
                    'name': product.name_ar,
                    'sku': product.sku,
                    'current_stock': product.current_stock or 0,
                    'minimum_stock': product.minimum_stock or 0,
                    'unit_price': float(product.unit_price or 0),
                    'status': 'نفد' if (product.current_stock or 0) == 0 else 
                             'منخفض' if (product.current_stock or 0) <= (product.minimum_stock or 0) else 'متوفر'
                }
                for product in products[:20]  # أول 20 منتج
            ]
        }
        
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': f'خطأ في تقرير المخزون: {str(e)}'}), 500

def generate_project_progress():
    """تقدم المشاريع"""
    try:
        projects = Project.query.all()
        
        # إحصائيات المشاريع
        total_projects = len(projects)
        active_projects = len([p for p in projects if p.status == 'active'])
        completed_projects = len([p for p in projects if p.status == 'completed'])
        
        # المشاريع حسب الحالة
        status_counts = {}
        for project in projects:
            status = project.status or 'غير محدد'
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # إنشاء رسم بياني دائري
        plt.figure(figsize=(8, 8))
        labels = []
        sizes = []
        colors_list = ['#8B1538', '#A91B47', '#5D0E26', '#F8F9FA']
        
        status_names = {
            'active': 'نشط',
            'completed': 'مكتمل',
            'on_hold': 'معلق',
            'cancelled': 'ملغي'
        }
        
        for status, count in status_counts.items():
            labels.append(status_names.get(status, status))
            sizes.append(count)
        
        plt.pie(sizes, labels=labels, colors=colors_list[:len(labels)], autopct='%1.1f%%', startangle=90)
        plt.title('توزيع المشاريع حسب الحالة', fontsize=16)
        plt.axis('equal')
        
        # حفظ الرسم البياني
        img = io.BytesIO()
        plt.savefig(img, format='png', dpi=150, bbox_inches='tight')
        img.seek(0)
        chart_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        data = {
            'total_projects': total_projects,
            'active_projects': active_projects,
            'completed_projects': completed_projects,
            'status_counts': status_counts,
            'chart': chart_url,
            'projects': [
                {
                    'name': project.name,
                    'status': project.status,
                    'start_date': project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد',
                    'end_date': project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد',
                    'budget': float(project.budget or 0),
                    'progress': project.progress or 0
                }
                for project in projects[:15]  # أول 15 مشروع
            ]
        }
        
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': f'خطأ في تقرير المشاريع: {str(e)}'}), 500

@reports_bp.route('/export/<report_type>/<format>')
@login_required
def export_report(report_type, format):
    """تصدير التقارير"""
    try:
        if format == 'pdf':
            return export_to_pdf(report_type)
        elif format == 'excel':
            return export_to_excel(report_type)
        elif format == 'csv':
            return export_to_csv(report_type)
        else:
            flash('تنسيق التصدير غير مدعوم', 'error')
            return redirect(url_for('reports.dashboard'))
    except Exception as e:
        flash(f'خطأ في تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('reports.dashboard'))

def export_to_pdf(report_type):
    """تصدير إلى PDF"""
    # محاكاة تصدير PDF
    flash(f'تم تصدير تقرير {report_type} إلى PDF بنجاح', 'success')
    return redirect(url_for('reports.dashboard'))

def export_to_excel(report_type):
    """تصدير إلى Excel"""
    # محاكاة تصدير Excel
    flash(f'تم تصدير تقرير {report_type} إلى Excel بنجاح', 'success')
    return redirect(url_for('reports.dashboard'))

def export_to_csv(report_type):
    """تصدير إلى CSV"""
    # محاكاة تصدير CSV
    flash(f'تم تصدير تقرير {report_type} إلى CSV بنجاح', 'success')
    return redirect(url_for('reports.dashboard'))
