{% extends "base.html" %}

{% block title %}طلبات الإجازات - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-calendar-alt me-2 text-primary"></i>
            طلبات الإجازات
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('hr.index') }}">الموارد البشرية</a></li>
                <li class="breadcrumb-item active">طلبات الإجازات</li>
            </ol>
        </nav>
    </div>
</div>

<!-- إحصائيات الإجازات -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-clock fa-2x text-warning"></i>
                </div>
                <h3 class="text-warning">{{ requests|selectattr("status", "equalto", "pending")|list|length }}</h3>
                <p class="text-muted mb-0">في انتظار الموافقة</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-2x text-success"></i>
                </div>
                <h3 class="text-success">{{ requests|selectattr("status", "equalto", "approved")|list|length }}</h3>
                <p class="text-muted mb-0">موافق عليها</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                </div>
                <h3 class="text-danger">{{ requests|selectattr("status", "equalto", "rejected")|list|length }}</h3>
                <p class="text-muted mb-0">مرفوضة</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-calendar-day fa-2x text-info"></i>
                </div>
                <h3 class="text-info">
                    {% set total_days = requests|selectattr("status", "equalto", "approved")|map(attribute="days_requested")|sum %}
                    {{ total_days }}
                </h3>
                <p class="text-muted mb-0">إجمالي أيام الإجازات</p>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التحكم -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">نوع الإجازة</label>
                        <select class="form-select" id="leaveTypeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="annual">إجازة سنوية</option>
                            <option value="sick">إجازة مرضية</option>
                            <option value="emergency">إجازة طارئة</option>
                            <option value="maternity">إجازة أمومة</option>
                            <option value="administrative">إجازة إدارية</option>
                            <option value="holiday_compensation">إجازة بدل أعياد</option>
                            <option value="grant_compensation">إجازة بدل منح</option>
                            <option value="casual">إجازة عرضية</option>
                            <option value="unpaid">إجازة بدون راتب</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلقة</option>
                            <option value="approved">موافق عليها</option>
                            <option value="rejected">مرفوضة</option>
                            <option value="cancelled">ملغاة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">القسم</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                            <option value="hr">الموارد البشرية</option>
                            <option value="finance">المالية</option>
                            <option value="it">تقنية المعلومات</option>
                            <option value="operations">العمليات</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="fromDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="newLeaveRequest()">
                                <i class="fas fa-plus me-2"></i>
                                طلب إجازة جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول طلبات الإجازات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                طلبات الإجازات ({{ requests|length if requests else 0 }} طلب)
            </div>
            <div class="card-body">
                {% if requests %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الإجازة</th>
                                <th>من تاريخ</th>
                                <th>إلى تاريخ</th>
                                <th>عدد الأيام</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in requests %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            {{ request.employee.first_name_ar[0] if request.employee.first_name_ar else 'م' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ request.employee.full_name_ar }}</div>
                                            <small class="text-muted">{{ request.employee.employee_number }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if request.leave_type == 'annual' %}
                                    <span class="badge bg-primary">إجازة سنوية</span>
                                    {% elif request.leave_type == 'sick' %}
                                    <span class="badge bg-warning">إجازة مرضية</span>
                                    {% elif request.leave_type == 'emergency' %}
                                    <span class="badge bg-danger">إجازة طارئة</span>
                                    {% elif request.leave_type == 'maternity' %}
                                    <span class="badge bg-info">إجازة أمومة</span>
                                    {% elif request.leave_type == 'administrative' %}
                                    <span class="badge bg-success">إجازة إدارية</span>
                                    {% elif request.leave_type == 'holiday_compensation' %}
                                    <span class="badge bg-purple">إجازة بدل أعياد</span>
                                    {% elif request.leave_type == 'grant_compensation' %}
                                    <span class="badge bg-teal">إجازة بدل منح</span>
                                    {% elif request.leave_type == 'casual' %}
                                    <span class="badge bg-orange">إجازة عرضية</span>
                                    {% elif request.leave_type == 'unpaid' %}
                                    <span class="badge bg-secondary">بدون راتب</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ request.start_date.strftime('%Y-%m-%d') }}</strong>
                                </td>
                                <td>
                                    <strong>{{ request.end_date.strftime('%Y-%m-%d') }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info fs-6">{{ request.days_requested }} يوم</span>
                                </td>
                                <td>
                                    <span class="text-truncate" style="max-width: 150px;" title="{{ request.reason }}">
                                        {{ request.reason[:50] }}{% if request.reason|length > 50 %}...{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if request.status == 'pending' %}
                                    <span class="badge bg-warning">معلقة</span>
                                    {% elif request.status == 'approved' %}
                                    <span class="badge bg-success">موافق عليها</span>
                                    {% elif request.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوضة</span>
                                    {% elif request.status == 'cancelled' %}
                                    <span class="badge bg-secondary">ملغاة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ request.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل" onclick="viewRequest({{ request.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-info" title="رصيد الإجازات" onclick="showEmployeeBalance({{ request.employee.id }})">
                                            <i class="fas fa-calendar-check"></i>
                                        </button>
                                        {% if request.status == 'pending' %}
                                        <button class="btn btn-outline-success" title="موافقة" onclick="approveRequest({{ request.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="رفض" onclick="rejectRequest({{ request.id }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        {% if request.attachment %}
                                        <button class="btn btn-outline-info" title="عرض المرفق" onclick="viewAttachment('{{ request.attachment }}')">
                                            <i class="fas fa-paperclip"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-calendar-alt"></i>
                    <h5 class="text-muted">لا يوجد طلبات إجازات</h5>
                    <p class="text-muted">لم يتم تقديم أي طلبات إجازة بعد</p>
                    <button class="btn btn-primary" onclick="newLeaveRequest()">
                        <i class="fas fa-plus me-2"></i>
                        طلب إجازة جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="approveAllPending()">
                            <i class="fas fa-check-double me-2"></i>
                            موافقة جماعية
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="leaveReport()">
                            <i class="fas fa-chart-line me-2"></i>
                            تقرير الإجازات
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="exportLeaves()">
                            <i class="fas fa-download me-2"></i>
                            تصدير البيانات
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="leaveSettings()">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات الإجازات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لطلب إجازة جديد -->
<div class="modal fade" id="leaveModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">طلب إجازة جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="leaveForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الموظف</label>
                            <select class="form-select" id="employeeSelect" required>
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع الإجازة</label>
                            <select class="form-select" id="leaveType" required>
                                <option value="">اختر نوع الإجازة</option>
                                <option value="annual">إجازة سنوية</option>
                                <option value="sick">إجازة مرضية</option>
                                <option value="emergency">إجازة طارئة</option>
                                <option value="maternity">إجازة أمومة</option>
                                <option value="administrative">إجازة إدارية</option>
                                <option value="holiday_compensation">إجازة بدل أعياد</option>
                                <option value="grant_compensation">إجازة بدل منح</option>
                                <option value="casual">إجازة عرضية</option>
                                <option value="unpaid">إجازة بدون راتب</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">عدد الأيام</label>
                            <input type="number" class="form-control" id="daysRequested" readonly>
                        </div>
                    </div>

                    <div class="mb-3 mt-3">
                        <label class="form-label">سبب الإجازة</label>
                        <textarea class="form-control" id="leaveReason" rows="4" required placeholder="اكتب سبب طلب الإجازة..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">مرفق (اختياري)</label>
                        <input type="file" class="form-control" id="leaveAttachment" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                        <small class="text-muted">يمكن إرفاق تقرير طبي أو مستندات أخرى</small>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إرسال الطلب للمراجعة والموافقة من قبل المدير المباشر.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitLeaveRequest()">تقديم الطلب</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الطلب -->
<div class="modal fade" id="viewRequestModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestDetails">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// حساب عدد الأيام تلقائياً
function calculateDays() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
        document.getElementById('daysRequested').value = diffDays;
    }
}

document.getElementById('startDate').addEventListener('change', calculateDays);
document.getElementById('endDate').addEventListener('change', calculateDays);

// وظائف الإجازات
function newLeaveRequest() {
    loadEmployees();
    new bootstrap.Modal(document.getElementById('leaveModal')).show();
}

function loadEmployees() {
    const select = document.getElementById('employeeSelect');
    select.innerHTML = '<option value="">اختر الموظف</option>';

    const employees = [
        {id: 1, name: 'أحمد محمد الكعبي'},
        {id: 2, name: 'فاطمة علي النعيمي'},
        {id: 3, name: 'محمد سالم الثاني'},
        {id: 4, name: 'عائشة خالد المري'},
        {id: 5, name: 'يوسف أحمد الدوسري'}
    ];

    employees.forEach(emp => {
        const option = document.createElement('option');
        option.value = emp.id;
        option.textContent = emp.name;
        select.appendChild(option);
    });
}

function submitLeaveRequest() {
    const form = document.getElementById('leaveForm');

    if (!document.getElementById('employeeSelect').value) {
        alert('يرجى اختيار الموظف');
        return;
    }

    if (!document.getElementById('leaveType').value) {
        alert('يرجى اختيار نوع الإجازة');
        return;
    }

    if (!document.getElementById('startDate').value || !document.getElementById('endDate').value) {
        alert('يرجى تحديد تواريخ الإجازة');
        return;
    }

    if (!document.getElementById('leaveReason').value.trim()) {
        alert('يرجى كتابة سبب الإجازة');
        return;
    }

    alert('تم تقديم طلب الإجازة بنجاح وسيتم مراجعته');
    bootstrap.Modal.getInstance(document.getElementById('leaveModal')).hide();
    // هنا يمكن إضافة استدعاء AJAX لحفظ البيانات
}

function viewRequest(id) {
    // عرض تفاصيل الطلب
    const details = `
        <div class="row">
            <div class="col-sm-4"><strong>رقم الطلب:</strong></div>
            <div class="col-sm-8">${id}</div>
        </div>
        <hr>
        <div class="row">
            <div class="col-sm-4"><strong>الموظف:</strong></div>
            <div class="col-sm-8">أحمد محمد الكعبي</div>
        </div>
        <div class="row mt-2">
            <div class="col-sm-4"><strong>نوع الإجازة:</strong></div>
            <div class="col-sm-8">إجازة سنوية</div>
        </div>
        <div class="row mt-2">
            <div class="col-sm-4"><strong>من تاريخ:</strong></div>
            <div class="col-sm-8">2024-02-01</div>
        </div>
        <div class="row mt-2">
            <div class="col-sm-4"><strong>إلى تاريخ:</strong></div>
            <div class="col-sm-8">2024-02-07</div>
        </div>
        <div class="row mt-2">
            <div class="col-sm-4"><strong>عدد الأيام:</strong></div>
            <div class="col-sm-8">7 أيام</div>
        </div>
        <div class="row mt-2">
            <div class="col-sm-4"><strong>السبب:</strong></div>
            <div class="col-sm-8">إجازة عائلية للسفر</div>
        </div>
    `;

    document.getElementById('requestDetails').innerHTML = details;
    new bootstrap.Modal(document.getElementById('viewRequestModal')).show();
}

function approveRequest(id) {
    if (confirm('هل تريد الموافقة على طلب الإجازة؟')) {
        fetch(`/hr/api/approve_leave/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload(); // إعادة تحميل الصفحة لإظهار التحديث
            } else {
                alert(data.message || 'حدث خطأ في الموافقة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function rejectRequest(id) {
    const reason = prompt('يرجى كتابة سبب الرفض:');
    if (reason) {
        fetch(`/hr/api/reject_leave/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({reason: reason})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload(); // إعادة تحميل الصفحة لإظهار التحديث
            } else {
                alert(data.message || 'حدث خطأ في الرفض');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function viewAttachment(filename) {
    alert(`عرض المرفق: ${filename}`);
    // هنا يمكن إضافة وظيفة عرض الملف
}

function approveAllPending() {
    if (confirm('هل تريد الموافقة على جميع الطلبات المعلقة؟')) {
        fetch('/hr/api/approve_all_pending', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`${data.message} (${data.count} طلب)`);
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في الموافقة الجماعية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function leaveReport() {
    // عرض مؤشر التحميل
    const originalText = event.target.innerHTML;
    event.target.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنتاج التقرير...';
    event.target.disabled = true;

    fetch('/hr/api/leave_report')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showLeaveReport(data.data);
        } else {
            alert('حدث خطأ في إنتاج التقرير');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        event.target.innerHTML = originalText;
        event.target.disabled = false;
    });
}

function exportLeaves() {
    // عرض مؤشر التحميل
    const originalText = event.target.innerHTML;
    event.target.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    event.target.disabled = true;

    fetch('/hr/api/export_leaves')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحويل البيانات إلى JSON وتنزيلها
            const jsonData = JSON.stringify(data.data, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert(`تم تصدير ${data.total_records} سجل بنجاح`);
        } else {
            alert('حدث خطأ في تصدير البيانات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        event.target.innerHTML = originalText;
        event.target.disabled = false;
    });
}

function leaveSettings() {
    fetch('/hr/api/leave_settings')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showLeaveSettings(data.settings);
        } else {
            alert('حدث خطأ في جلب الإعدادات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// تصفية الجدول
document.getElementById('leaveTypeFilter').addEventListener('change', filterTable);
document.getElementById('statusFilter').addEventListener('change', filterTable);
document.getElementById('departmentFilter').addEventListener('change', filterTable);

function filterTable() {
    const typeFilter = document.getElementById('leaveTypeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const deptFilter = document.getElementById('departmentFilter').value;
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        let showRow = true;

        if (typeFilter && showRow) {
            const typeCell = row.cells[1].textContent.toLowerCase();
            const typeMap = {
                'annual': 'سنوية',
                'sick': 'مرضية',
                'emergency': 'طارئة',
                'maternity': 'أمومة',
                'unpaid': 'بدون راتب'
            };
            if (!typeCell.includes(typeMap[typeFilter])) {
                showRow = false;
            }
        }

        if (statusFilter && showRow) {
            const statusCell = row.cells[6].textContent.toLowerCase();
            const statusMap = {
                'pending': 'معلقة',
                'approved': 'موافق',
                'rejected': 'مرفوضة',
                'cancelled': 'ملغاة'
            };
            if (!statusCell.includes(statusMap[statusFilter])) {
                showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    });
}

// وظائف عرض التقارير والإعدادات
function showLeaveReport(reportData) {
    const reportHtml = `
        <div class="modal fade" id="reportModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الإجازات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h3 class="text-primary">${reportData.summary.total_requests}</h3>
                                        <p class="mb-0">إجمالي الطلبات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h3 class="text-warning">${reportData.summary.pending_requests}</h3>
                                        <p class="mb-0">طلبات معلقة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h3 class="text-success">${reportData.summary.approved_requests}</h3>
                                        <p class="mb-0">طلبات موافق عليها</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h3 class="text-info">${reportData.summary.approval_rate}%</h3>
                                        <p class="mb-0">معدل الموافقة</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>إحصائيات حسب نوع الإجازة</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>نوع الإجازة</th>
                                                <th>عدد الطلبات</th>
                                                <th>إجمالي الأيام</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${reportData.leave_types.map(lt => `
                                                <tr>
                                                    <td>${lt.type_name}</td>
                                                    <td>${lt.count}</td>
                                                    <td>${lt.total_days}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>أكثر الموظفين طلباً للإجازات</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الموظف</th>
                                                <th>عدد الطلبات</th>
                                                <th>إجمالي الأيام</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${reportData.top_employees.map(emp => `
                                                <tr>
                                                    <td>${emp.name}</td>
                                                    <td>${emp.request_count}</td>
                                                    <td>${emp.total_days}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">تم إنتاج التقرير في: ${reportData.generated_at}</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="printReport()">طباعة التقرير</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة المودال إلى الصفحة وعرضه
    document.body.insertAdjacentHTML('beforeend', reportHtml);
    new bootstrap.Modal(document.getElementById('reportModal')).show();

    // إزالة المودال عند إغلاقه
    document.getElementById('reportModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function showLeaveSettings(settings) {
    const settingsHtml = `
        <div class="modal fade" id="settingsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إعدادات الإجازات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="settingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">أيام الإجازة السنوية</label>
                                    <input type="number" class="form-control" id="annualLeaveDays" value="${settings.annual_leave_days}">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">أيام الإجازة المرضية</label>
                                    <input type="number" class="form-control" id="sickLeaveDays" value="${settings.sick_leave_days}">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">أيام إجازة الأمومة</label>
                                    <input type="number" class="form-control" id="maternityLeaveDays" value="${settings.maternity_leave_days}">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">أيام الإجازة الطارئة</label>
                                    <input type="number" class="form-control" id="emergencyLeaveDays" value="${settings.emergency_leave_days}">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">أيام الإشعار المسبق</label>
                                    <input type="number" class="form-control" id="advanceNoticeDays" value="${settings.advance_notice_days}">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">الحد الأقصى للأيام المتتالية</label>
                                    <input type="number" class="form-control" id="maxConsecutiveDays" value="${settings.max_consecutive_days}">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoApproveAnnual" ${settings.auto_approve_annual ? 'checked' : ''}>
                                        <label class="form-check-label" for="autoApproveAnnual">
                                            الموافقة التلقائية على الإجازة السنوية
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireMedicalCert" ${settings.require_medical_certificate ? 'checked' : ''}>
                                        <label class="form-check-label" for="requireMedicalCert">
                                            طلب شهادة طبية للإجازة المرضية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveLeaveSettings()">حفظ الإعدادات</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة المودال إلى الصفحة وعرضه
    document.body.insertAdjacentHTML('beforeend', settingsHtml);
    new bootstrap.Modal(document.getElementById('settingsModal')).show();

    // إزالة المودال عند إغلاقه
    document.getElementById('settingsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function saveLeaveSettings() {
    const settingsData = {
        annual_leave_days: parseInt(document.getElementById('annualLeaveDays').value),
        sick_leave_days: parseInt(document.getElementById('sickLeaveDays').value),
        maternity_leave_days: parseInt(document.getElementById('maternityLeaveDays').value),
        emergency_leave_days: parseInt(document.getElementById('emergencyLeaveDays').value),
        advance_notice_days: parseInt(document.getElementById('advanceNoticeDays').value),
        max_consecutive_days: parseInt(document.getElementById('maxConsecutiveDays').value),
        auto_approve_annual: document.getElementById('autoApproveAnnual').checked,
        require_medical_certificate: document.getElementById('requireMedicalCert').checked
    };

    fetch('/hr/api/leave_settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
        } else {
            alert('حدث خطأ في حفظ الإعدادات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function printReport() {
    window.print();
}

// وظيفة عرض رصيد إجازات الموظف
function showEmployeeBalance(employeeId) {
    fetch(`/hr/api/employee_leave_balance/${employeeId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const balanceHtml = `
                <div class="modal fade" id="balanceModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">رصيد إجازات الموظف</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <h6>${data.employee.name} (${data.employee.employee_number})</h6>
                                <p class="text-muted">${data.employee.department} - العام ${data.year}</p>

                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>نوع الإجازة</th>
                                                <th>المتاح</th>
                                                <th>المستخدم</th>
                                                <th>المتبقي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${Object.entries(data.balances).map(([type, balance]) => `
                                                <tr>
                                                    <td>${balance.type_name}</td>
                                                    <td>${balance.available}</td>
                                                    <td>${balance.used}</td>
                                                    <td class="${balance.remaining < 5 ? 'text-danger' : 'text-success'}">${balance.remaining}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', balanceHtml);
            new bootstrap.Modal(document.getElementById('balanceModal')).show();

            document.getElementById('balanceModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        } else {
            alert('حدث خطأ في جلب رصيد الإجازات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}
</script>
{% endblock %}
