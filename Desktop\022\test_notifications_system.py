"""
اختبار نظام الإشعارات وسير العمل المتقدم
"""

import requests
import time
import json

def test_notifications_system():
    """اختبار نظام الإشعارات وسير العمل"""
    
    base_url = "http://localhost:5000"
    
    print("🚀 بدء اختبار نظام الإشعارات وسير العمل المتقدم")
    print("=" * 60)
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # 1. اختبار تسجيل الدخول
        print("🔑 اختبار تسجيل الدخول...")
        login_response = session.post(f"{base_url}/login", data={
            'username': 'admin',
            'password': 'admin123'
        }, allow_redirects=False)
        
        if login_response.status_code in [200, 302]:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print("❌ فشل تسجيل الدخول")
            return
        
        # 2. اختبار الصفحة الرئيسية
        print("\n📊 اختبار الصفحة الرئيسية...")
        dashboard_response = session.get(f"{base_url}/dashboard")
        if dashboard_response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل بنجاح")
            if "الإشعارات" in dashboard_response.text:
                print("✅ نظام الإشعارات موجود في الواجهة")
            if "سير العمل" in dashboard_response.text:
                print("✅ نظام سير العمل موجود في الواجهة")
        else:
            print("❌ خطأ في الصفحة الرئيسية")
        
        # 3. اختبار APIs الإشعارات
        print("\n🔔 اختبار APIs الإشعارات...")
        
        # اختبار الحصول على الإشعارات
        notifications_response = session.get(f"{base_url}/api/notifications")
        if notifications_response.status_code == 200:
            print("✅ API الإشعارات يعمل بنجاح")
            notifications = notifications_response.json()
            print(f"📊 عدد الإشعارات: {len(notifications)}")
        else:
            print("❌ خطأ في API الإشعارات")
        
        # اختبار عدد الإشعارات غير المقروءة
        count_response = session.get(f"{base_url}/api/notifications/count")
        if count_response.status_code == 200:
            print("✅ API عدد الإشعارات يعمل بنجاح")
            count_data = count_response.json()
            print(f"📊 الإشعارات غير المقروءة: {count_data.get('count', 0)}")
        else:
            print("❌ خطأ في API عدد الإشعارات")
        
        # 4. اختبار APIs سير العمل
        print("\n⚙️ اختبار APIs سير العمل...")
        
        # اختبار مهام سير العمل
        workflow_response = session.get(f"{base_url}/api/workflow/tasks")
        if workflow_response.status_code == 200:
            print("✅ API مهام سير العمل يعمل بنجاح")
            tasks = workflow_response.json()
            print(f"📊 عدد المهام المعلقة: {len(tasks)}")
        else:
            print("❌ خطأ في API مهام سير العمل")
        
        # 5. اختبار صفحة الإعدادات
        print("\n⚙️ اختبار صفحة الإعدادات...")
        settings_response = session.get(f"{base_url}/settings")
        if settings_response.status_code == 200:
            print("✅ صفحة الإعدادات تعمل بنجاح")
            if "الإشعارات" in settings_response.text:
                print("✅ إعدادات الإشعارات متاحة")
        else:
            print("❌ خطأ في صفحة الإعدادات")
        
        # 6. اختبار لوحة الإدارة المتقدمة
        print("\n🛠️ اختبار لوحة الإدارة المتقدمة...")
        admin_response = session.get(f"{base_url}/admin")
        if admin_response.status_code == 200:
            print("✅ لوحة الإدارة المتقدمة تعمل بنجاح")
            if "الإحصائيات" in admin_response.text:
                print("✅ الإحصائيات متاحة")
            if "الوحدات" in admin_response.text:
                print("✅ إدارة الوحدات متاحة")
        else:
            print("❌ خطأ في لوحة الإدارة المتقدمة")
        
        # 7. اختبار حالة النظام
        print("\n📊 اختبار حالة النظام...")
        status_response = session.get(f"{base_url}/api/system/status")
        if status_response.status_code == 200:
            print("✅ API حالة النظام يعمل بنجاح")
            status_data = status_response.json()
            print(f"📊 المستخدمين النشطين: {status_data.get('active_users', 0)}")
            print(f"📊 الإشعارات: {status_data.get('notifications_count', 0)}")
            print(f"📊 مهام سير العمل: {status_data.get('workflow_tasks_count', 0)}")
        else:
            print("❌ خطأ في API حالة النظام")
        
        # 8. اختبار إنشاء إشعار تجريبي
        print("\n🧪 اختبار إنشاء إشعار تجريبي...")
        test_notification_response = session.post(f"{base_url}/settings/api/test-notification", 
            json={
                'title': 'إشعار تجريبي',
                'message': 'هذا إشعار تجريبي لاختبار النظام',
                'type': 'info'
            },
            headers={'Content-Type': 'application/json'}
        )
        if test_notification_response.status_code == 200:
            print("✅ تم إنشاء الإشعار التجريبي بنجاح")
        else:
            print("❌ خطأ في إنشاء الإشعار التجريبي")
        
        print("\n" + "=" * 60)
        print("🎉 تم إكمال اختبار نظام الإشعارات وسير العمل!")
        print("\n📋 الميزات المتاحة:")
        print("✅ نظام الإشعارات المتقدم مع واجهة تفاعلية")
        print("✅ سير العمل والموافقات التلقائية")
        print("✅ لوحة الإدارة المتقدمة مع الإحصائيات")
        print("✅ إعدادات النظام الشاملة")
        print("✅ APIs متقدمة للتكامل")
        print("✅ واجهة مستخدم عربية متجاوبة")
        
        print("\n🌐 معلومات الوصول:")
        print(f"🔗 الرابط الرئيسي: {base_url}")
        print("🔑 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        
        print("\n📱 الميزات الجديدة:")
        print("• إشعارات فورية في الشريط العلوي")
        print("• مهام سير العمل مع التنبيهات")
        print("• لوحة إدارة متقدمة مع الإحصائيات")
        print("• إعدادات قابلة للتخصيص")
        print("• سجل تدقيق شامل")
        print("• نسخ احتياطي تلقائي")
        
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم")
        print("💡 تأكد من تشغيل النظام باستخدام: python app.py")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")

def test_ui_features():
    """اختبار ميزات الواجهة"""
    
    print("\n🎨 اختبار ميزات الواجهة...")
    print("=" * 40)
    
    features = [
        "✅ شريط إشعارات تفاعلي مع عدادات",
        "✅ قائمة مهام سير العمل مع الأولويات",
        "✅ إعدادات سريعة في الشريط العلوي",
        "✅ لوحة إدارة متقدمة مع الرسوم البيانية",
        "✅ واجهة عربية RTL متجاوبة",
        "✅ ألوان علم قطر والهوية البصرية",
        "✅ تأثيرات بصرية متقدمة",
        "✅ تحديث تلقائي للبيانات",
        "✅ مودالات تفاعلية للإعدادات",
        "✅ نظام تنبيهات متقدم"
    ]
    
    for feature in features:
        print(feature)
        time.sleep(0.1)  # تأثير بصري
    
    print("\n🎯 النظام جاهز للاستخدام مع جميع الميزات المتقدمة!")

if __name__ == '__main__':
    test_notifications_system()
    test_ui_features()
