# دليل المستخدم السريع
## نظام إدارة الموارد - دولة قطر 🇶🇦

---

## 🚀 بدء التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل
1. انقر نقراً مزدوجاً على ملف `start_system.bat`
2. انتظر حتى يتم تحميل النظام
3. سيفتح النظام تلقائياً في المتصفح على العنوان: `http://localhost:5000`

### الطريقة الثانية: التشغيل اليدوي
```bash
python app.py
```

---

## 🔑 بيانات تسجيل الدخول

### المدير العام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: جميع الوحدات

### مدير الموارد البشرية
- **اسم المستخدم**: `hr_manager`
- **كلمة المرور**: `123456`
- **الصلاحيات**: الموارد البشرية، الموظفين، الرواتب

### المحاسب
- **اسم المستخدم**: `accountant`
- **كلمة المرور**: `123456`
- **الصلاحيات**: المحاسبة، المبيعات، المشتريات، التقارير

### مدير المخزون
- **اسم المستخدم**: `inventory_manager`
- **كلمة المرور**: `123456`
- **الصلاحيات**: المخزون، المنتجات، المستودعات

---

## 📊 إعداد البيانات التجريبية

لإضافة بيانات تجريبية للنظام:
1. انقر نقراً مزدوجاً على ملف `setup_demo.bat`
2. أو استخدم الأمر: `python setup_demo_data.py`

### البيانات التجريبية تشمل:
- 👥 **5 موظفين** بأقسام مختلفة
- 📦 **5 منتجات** (إلكترونيات، أثاث، مستلزمات)
- 🏢 **3 عملاء** (حكومي وخاص)
- 🚚 **2 موردين** معتمدين
- 🏪 **2 مستودعات** (رئيسي وفرعي)
- 💰 **8 حسابات محاسبية** أساسية

---

## 🧭 التنقل في النظام

### الشريط العلوي
- **شعار النظام**: العودة للوحة التحكم
- **لوحة التحكم**: الصفحة الرئيسية
- **الملف الشخصي**: معلومات المستخدم وتغيير كلمة المرور

### الشريط الجانبي
يعرض الوحدات المتاحة حسب صلاحيات المستخدم:
- 🧮 **المحاسبة**: الحسابات، القيود، التقارير المالية
- 👥 **الموارد البشرية**: الموظفين، الحضور، الرواتب، الإجازات
- 📦 **المخزون**: المنتجات، المستودعات، حركات المخزون
- 🛒 **المبيعات**: العملاء، أوامر البيع، الفواتير
- 🚛 **المشتريات**: الموردين، أوامر الشراء
- 📋 **المشاريع**: إدارة المشاريع والمهام
- 🤝 **إدارة العملاء**: العملاء المحتملين والأنشطة

---

## 🔧 الوظائف الأساسية

### إدارة الموظفين
1. انتقل إلى **الموارد البشرية** → **الموظفين**
2. استخدم أدوات البحث والتصفية
3. اضغط **إضافة موظف** لإضافة موظف جديد
4. استخدم الأزرار في عمود الإجراءات للتعديل والعرض

### إدارة المنتجات
1. انتقل إلى **المخزون** → **المنتجات**
2. تصفح قائمة المنتجات الحالية
3. راقب المنتجات منخفضة المخزون (باللون الأحمر)
4. استخدم أدوات البحث للعثور على منتجات محددة

### المحاسبة
1. انتقل إلى **المحاسبة** → **دليل الحسابات**
2. استعرض شجرة الحسابات المحاسبية
3. راقب الأرصدة الحالية لكل حساب
4. استخدم التصفية حسب نوع الحساب أو المستوى

---

## 🎨 مزايا التصميم

### ألوان علم قطر
- **العنابي الداكن**: `#8B1538` (الأزرار والعناوين)
- **العنابي الفاتح**: `#A91B47` (التدرجات)
- **الأبيض**: `#FFFFFF` (النصوص والخلفيات)

### الخط العربي
- خط **Cairo** الجميل والواضح
- دعم كامل للغة العربية مع RTL
- أحجام متدرجة للعناوين والنصوص

### التصميم المتجاوب
- يعمل على الكمبيوتر والهاتف والتابلت
- قوائم قابلة للطي على الشاشات الصغيرة
- أزرار وعناصر سهلة اللمس

---

## 🔍 البحث والتصفية

### أدوات البحث
- **مربع البحث**: البحث في جميع الحقول
- **تصفية القسم**: تصفية حسب القسم أو الفئة
- **تصفية الحالة**: تصفية حسب الحالة (نشط/غير نشط)
- **الترتيب**: ترتيب النتائج حسب معايير مختلفة

### نصائح البحث
- استخدم كلمات مفتاحية قصيرة
- جرب البحث بالأرقام (كود الموظف، كود المنتج)
- استخدم التصفية لتضييق النتائج

---

## 📱 الاستخدام على الهاتف

### التنقل
- اضغط على أيقونة القائمة (☰) لفتح الشريط الجانبي
- استخدم التمرير الأفقي للجداول الكبيرة
- اضغط على العناصر للحصول على تفاصيل أكثر

### نصائح للهاتف
- استخدم الوضع الأفقي للجداول
- اضغط مطولاً على الأزرار لرؤية التلميحات
- استخدم البحث بدلاً من التصفح في القوائم الطويلة

---

## ⚠️ نصائح مهمة

### الأمان
- غيّر كلمة المرور الافتراضية فوراً
- لا تشارك بيانات تسجيل الدخول
- سجل الخروج عند الانتهاء من العمل

### الأداء
- أغلق التبويبات غير المستخدمة
- حدّث الصفحة إذا واجهت بطء
- استخدم متصفح حديث (Chrome, Firefox, Edge)

### النسخ الاحتياطية
- النظام يحفظ البيانات تلقائياً
- يُنصح بعمل نسخة احتياطية دورية من مجلد `instance`

---

## 🆘 حل المشاكل الشائعة

### لا يمكن الوصول للنظام
1. تأكد من تشغيل النظام (`python app.py`)
2. تحقق من العنوان: `http://localhost:5000`
3. جرب إعادة تشغيل النظام

### مشاكل تسجيل الدخول
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. تأكد من عدم وجود مسافات إضافية
3. جرب المستخدم الافتراضي: `admin` / `admin123`

### الصفحة لا تعمل
1. حدّث الصفحة (F5)
2. امسح ذاكرة التخزين المؤقت
3. جرب متصفح آخر

### البيانات لا تظهر
1. تأكد من إضافة البيانات التجريبية
2. تحقق من الصلاحيات
3. جرب تسجيل الدخول بمستخدم آخر

---

## 📞 الدعم الفني

للحصول على المساعدة:
- راجع ملف `README.md` للتفاصيل التقنية
- تحقق من ملفات السجل في وحدة التحكم
- تواصل مع فريق تقنية المعلومات

---

**نتمنى لك تجربة ممتعة مع نظام إدارة الموارد - دولة قطر! 🇶🇦**
