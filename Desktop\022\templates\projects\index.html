{% extends "base.html" %}

{% block title %}إدارة المشاريع{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-project-diagram me-2"></i>
                        إدارة المشاريع
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">المشاريع</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="addNewProject()">
                        <i class="fas fa-plus me-2"></i>
                        مشروع جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-project-diagram fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ projects|length }}</h4>
                    <p class="text-muted mb-0">إجمالي المشاريع</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-play fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ active_projects }}</h4>
                    <p class="text-muted mb-0">مشاريع نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-check fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ completed_projects }}</h4>
                    <p class="text-muted mb-0">مشاريع مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-dollar-sign fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ total_budget|number_format }}</h4>
                    <p class="text-muted mb-0">إجمالي الميزانية (ر.ق)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchProjects" placeholder="البحث في المشاريع...">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="planning">تخطيط</option>
                <option value="active">نشط</option>
                <option value="on_hold">معلق</option>
                <option value="completed">مكتمل</option>
                <option value="cancelled">ملغي</option>
            </select>
        </div>
        <div class="col-md-3">
            <div class="btn-group w-100">
                <button class="btn btn-outline-danger" onclick="deleteSelectedProjects()" id="deleteSelectedBtn" disabled>
                    <i class="fas fa-trash me-2"></i>
                    حذف المحدد
                </button>
                <button class="btn btn-outline-primary" onclick="exportProjects()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- جدول المشاريع -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المشاريع
                </h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAllProjects" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAllProjects">
                        تحديد الكل
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if projects %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllHeader" onchange="toggleSelectAll()">
                            </th>
                            <th>اسم المشروع</th>
                            <th>العميل</th>
                            <th>الحالة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الميزانية</th>
                            <th>التقدم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for project in projects %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input project-checkbox" value="{{ project.id }}" onchange="updateDeleteButton()">
                            </td>
                            <td>
                                <div>
                                    <strong>{{ project.name }}</strong>
                                    {% if project.description %}
                                    <br><small class="text-muted">{{ project.description[:50] }}...</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>{{ project.client_name or 'غير محدد' }}</td>
                            <td>
                                {% if project.status == 'planning' %}
                                <span class="badge bg-secondary">تخطيط</span>
                                {% elif project.status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                                {% elif project.status == 'on_hold' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif project.status == 'completed' %}
                                <span class="badge bg-info">مكتمل</span>
                                {% elif project.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                            <td>{{ project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد' }}</td>
                            <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                            <td>{{ project.budget|number_format if project.budget else 'غير محدد' }} ر.ق</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" style="width: {{ project.progress or 0 }}%">
                                        {{ project.progress or 0 }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewProject({{ project.id }})" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="editProject({{ project.id }})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="viewTasks({{ project.id }})" title="المهام">
                                        <i class="fas fa-tasks"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteProject({{ project.id }})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مشاريع</h5>
                <p class="text-muted">ابدأ بإضافة مشروع جديد</p>
                <button class="btn btn-primary" onclick="addNewProject()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مشروع جديد
                </button>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportProjects()">
                                <i class="fas fa-download me-2"></i>
                                تصدير المشاريع
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="generateProjectReport()">
                                <i class="fas fa-chart-bar me-2"></i>
                                تقرير المشاريع
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="bulkProjectActions()">
                                <i class="fas fa-tasks me-2"></i>
                                إجراءات جماعية
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="projectSettings()">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات المشاريع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة مشروع جديد -->
<div class="modal fade" id="addProjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مشروع جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProjectForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم المشروع *</label>
                            <input type="text" class="form-control" id="projectName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">العميل</label>
                            <input type="text" class="form-control" id="clientName">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">وصف المشروع</label>
                            <textarea class="form-control" id="projectDescription" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الانتهاء</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الميزانية (ر.ق)</label>
                            <input type="number" class="form-control" id="budget" step="0.01">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="projectStatus">
                                <option value="planning">تخطيط</option>
                                <option value="active">نشط</option>
                                <option value="on_hold">معلق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نسبة التقدم (%)</label>
                            <input type="number" class="form-control" id="progress" min="0" max="100" value="0">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveProject()">حفظ المشروع</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let selectedProjects = [];

// وظائف إدارة المشاريع
function addNewProject() {
    // تحديد التاريخ الافتراضي
    document.getElementById('startDate').value = new Date().toISOString().split('T')[0];

    // عرض النموذج
    new bootstrap.Modal(document.getElementById('addProjectModal')).show();
}

function saveProject() {
    const form = document.getElementById('addProjectForm');

    // التحقق من الحقول المطلوبة
    if (!document.getElementById('projectName').value.trim()) {
        alert('يرجى إدخال اسم المشروع');
        return;
    }

    // جمع البيانات
    const projectData = {
        name: document.getElementById('projectName').value,
        client_name: document.getElementById('clientName').value,
        description: document.getElementById('projectDescription').value,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        budget: parseFloat(document.getElementById('budget').value) || 0,
        status: document.getElementById('projectStatus').value,
        progress: parseInt(document.getElementById('progress').value) || 0
    };

    // محاكاة حفظ البيانات
    console.log('بيانات المشروع:', projectData);
    alert('تم حفظ المشروع بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('addProjectModal')).hide();
    location.reload();
}

function viewProject(projectId) {
    alert(`عرض تفاصيل المشروع رقم ${projectId}`);
}

function editProject(projectId) {
    alert(`تعديل المشروع رقم ${projectId}`);
}

function viewTasks(projectId) {
    alert(`عرض مهام المشروع رقم ${projectId}`);
}

function deleteProject(projectId) {
    if (confirm('هل أنت متأكد من حذف هذا المشروع؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert(`تم حذف المشروع رقم ${projectId}`);
        location.reload();
    }
}

// وظائف التحديد المتعدد
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllProjects') || document.getElementById('selectAllHeader');
    const projectCheckboxes = document.querySelectorAll('.project-checkbox');

    projectCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.project-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    selectedProjects = Array.from(checkedBoxes).map(cb => cb.value);

    if (selectedProjects.length > 0) {
        deleteBtn.disabled = false;
        deleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>حذف المحدد (${selectedProjects.length})`;
    } else {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-trash me-2"></i>حذف المحدد';
    }
}

function deleteSelectedProjects() {
    if (selectedProjects.length === 0) {
        alert('يرجى تحديد مشاريع للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedProjects.length} مشروع؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
        alert(`تم حذف ${selectedProjects.length} مشروع بنجاح`);
        location.reload();
    }
}

// الإجراءات السريعة
function exportProjects() {
    alert('تصدير قائمة المشاريع');
}

function generateProjectReport() {
    alert('إنتاج تقرير المشاريع');
}

function bulkProjectActions() {
    alert('الإجراءات الجماعية للمشاريع');
}

function projectSettings() {
    alert('إعدادات إدارة المشاريع');
}

// البحث والتصفية
document.getElementById('searchProjects').addEventListener('input', function() {
    // تطبيق البحث
    console.log('البحث:', this.value);
});

document.getElementById('statusFilter').addEventListener('change', function() {
    // تطبيق التصفية
    console.log('التصفية:', this.value);
});

// تحديث أزرار الحذف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDeleteButton();
});
</script>
{% endblock %}
