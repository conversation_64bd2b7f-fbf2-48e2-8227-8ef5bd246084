{% extends "base.html" %}

{% block title %}إدارة المبيعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-shopping-cart me-2"></i>
                        إدارة المبيعات
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">المبيعات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="addNewSale()">
                        <i class="fas fa-plus me-2"></i>
                        فاتورة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ sales|length }}</h4>
                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ total_sales|number_format }}</h4>
                    <p class="text-muted mb-0">إجمالي المبيعات (ر.ق)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ monthly_sales|number_format }}</h4>
                    <p class="text-muted mb-0">مبيعات الشهر (ر.ق)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ unique_customers }}</h4>
                    <p class="text-muted mb-0">عملاء فريدون</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="row mb-3">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchSales" placeholder="البحث في الفواتير...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="draft">مسودة</option>
                <option value="pending">معلقة</option>
                <option value="paid">مدفوعة</option>
                <option value="cancelled">ملغاة</option>
            </select>
        </div>
        <div class="col-md-2">
            <input type="date" class="form-control" id="dateFilter" placeholder="التاريخ">
        </div>
        <div class="col-md-4">
            <div class="btn-group w-100">
                <button class="btn btn-outline-danger" onclick="deleteSelectedSales()" id="deleteSelectedBtn" disabled>
                    <i class="fas fa-trash me-2"></i>
                    حذف المحدد
                </button>
                <button class="btn btn-outline-primary" onclick="exportSales()">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- جدول المبيعات -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    فواتير المبيعات
                </h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAllSales" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAllSales">
                        تحديد الكل
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if sales %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllHeader" onchange="toggleSelectAll()">
                            </th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>طريقة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input sale-checkbox" value="{{ sale.id }}" onchange="updateDeleteButton()">
                            </td>
                            <td>
                                <strong>{{ sale.invoice_number }}</strong>
                            </td>
                            <td>{{ sale.customer_name or 'عميل نقدي' }}</td>
                            <td>{{ sale.sale_date.strftime('%Y-%m-%d') if sale.sale_date else 'غير محدد' }}</td>
                            <td>{{ sale.total_amount|number_format }} ر.ق</td>
                            <td>
                                {% if sale.status == 'draft' %}
                                <span class="badge bg-secondary">مسودة</span>
                                {% elif sale.status == 'pending' %}
                                <span class="badge bg-warning">معلقة</span>
                                {% elif sale.status == 'paid' %}
                                <span class="badge bg-success">مدفوعة</span>
                                {% elif sale.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغاة</span>
                                {% endif %}
                            </td>
                            <td>{{ sale.payment_method or 'غير محدد' }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewSale({{ sale.id }})" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="editSale({{ sale.id }})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="printInvoice({{ sale.id }})" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteSale({{ sale.id }})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فواتير مبيعات</h5>
                <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                <button class="btn btn-primary" onclick="addNewSale()">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء فاتورة جديدة
                </button>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportSales()">
                                <i class="fas fa-download me-2"></i>
                                تصدير المبيعات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="generateSalesReport()">
                                <i class="fas fa-chart-bar me-2"></i>
                                تقرير المبيعات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="bulkSalesActions()">
                                <i class="fas fa-tasks me-2"></i>
                                إجراءات جماعية
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="salesSettings()">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات المبيعات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة فاتورة جديدة -->
<div class="modal fade" id="addSaleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء فاتورة مبيعات جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addSaleForm">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">رقم الفاتورة *</label>
                            <input type="text" class="form-control" id="invoiceNumber" required readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الفاتورة *</label>
                            <input type="date" class="form-control" id="saleDate" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">العميل</label>
                            <input type="text" class="form-control" id="customerName" placeholder="عميل نقدي">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="paymentMethod">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="saleStatus">
                                <option value="draft">مسودة</option>
                                <option value="pending">معلقة</option>
                                <option value="paid">مدفوعة</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول الأصناف -->
                    <div class="mt-4">
                        <h6>أصناف الفاتورة</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="itemsTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><input type="text" class="form-control" placeholder="اسم الصنف"></td>
                                        <td><input type="number" class="form-control quantity" min="1" value="1" onchange="calculateRowTotal(this)"></td>
                                        <td><input type="number" class="form-control price" step="0.01" onchange="calculateRowTotal(this)"></td>
                                        <td><span class="row-total">0.00</span> ر.ق</td>
                                        <td><button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">حذف</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <button type="button" class="btn btn-sm btn-success" onclick="addNewRow()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة صنف
                        </button>
                    </div>

                    <!-- الإجماليات -->
                    <div class="row mt-3">
                        <div class="col-md-8"></div>
                        <div class="col-md-4">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>الإجمالي:</strong></td>
                                    <td class="text-end"><strong><span id="totalAmount">0.00</span> ر.ق</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSale()">حفظ الفاتورة</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let selectedSales = [];

// وظائف إدارة المبيعات
function addNewSale() {
    // إنتاج رقم فاتورة تلقائي
    const invoiceNumber = 'INV' + new Date().getFullYear() + String(Date.now()).slice(-6);
    document.getElementById('invoiceNumber').value = invoiceNumber;

    // تحديد التاريخ الحالي
    document.getElementById('saleDate').value = new Date().toISOString().split('T')[0];

    // عرض النموذج
    new bootstrap.Modal(document.getElementById('addSaleModal')).show();
}

function saveSale() {
    const form = document.getElementById('addSaleForm');

    // التحقق من الحقول المطلوبة
    if (!document.getElementById('invoiceNumber').value.trim()) {
        alert('يرجى إدخال رقم الفاتورة');
        return;
    }

    // جمع بيانات الأصناف
    const items = [];
    const rows = document.querySelectorAll('#itemsTable tbody tr');

    rows.forEach(row => {
        const itemName = row.querySelector('input[type="text"]').value;
        const quantity = row.querySelector('.quantity').value;
        const price = row.querySelector('.price').value;

        if (itemName && quantity && price) {
            items.push({
                name: itemName,
                quantity: parseFloat(quantity),
                price: parseFloat(price),
                total: parseFloat(quantity) * parseFloat(price)
            });
        }
    });

    if (items.length === 0) {
        alert('يرجى إضافة صنف واحد على الأقل');
        return;
    }

    // جمع البيانات
    const saleData = {
        invoice_number: document.getElementById('invoiceNumber').value,
        sale_date: document.getElementById('saleDate').value,
        customer_name: document.getElementById('customerName').value,
        payment_method: document.getElementById('paymentMethod').value,
        status: document.getElementById('saleStatus').value,
        items: items,
        total_amount: parseFloat(document.getElementById('totalAmount').textContent)
    };

    // محاكاة حفظ البيانات
    console.log('بيانات الفاتورة:', saleData);
    alert('تم حفظ الفاتورة بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('addSaleModal')).hide();
    location.reload();
}

// وظائف جدول الأصناف
function addNewRow() {
    const tbody = document.querySelector('#itemsTable tbody');
    const newRow = tbody.rows[0].cloneNode(true);

    // مسح القيم
    newRow.querySelectorAll('input').forEach(input => input.value = input.type === 'number' && input.classList.contains('quantity') ? '1' : '');
    newRow.querySelector('.row-total').textContent = '0.00';

    tbody.appendChild(newRow);
}

function removeRow(button) {
    const tbody = document.querySelector('#itemsTable tbody');
    if (tbody.rows.length > 1) {
        button.closest('tr').remove();
        calculateTotal();
    }
}

function calculateRowTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const price = parseFloat(row.querySelector('.price').value) || 0;
    const total = quantity * price;

    row.querySelector('.row-total').textContent = total.toFixed(2);
    calculateTotal();
}

function calculateTotal() {
    const rowTotals = document.querySelectorAll('.row-total');
    let total = 0;

    rowTotals.forEach(span => {
        total += parseFloat(span.textContent) || 0;
    });

    document.getElementById('totalAmount').textContent = total.toFixed(2);
}

// وظائف أخرى
function viewSale(saleId) {
    alert(`عرض تفاصيل الفاتورة رقم ${saleId}`);
}

function editSale(saleId) {
    alert(`تعديل الفاتورة رقم ${saleId}`);
}

function printInvoice(saleId) {
    alert(`طباعة الفاتورة رقم ${saleId}`);
}

function deleteSale(saleId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert(`تم حذف الفاتورة رقم ${saleId}`);
        location.reload();
    }
}

// وظائف التحديد المتعدد
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllSales') || document.getElementById('selectAllHeader');
    const saleCheckboxes = document.querySelectorAll('.sale-checkbox');

    saleCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.sale-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    selectedSales = Array.from(checkedBoxes).map(cb => cb.value);

    if (selectedSales.length > 0) {
        deleteBtn.disabled = false;
        deleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>حذف المحدد (${selectedSales.length})`;
    } else {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-trash me-2"></i>حذف المحدد';
    }
}

function deleteSelectedSales() {
    if (selectedSales.length === 0) {
        alert('يرجى تحديد فواتير للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedSales.length} فاتورة؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
        alert(`تم حذف ${selectedSales.length} فاتورة بنجاح`);
        location.reload();
    }
}

// الإجراءات السريعة
function exportSales() {
    alert('تصدير فواتير المبيعات');
}

function generateSalesReport() {
    alert('إنتاج تقرير المبيعات');
}

function bulkSalesActions() {
    alert('الإجراءات الجماعية للمبيعات');
}

function salesSettings() {
    alert('إعدادات إدارة المبيعات');
}

// تحديث أزرار الحذف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDeleteButton();
});
</script>
{% endblock %}
