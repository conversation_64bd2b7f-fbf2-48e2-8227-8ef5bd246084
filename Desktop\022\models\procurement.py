from datetime import datetime
from decimal import Decimal
from database_setup import db

class Supplier(db.Model):
    __tablename__ = 'suppliers'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))

    # نوع المورد
    supplier_type = db.Column(db.String(20), default='company')  # individual, company, government

    # معلومات الاتصال
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    country = db.Column(db.String(50), default='Qatar')

    # معلومات تجارية
    tax_number = db.Column(db.String(50))
    commercial_register = db.Column(db.String(50))
    payment_terms = db.Column(db.Integer, default=30)  # أيام الدفع

    # تقييم المورد
    rating = db.Column(db.Integer, default=5)  # من 1 إلى 5

    # حالة المورد
    is_active = db.Column(db.Boolean, default=True)
    is_approved = db.Column(db.Boolean, default=False)

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    purchase_orders = db.relationship('PurchaseOrder', backref='supplier', lazy='dynamic')

    def __repr__(self):
        return f'<Supplier {self.code} - {self.name_ar}>'

    def get_balance(self):
        """حساب رصيد المورد"""
        total_purchases = self.purchase_orders.filter_by(status='received').with_entities(
            db.func.sum(PurchaseOrder.total_amount)).scalar() or 0
        # يمكن إضافة المدفوعات هنا
        return total_purchases

class PurchaseOrder(db.Model):
    __tablename__ = 'purchase_orders'

    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(20), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)

    # تواريخ الطلب
    order_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    expected_delivery_date = db.Column(db.Date)
    actual_delivery_date = db.Column(db.Date)

    # حالة الطلب
    status = db.Column(db.String(20), default='draft')  # draft, sent, confirmed, received, cancelled

    # المبالغ
    subtotal = db.Column(db.Numeric(12, 2), default=0)
    tax_amount = db.Column(db.Numeric(12, 2), default=0)
    discount_amount = db.Column(db.Numeric(12, 2), default=0)
    total_amount = db.Column(db.Numeric(12, 2), default=0)

    # معلومات إضافية
    notes = db.Column(db.Text)
    terms_conditions = db.Column(db.Text)
    delivery_address = db.Column(db.Text)

    # المستخدم والتواريخ
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    received_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    order_items = db.relationship('PurchaseOrderItem', backref='purchase_order', cascade='all, delete-orphan')
    creator = db.relationship('User', foreign_keys=[created_by])
    approver = db.relationship('User', foreign_keys=[approved_by])
    receiver = db.relationship('User', foreign_keys=[received_by])

    def __repr__(self):
        return f'<PurchaseOrder {self.order_number}>'

    def calculate_totals(self):
        """حساب إجماليات الطلب"""
        self.subtotal = sum(item.total_price for item in self.order_items)
        self.tax_amount = self.subtotal * Decimal('0.05')  # ضريبة 5%
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount

    def receive_order(self, user_id):
        """استلام الطلب وتحديث المخزون"""
        if self.status != 'confirmed':
            raise ValueError("لا يمكن استلام طلب غير مؤكد")

        self.status = 'received'
        self.received_by = user_id
        self.actual_delivery_date = datetime.utcnow().date()

        # تحديث المخزون
        for item in self.order_items:
            if item.received_quantity > 0:
                item.product.update_stock(
                    quantity=item.received_quantity,
                    movement_type='in'
                )

class PurchaseOrderItem(db.Model):
    __tablename__ = 'purchase_order_items'

    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)

    # تفاصيل الصنف
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    received_quantity = db.Column(db.Numeric(10, 2), default=0)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    discount_percent = db.Column(db.Numeric(5, 2), default=0)
    total_price = db.Column(db.Numeric(12, 2))

    # معلومات إضافية
    description = db.Column(db.Text)
    notes = db.Column(db.Text)

    # العلاقات
    product = db.relationship('Product')

    def __repr__(self):
        return f'<PurchaseOrderItem {self.product_id} - {self.quantity}>'

    def calculate_total(self):
        """حساب إجمالي الصنف"""
        discount_amount = (self.unit_price * self.quantity) * (self.discount_percent / 100)
        self.total_price = (self.unit_price * self.quantity) - discount_amount

    @property
    def pending_quantity(self):
        """الكمية المتبقية للاستلام"""
        return self.quantity - self.received_quantity
