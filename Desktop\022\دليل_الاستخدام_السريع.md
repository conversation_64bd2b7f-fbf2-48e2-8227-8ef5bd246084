# 📖 دليل الاستخدام السريع
## نظام إدارة الموارد المتكامل - دولة قطر 🇶🇦

---

## 🚀 البدء السريع

### 1. تشغيل النظام
```bash
# الطريقة الأولى: ملف التشغيل السريع
انقر نقراً مزدوجاً على: تشغيل_جميع_الوحدات.bat

# الطريقة الثانية: سطر الأوامر
python app.py
```

### 2. فتح النظام
- **الرابط**: http://localhost:5000
- **المستخدم**: hr_manager
- **كلمة المرور**: 123456

---

## 🎯 الوحدات المتاحة

### 💰 وحدة المحاسبة
**الرابط**: http://localhost:5000/accounting
**الوظائف**:
- إدارة الحسابات المالية
- القيود اليومية
- التقارير المالية
- الميزانية العمومية

### 👥 وحدة الموارد البشرية
**الرابط**: http://localhost:5000/hr
**الوظائف**:
- إدارة الموظفين
- طلبات الإجازات (9 أنواع)
- الحضور والانصراف
- الرواتب والمكافآت

### 📦 وحدة المخزون
**الرابط**: http://localhost:5000/inventory
**الوظائف**:
- إدارة المنتجات
- حركات المخزون
- تتبع المستويات
- تقارير المخزون

### 🛒 وحدة المبيعات
**الرابط**: http://localhost:5000/sales
**الوظائف**:
- فواتير المبيعات
- إدارة العملاء
- طلبات البيع
- تقارير المبيعات

### 🛍️ وحدة المشتريات
**الرابط**: http://localhost:5000/procurement
**الوظائف**:
- طلبات الشراء
- إدارة الموردين
- متابعة الطلبات
- تقارير المشتريات

### 📋 وحدة المشاريع
**الرابط**: http://localhost:5000/projects
**الوظائف**:
- إدارة المشاريع
- تتبع المهام
- إدارة الفرق
- تقارير التقدم

### 🤝 وحدة إدارة العملاء
**الرابط**: http://localhost:5000/crm
**الوظائف**:
- العملاء المحتملين
- جهات الاتصال
- الأنشطة والمتابعة
- تحويل العملاء

---

## 🔧 الوظائف الأساسية

### تسجيل الدخول
1. افتح http://localhost:5000
2. أدخل: hr_manager
3. أدخل: 123456
4. انقر "تسجيل الدخول"

### التنقل بين الوحدات
- استخدم القائمة الجانبية اليسرى
- انقر على أي وحدة للانتقال إليها
- جميع الوحدات السبعة متاحة

### البحث والتصفية
- استخدم مربع البحث في أعلى كل جدول
- استخدم قوائم التصفية المتاحة
- النتائج تظهر فورياً

### الحذف المتعدد
1. حدد العناصر باستخدام مربعات الاختيار
2. انقر "حذف المحدد"
3. أكد العملية

### التصدير
- انقر زر "تصدير" في أي وحدة
- اختر التنسيق المطلوب
- سيتم تحميل الملف

---

## 📊 البيانات التجريبية

### المتوفر حالياً
- **👥 المستخدمون**: 4
- **🏢 الموظفون**: 6
- **💰 الحسابات**: 9
- **📦 المنتجات**: 5
- **🤝 العملاء**: 4
- **🚚 الموردون**: 3
- **📋 المشاريع**: 1
- **🎯 العملاء المحتملون**: 1

### إضافة بيانات جديدة
- انقر "إضافة جديد" في أي وحدة
- املأ النموذج المطلوب
- انقر "حفظ"

---

## 🎨 المزايا المتقدمة

### التصميم القطري
- ألوان العلم القطري (العنابي والأبيض)
- خط عربي جميل (Cairo)
- دعم كامل للغة العربية (RTL)
- تصميم متجاوب لجميع الأجهزة

### الوظائف التفاعلية
- مربعات حذف متعددة
- عدادات ديناميكية
- رسائل تأكيد واضحة
- تحديث فوري للواجهة

### الأمان
- نظام تسجيل دخول آمن
- صلاحيات متدرجة
- تتبع الأنشطة
- حماية البيانات

---

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة

#### لا يعمل النظام
**الحل**:
1. تأكد من تثبيت Python
2. شغل: `pip install -r requirements.txt`
3. شغل: `python app.py`

#### لا تظهر الوحدات
**الحل**:
1. تأكد من تسجيل الدخول
2. تحقق من الصلاحيات
3. أعد تحميل الصفحة

#### خطأ في قاعدة البيانات
**الحل**:
1. شغل: `python test_all_modules.py`
2. أعد تشغيل النظام

#### مشاكل في التصميم
**الحل**:
1. امسح ذاكرة التخزين المؤقت
2. أعد تحميل الصفحة
3. تحقق من اتصال الإنترنت

---

## 📞 الدعم والمساعدة

### الملفات المرجعية
- `تقرير_تفعيل_جميع_الوحدات.md` - تقرير شامل
- `🎉_تم_الانتهاء_بنجاح_🎉.md` - ملخص الإنجازات
- `test_all_modules.py` - اختبار شامل

### الاختبار السريع
```bash
# اختبار جميع الوحدات
python test_all_modules.py

# تشغيل النظام
python app.py
```

### معلومات النظام
- **المنفذ**: 5000
- **قاعدة البيانات**: SQLite
- **الإطار**: Flask
- **اللغة**: Python

---

## 🎯 نصائح للاستخدام الأمثل

### الأداء
- أغلق الوحدات غير المستخدمة
- استخدم التصفية لتقليل البيانات
- نظف ذاكرة التخزين المؤقت دورياً

### الأمان
- غير كلمة المرور الافتراضية
- سجل الخروج بعد الانتهاء
- لا تشارك معلومات الدخول

### الصيانة
- اعمل نسخة احتياطية من قاعدة البيانات
- حدث النظام دورياً
- راقب مساحة التخزين

---

## 📋 قائمة مرجعية سريعة

### ✅ قبل البدء
- [ ] تثبيت Python
- [ ] تثبيت المتطلبات
- [ ] تشغيل الاختبارات
- [ ] فتح المتصفح

### ✅ أثناء الاستخدام
- [ ] تسجيل الدخول
- [ ] اختبار جميع الوحدات
- [ ] تجربة الوظائف
- [ ] فحص البيانات

### ✅ بعد الانتهاء
- [ ] حفظ البيانات المهمة
- [ ] تسجيل الخروج
- [ ] إيقاف الخادم
- [ ] عمل نسخة احتياطية

---

## 🎉 الخلاصة

### النظام جاهز!
جميع الوحدات السبعة مفعلة ومختبرة:
1. ✅ المحاسبة
2. ✅ الموارد البشرية
3. ✅ المخزون
4. ✅ المبيعات
5. ✅ المشتريات
6. ✅ المشاريع
7. ✅ إدارة العملاء

### ابدأ الآن!
**🌐 الرابط**: http://localhost:5000  
**🔑 المستخدم**: hr_manager  
**🔑 كلمة المرور**: 123456

---

**🇶🇦 نظام إدارة الموارد المتكامل - دولة قطر**  
**📅 آخر تحديث**: 2024-12-19  
**✅ الحالة**: جميع الوحدات مفعلة وجاهزة**

**🎯 استمتع بالاستخدام!**
