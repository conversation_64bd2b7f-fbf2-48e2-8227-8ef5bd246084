import os
from datetime import timedelta

class Config:
    # إعدادات قاعدة البيانات
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'qatar-erp-system-2024'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///erp_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)

    # إعدادات التطبيق
    APP_NAME = 'نظام إدارة الموارد - دولة قطر'
    COMPANY_NAME = 'دولة قطر'

    # إعدادات اللغة والمنطقة
    LANGUAGES = ['ar', 'en']
    DEFAULT_LANGUAGE = 'ar'
    TIMEZONE = 'Asia/Qatar'

    # إعدادات الملفات
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # ألوان علم قطر
    QATAR_COLORS = {
        'primary': '#8B1538',    # العنابي الداكن
        'secondary': '#FFFFFF',   # الأبيض
        'accent': '#A91B47',     # العنابي الفاتح
        'dark': '#5D0E26',       # العنابي الأغمق
        'light': '#F8F9FA'       # الرمادي الفاتح
    }
