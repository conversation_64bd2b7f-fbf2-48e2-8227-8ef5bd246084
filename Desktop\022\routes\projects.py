from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.projects import Project, Task, ProjectMember
from database_setup import db

bp = Blueprint('projects', __name__, url_prefix='/projects')

@bp.route('/')
@login_required
def index():
    """صفحة المشاريع الرئيسية"""
    # جلب جميع البيانات المطلوبة للقالب
    projects = Project.query.all()

    # حساب الإحصائيات
    active_projects = len([p for p in projects if p.status == 'active'])
    completed_projects = len([p for p in projects if p.status == 'completed'])
    total_budget = sum(project.budget or 0 for project in projects)

    # إحصائيات إضافية
    stats = {
        'active_projects': active_projects,
        'completed_projects': completed_projects,
        'pending_tasks': Task.query.filter_by(status='pending').count() if Task else 0
    }

    return render_template('projects/index.html',
                         projects=projects,
                         active_projects=active_projects,
                         completed_projects=completed_projects,
                         total_budget=total_budget,
                         stats=stats)

@bp.route('/list')
@login_required
def projects_list():
    """قائمة المشاريع"""
    projects = Project.query.order_by(Project.created_at.desc()).all()
    return render_template('projects/list.html', projects=projects)

@bp.route('/tasks')
@login_required
def tasks():
    """المهام"""
    tasks = Task.query.order_by(Task.created_at.desc()).limit(50).all()
    return render_template('projects/tasks.html', tasks=tasks)
