from datetime import datetime
from decimal import Decimal
from database_setup import db

class Product(db.Model):
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description = db.Column(db.Text)

    # تصنيف المنتج
    category = db.Column(db.String(50))
    subcategory = db.Column(db.String(50))
    brand = db.Column(db.String(50))

    # وحدات القياس
    unit_of_measure = db.Column(db.String(20), nullable=False)  # piece, kg, liter, meter

    # الأسعار
    cost_price = db.Column(db.Numeric(10, 2))
    selling_price = db.Column(db.Numeric(10, 2))

    # معلومات المخزون
    current_stock = db.Column(db.Numeric(10, 2), default=0)
    minimum_stock = db.Column(db.Numeric(10, 2), default=0)
    maximum_stock = db.Column(db.Numeric(10, 2), default=0)
    reorder_point = db.Column(db.Numeric(10, 2), default=0)

    # حالة المنتج
    is_active = db.Column(db.Boolean, default=True)
    is_service = db.Column(db.Boolean, default=False)
    track_inventory = db.Column(db.Boolean, default=True)

    # معلومات إضافية
    barcode = db.Column(db.String(50))
    image_url = db.Column(db.String(255))
    weight = db.Column(db.Numeric(8, 3))
    dimensions = db.Column(db.String(50))

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    stock_movements = db.relationship('StockMovement', backref='product', lazy='dynamic')

    def __repr__(self):
        return f'<Product {self.code} - {self.name_ar}>'

    def update_stock(self, quantity, movement_type, warehouse_id=None):
        """تحديث المخزون"""
        if movement_type == 'in':
            self.current_stock += quantity
        elif movement_type == 'out':
            if self.current_stock >= quantity:
                self.current_stock -= quantity
            else:
                raise ValueError("الكمية المطلوبة غير متوفرة في المخزون")

        # إنشاء حركة مخزون
        movement = StockMovement(
            product_id=self.id,
            warehouse_id=warehouse_id,
            movement_type=movement_type,
            quantity=quantity,
            balance_after=self.current_stock,
            date=datetime.utcnow().date()
        )
        db.session.add(movement)

    def is_low_stock(self):
        """فحص إذا كان المخزون منخفض"""
        return self.current_stock <= self.minimum_stock

class Warehouse(db.Model):
    __tablename__ = 'warehouses'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))

    # معلومات المستودع
    location = db.Column(db.String(255))
    manager_id = db.Column(db.Integer, db.ForeignKey('employees.id'))

    # حالة المستودع
    is_active = db.Column(db.Boolean, default=True)
    is_main = db.Column(db.Boolean, default=False)

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    stock_movements = db.relationship('StockMovement', backref='warehouse', lazy='dynamic')
    manager = db.relationship('Employee', backref='managed_warehouses')

    def __repr__(self):
        return f'<Warehouse {self.code} - {self.name_ar}>'

class StockMovement(db.Model):
    __tablename__ = 'stock_movements'

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))

    # تفاصيل الحركة
    movement_type = db.Column(db.String(20), nullable=False)  # in, out, transfer, adjustment
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit_cost = db.Column(db.Numeric(10, 2))

    # الرصيد
    balance_before = db.Column(db.Numeric(10, 2))
    balance_after = db.Column(db.Numeric(10, 2))

    # معلومات المرجع
    reference_type = db.Column(db.String(20))  # purchase, sale, transfer, adjustment
    reference_id = db.Column(db.Integer)
    reference_number = db.Column(db.String(50))

    # تفاصيل إضافية
    date = db.Column(db.Date, nullable=False)
    notes = db.Column(db.Text)

    # المستخدم والتواريخ
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<StockMovement {self.movement_type} - {self.quantity}>'
