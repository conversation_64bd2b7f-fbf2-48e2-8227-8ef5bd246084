from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from app import db

bp = Blueprint('settings', __name__, url_prefix='/settings')

@bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للإعدادات"""
    return render_template('settings/index.html')

@bp.route('/general')
@login_required
def general():
    """الإعدادات العامة"""
    settings = SystemSetting.query.filter_by(category='general').all()
    return render_template('settings/general.html', settings=settings)

@bp.route('/notifications')
@login_required
def notifications():
    """إعدادات الإشعارات"""
    from models.settings import SystemSetting, UserPreference
    from models.notifications import NotificationTemplate

    # إعدادات الإشعارات
    notification_settings = SystemSetting.query.filter_by(category='notifications').all()

    # قوالب الإشعارات
    templates = NotificationTemplate.query.all()

    # تفضيلات المستخدم للإشعارات
    user_prefs = UserPreference.query.filter_by(
        user_id=current_user.id,
        category='notifications'
    ).all()

    user_preferences = {pref.key: pref.get_value() for pref in user_prefs}

    return render_template('settings/notifications.html',
                         settings=notification_settings,
                         templates=templates,
                         user_preferences=user_preferences)

@bp.route('/security')
@login_required
def security():
    """إعدادات الأمان"""
    if current_user.role != 'admin':
        flash('غير مصرح لك بالوصول لإعدادات الأمان', 'error')
        return redirect(url_for('settings.index'))

    security_settings = SystemSetting.query.filter_by(category='security').all()

    # سجل التدقيق الأخير
    recent_logs = AuditLog.query.order_by(AuditLog.timestamp.desc()).limit(20).all()

    return render_template('settings/security.html',
                         settings=security_settings,
                         recent_logs=recent_logs)

@bp.route('/modules')
@login_required
def modules():
    """إعدادات الوحدات"""
    if current_user.role != 'admin':
        flash('غير مصرح لك بإدارة الوحدات', 'error')
        return redirect(url_for('settings.index'))

    modules = ModuleConfiguration.query.all()
    return render_template('settings/modules.html', modules=modules)

@bp.route('/preferences')
@login_required
def preferences():
    """تفضيلات المستخدم"""
    user_prefs = UserPreference.query.filter_by(user_id=current_user.id).all()
    preferences = {pref.key: pref.get_value() for pref in user_prefs}

    return render_template('settings/preferences.html', preferences=preferences)

@bp.route('/backup')
@login_required
def backup():
    """إعدادات النسخ الاحتياطي"""
    if current_user.role != 'admin':
        flash('غير مصرح لك بالوصول لإعدادات النسخ الاحتياطي', 'error')
        return redirect(url_for('settings.index'))

    backup_settings = SystemSetting.query.filter_by(category='backup').all()
    return render_template('settings/backup.html', settings=backup_settings)

# ===== APIs للإعدادات =====

@bp.route('/api/update', methods=['POST'])
@login_required
def update_setting():
    """تحديث إعداد"""
    try:
        data = request.get_json()
        setting_key = data.get('key')
        new_value = data.get('value')

        setting = SystemSetting.query.filter_by(key=setting_key).first()

        if not setting:
            return jsonify({'error': 'الإعداد غير موجود'}), 404

        # فحص الصلاحيات
        if setting.is_system and current_user.role != 'admin':
            return jsonify({'error': 'غير مصرح لك بتعديل هذا الإعداد'}), 403

        # حفظ القيمة القديمة
        old_value = setting.get_value()

        # تحديث الإعداد
        setting.set_value(new_value, current_user.id)

        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='setting_updated',
            entity_type='system_setting',
            entity_id=setting.id,
            old_values={'value': old_value},
            new_values={'value': new_value},
            user_id=current_user.id,
            category='settings'
        )

        flash('تم تحديث الإعداد بنجاح', 'success')
        return jsonify({'success': True, 'message': 'تم تحديث الإعداد بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث الإعداد: {str(e)}'}), 500

@bp.route('/api/module/<module_name>/toggle', methods=['POST'])
@login_required
def toggle_module(module_name):
    """تفعيل/إيقاف وحدة"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإدارة الوحدات'}), 403

    try:
        module = ModuleConfiguration.query.filter_by(module_name=module_name).first()

        if not module:
            return jsonify({'error': 'الوحدة غير موجودة'}), 404

        # تبديل حالة التفعيل
        old_status = module.is_enabled
        module.is_enabled = not module.is_enabled
        module.updated_at = datetime.utcnow()

        db.session.commit()

        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='module_toggled',
            entity_type='module_configuration',
            entity_id=module.id,
            old_values={'is_enabled': old_status},
            new_values={'is_enabled': module.is_enabled},
            user_id=current_user.id,
            category='modules'
        )

        status_text = 'تم تفعيل' if module.is_enabled else 'تم إيقاف'
        flash(f'{status_text} وحدة {module.module_title} بنجاح', 'success')

        return jsonify({
            'success': True,
            'message': f'{status_text} الوحدة بنجاح',
            'is_enabled': module.is_enabled
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في تبديل حالة الوحدة: {str(e)}'}), 500

@bp.route('/api/notification-template', methods=['POST'])
@login_required
def create_notification_template():
    """إنشاء قالب إشعار جديد"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإنشاء قوالب الإشعارات'}), 403

    try:
        data = request.get_json()

        template = NotificationTemplate(
            name=data['name'],
            title_template=data['title_template'],
            message_template=data['message_template'],
            type=data['type'],
            priority=data.get('priority', 'normal'),
            icon=data.get('icon'),
            is_active=data.get('is_active', True),
            auto_send=data.get('auto_send', False)
        )

        db.session.add(template)
        db.session.commit()

        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='notification_template_created',
            entity_type='notification_template',
            entity_id=template.id,
            new_values={
                'name': template.name,
                'type': template.type,
                'priority': template.priority
            },
            user_id=current_user.id,
            category='notifications'
        )

        flash('تم إنشاء قالب الإشعار بنجاح', 'success')
        return jsonify({'success': True, 'message': 'تم إنشاء القالب بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء قالب الإشعار: {str(e)}'}), 500

@bp.route('/api/test-notification', methods=['POST'])
@login_required
def test_notification():
    """اختبار إرسال إشعار"""
    try:
        data = request.get_json()

        notification = NotificationService.create_notification(
            user_id=current_user.id,
            title=data.get('title', 'إشعار تجريبي'),
            message=data.get('message', 'هذا إشعار تجريبي لاختبار النظام'),
            notification_type=data.get('type', 'info'),
            priority=data.get('priority', 'normal'),
            icon=data.get('icon', 'fas fa-test')
        )

        return jsonify({
            'success': True,
            'message': 'تم إرسال الإشعار التجريبي بنجاح',
            'notification_id': notification.id
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في إرسال الإشعار التجريبي: {str(e)}'}), 500

@bp.route('/api/preferences/update', methods=['POST'])
@login_required
def update_preferences():
    """تحديث تفضيلات المستخدم"""
    try:
        data = request.get_json()

        for key, value in data.items():
            preference = UserPreference.query.filter_by(
                user_id=current_user.id,
                key=key
            ).first()

            if preference:
                old_value = preference.get_value()
                preference.value = str(value)
                preference.updated_at = datetime.utcnow()
            else:
                old_value = None
                preference = UserPreference(
                    user_id=current_user.id,
                    key=key,
                    value=str(value),
                    category='general'
                )
                db.session.add(preference)

            # تسجيل في سجل التدقيق
            AuditLog.log_action(
                action='user_preference_updated',
                entity_type='user_preference',
                entity_id=preference.id if hasattr(preference, 'id') else None,
                old_values={'value': old_value},
                new_values={'value': value},
                user_id=current_user.id,
                category='preferences'
            )

        db.session.commit()

        flash('تم تحديث التفضيلات بنجاح', 'success')
        return jsonify({'success': True, 'message': 'تم تحديث التفضيلات بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث التفضيلات: {str(e)}'}), 500

@bp.route('/api/reset-defaults', methods=['POST'])
@login_required
def reset_to_defaults():
    """إعادة تعيين الإعدادات للقيم الافتراضية"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإعادة تعيين الإعدادات'}), 403

    try:
        data = request.get_json()
        category = data.get('category', 'general')

        settings = SystemSetting.query.filter_by(category=category).all()
        reset_count = 0

        for setting in settings:
            if setting.default_value is not None:
                old_value = setting.get_value()
                setting.value = setting.default_value
                setting.updated_at = datetime.utcnow()
                setting.updated_by = current_user.id
                reset_count += 1

                # تسجيل في سجل التدقيق
                AuditLog.log_action(
                    action='setting_reset_to_default',
                    entity_type='system_setting',
                    entity_id=setting.id,
                    old_values={'value': old_value},
                    new_values={'value': setting.default_value},
                    user_id=current_user.id,
                    category='settings'
                )

        db.session.commit()

        flash(f'تم إعادة تعيين {reset_count} إعداد للقيم الافتراضية', 'success')
        return jsonify({
            'success': True,
            'message': f'تم إعادة تعيين {reset_count} إعداد بنجاح',
            'reset_count': reset_count
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في إعادة تعيين الإعدادات: {str(e)}'}), 500
