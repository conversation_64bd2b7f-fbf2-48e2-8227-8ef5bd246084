@echo off
chcp 65001 > nul
title نظام إدارة الموارد المتكامل - دولة قطر

echo.
echo ===============================================================================
echo                    🇶🇦 نظام إدارة الموارد المتكامل - دولة قطر 🇶🇦
echo ===============================================================================
echo.
echo 🚀 بدء تشغيل النظام مع جميع الوحدات المفعلة...
echo.

echo 📊 فحص النظام...
if not exist "app.py" (
    echo ❌ خطأ: ملف app.py غير موجود
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo ✅ ملفات النظام موجودة

echo.
echo 🔧 تحديث المتطلبات...
pip install -r requirements.txt > nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: قد تحتاج لتثبيت بعض المتطلبات يدوياً
) else (
    echo ✅ تم تحديث المتطلبات بنجاح
)

echo.
echo 🧪 اختبار جميع الوحدات...
python test_all_modules.py > nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: بعض الاختبارات قد فشلت، لكن النظام قد يعمل
) else (
    echo ✅ جميع الوحدات تعمل بنجاح
)

echo.
echo ===============================================================================
echo                              🎯 الوحدات المفعلة
echo ===============================================================================
echo.
echo   1. 💰 المحاسبة                    - http://localhost:5000/accounting
echo   2. 👥 الموارد البشرية             - http://localhost:5000/hr
echo   3. 📦 المخزون                     - http://localhost:5000/inventory
echo   4. 🛒 المبيعات                    - http://localhost:5000/sales
echo   5. 🛍️  المشتريات                  - http://localhost:5000/procurement
echo   6. 📋 المشاريع                    - http://localhost:5000/projects
echo   7. 🤝 إدارة العملاء               - http://localhost:5000/crm
echo.
echo ===============================================================================

echo.
echo 🔑 معلومات تسجيل الدخول:
echo    المستخدم: hr_manager
echo    كلمة المرور: 123456
echo.

echo 🌐 سيتم فتح النظام في المتصفح تلقائياً...
echo.

echo 🚀 بدء تشغيل الخادم...
echo.
echo ===============================================================================
echo                              🎉 النظام جاهز!
echo ===============================================================================
echo.
echo 📱 الرابط المحلي: http://localhost:5000
echo 🌍 الرابط الشبكي: http://*************:5000
echo.
echo 💡 نصائح:
echo    - استخدم Ctrl+C لإيقاف الخادم
echo    - تأكد من أن المنفذ 5000 غير مستخدم
echo    - في حالة المشاكل، راجع ملف تقرير_تفعيل_جميع_الوحدات.md
echo.
echo ===============================================================================

timeout /t 3 > nul

start http://localhost:5000

python app.py

echo.
echo 🛑 تم إيقاف الخادم
echo.
pause
