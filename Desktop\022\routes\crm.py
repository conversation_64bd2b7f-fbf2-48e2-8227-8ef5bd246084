from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.crm import Lead, Contact, Activity
from database_setup import db

bp = Blueprint('crm', __name__, url_prefix='/crm')

@bp.route('/')
@login_required
def index():
    """صفحة CRM الرئيسية"""
    try:
        # جلب جميع البيانات المطلوبة للقالب
        customers = Contact.query.filter_by(is_active=True).all()
        leads = Lead.query.all()

        # حساب الإحصائيات
        active_customers = len([c for c in customers if c.is_active])
        potential_customers = len([l for l in leads if l.status in ['new', 'contacted']])
        total_revenue = sum(float(lead.estimated_value or 0) for lead in leads)

        # إحصائيات إضافية
        try:
            pending_activities = Activity.query.filter_by(status='planned').count()
        except:
            pending_activities = 0

        try:
            qualified_leads = Lead.query.filter_by(status='qualified').count()
        except:
            qualified_leads = 0

        stats = {
            'total_leads': len(leads),
            'qualified_leads': qualified_leads,
            'total_contacts': len(customers),
            'pending_activities': pending_activities
        }

        return render_template('crm/index.html',
                             customers=customers,
                             leads=leads,
                             active_customers=active_customers,
                             potential_customers=potential_customers,
                             total_revenue=total_revenue,
                             stats=stats)
    except Exception as e:
        # في حالة حدوث خطأ، إرجاع بيانات فارغة
        return render_template('crm/index.html',
                             customers=[],
                             leads=[],
                             active_customers=0,
                             potential_customers=0,
                             total_revenue=0,
                             stats={
                                 'total_leads': 0,
                                 'qualified_leads': 0,
                                 'total_contacts': 0,
                                 'pending_activities': 0
                             })

@bp.route('/leads')
@login_required
def leads():
    """العملاء المحتملين"""
    leads = Lead.query.order_by(Lead.created_at.desc()).all()
    return render_template('crm/leads.html', leads=leads)

@bp.route('/contacts')
@login_required
def contacts():
    """جهات الاتصال"""
    contacts = Contact.query.filter_by(is_active=True).all()
    return render_template('crm/contacts.html', contacts=contacts)

@bp.route('/activities')
@login_required
def activities():
    """الأنشطة"""
    try:
        activities = Activity.query.order_by(Activity.activity_date.desc()).limit(50).all()
    except:
        activities = []
    return render_template('crm/activities.html', activities=activities)
