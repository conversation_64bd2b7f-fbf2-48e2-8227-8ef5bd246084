from datetime import datetime
from decimal import Decimal
from database_setup import db

class Project(db.Model):
    __tablename__ = 'projects'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description = db.Column(db.Text)

    # معلومات المشروع
    project_type = db.Column(db.String(20))  # internal, external, government
    priority = db.Column(db.String(10), default='medium')  # low, medium, high, urgent

    # التواريخ
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    actual_start_date = db.Column(db.Date)
    actual_end_date = db.Column(db.Date)

    # الميزانية
    budget = db.Column(db.Numeric(15, 2))
    actual_cost = db.Column(db.Numeric(15, 2), default=0)

    # حالة المشروع
    status = db.Column(db.String(20), default='planning')  # planning, active, on_hold, completed, cancelled
    progress_percentage = db.Column(db.Integer, default=0)

    # معلومات العميل
    client_id = db.Column(db.Integer, db.ForeignKey('customers.id'))

    # مدير المشروع
    project_manager_id = db.Column(db.Integer, db.ForeignKey('employees.id'))

    # معلومات إضافية
    location = db.Column(db.String(255))
    notes = db.Column(db.Text)

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    tasks = db.relationship('Task', backref='project', lazy='dynamic')
    members = db.relationship('ProjectMember', backref='project', lazy='dynamic')
    client = db.relationship('Customer')
    project_manager = db.relationship('Employee', foreign_keys=[project_manager_id])
    creator = db.relationship('User')

    def __repr__(self):
        return f'<Project {self.code} - {self.name_ar}>'

    def calculate_progress(self):
        """حساب نسبة الإنجاز"""
        total_tasks = self.tasks.count()
        if total_tasks == 0:
            return 0

        completed_tasks = self.tasks.filter_by(status='completed').count()
        self.progress_percentage = int((completed_tasks / total_tasks) * 100)
        return self.progress_percentage

    def get_team_members(self):
        """الحصول على أعضاء الفريق"""
        return [member.employee for member in self.members.filter_by(is_active=True)]

    def is_overdue(self):
        """فحص إذا كان المشروع متأخر"""
        if self.end_date and self.status not in ['completed', 'cancelled']:
            return datetime.utcnow().date() > self.end_date
        return False

class Task(db.Model):
    __tablename__ = 'tasks'

    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)

    # معلومات المهمة
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    priority = db.Column(db.String(10), default='medium')  # low, medium, high, urgent

    # التواريخ
    start_date = db.Column(db.Date)
    due_date = db.Column(db.Date)
    completed_date = db.Column(db.Date)

    # التقدير والوقت الفعلي
    estimated_hours = db.Column(db.Numeric(6, 2))
    actual_hours = db.Column(db.Numeric(6, 2), default=0)

    # حالة المهمة
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    progress_percentage = db.Column(db.Integer, default=0)

    # المسؤول عن المهمة
    assigned_to = db.Column(db.Integer, db.ForeignKey('employees.id'))

    # المهمة الأساسية (للمهام الفرعية)
    parent_task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))

    # معلومات إضافية
    notes = db.Column(db.Text)

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    assignee = db.relationship('Employee', backref='assigned_tasks')
    creator = db.relationship('User')
    subtasks = db.relationship('Task', backref=db.backref('parent_task', remote_side=[id]))

    def __repr__(self):
        return f'<Task {self.title}>'

    def is_overdue(self):
        """فحص إذا كانت المهمة متأخرة"""
        if self.due_date and self.status not in ['completed', 'cancelled']:
            return datetime.utcnow().date() > self.due_date
        return False

    def complete_task(self):
        """إكمال المهمة"""
        self.status = 'completed'
        self.progress_percentage = 100
        self.completed_date = datetime.utcnow().date()

class ProjectMember(db.Model):
    __tablename__ = 'project_members'

    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)

    # دور العضو في المشروع
    role = db.Column(db.String(50))  # manager, developer, designer, analyst, tester

    # تواريخ المشاركة
    join_date = db.Column(db.Date, default=datetime.utcnow().date())
    leave_date = db.Column(db.Date)

    # حالة العضوية
    is_active = db.Column(db.Boolean, default=True)

    # معلومات إضافية
    hourly_rate = db.Column(db.Numeric(8, 2))  # معدل الساعة للمشروع
    notes = db.Column(db.Text)

    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee')

    def __repr__(self):
        return f'<ProjectMember {self.employee_id} in {self.project_id}>'
