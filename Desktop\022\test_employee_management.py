#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف إدارة الموظفين
"""

from app import app
from database_setup import db
from models.hr import Employee
from datetime import datetime, date
import json

def test_employee_management():
    """اختبار وظائف إدارة الموظفين"""
    
    print("🧪 اختبار وظائف إدارة الموظفين...")
    print("=" * 60)
    
    with app.app_context():
        # عرض الموظفين الحاليين
        current_employees = Employee.query.all()
        print(f"📊 عدد الموظفين الحاليين: {len(current_employees)}")
        
        if current_employees:
            print("\n👥 قائمة الموظفين الحاليين:")
            for emp in current_employees:
                print(f"   - {emp.full_name_ar} ({emp.employee_number}) - {emp.department}")
        
        # اختبار إضافة موظف جديد
        print("\n🆕 اختبار إضافة موظف جديد...")
        
        # بيانات موظف تجريبي
        new_employee_data = {
            'first_name_ar': 'سارة',
            'last_name_ar': 'أحمد المهندي',
            'first_name_en': 'Sara',
            'last_name_en': 'Ahmed Al-Mohannadi',
            'national_id': '29876543210',
            'birth_date': '1990-03-15',
            'gender': 'female',
            'employee_number': f'EMP{datetime.now().year}999',
            'department': 'marketing',
            'position': 'مدير التسويق',
            'hire_date': date.today().strftime('%Y-%m-%d'),
            'employment_status': 'active',
            'basic_salary': 12000.00,
            'allowances': 1500.00,
            'email': '<EMAIL>',
            'phone': '+974 5555 9999',
            'address': 'الدوحة، قطر'
        }
        
        # التحقق من عدم وجود موظف بنفس الرقم
        existing_emp = Employee.query.filter_by(employee_number=new_employee_data['employee_number']).first()
        if existing_emp:
            print(f"⚠️ موظف برقم {new_employee_data['employee_number']} موجود مسبقاً")
        else:
            try:
                from decimal import Decimal
                
                # إنشاء موظف جديد
                new_employee = Employee(
                    first_name_ar=new_employee_data['first_name_ar'],
                    last_name_ar=new_employee_data['last_name_ar'],
                    first_name_en=new_employee_data['first_name_en'],
                    last_name_en=new_employee_data['last_name_en'],
                    national_id=new_employee_data['national_id'],
                    birth_date=datetime.strptime(new_employee_data['birth_date'], '%Y-%m-%d').date(),
                    gender=new_employee_data['gender'],
                    employee_number=new_employee_data['employee_number'],
                    department=new_employee_data['department'],
                    position=new_employee_data['position'],
                    hire_date=datetime.strptime(new_employee_data['hire_date'], '%Y-%m-%d').date(),
                    employment_status=new_employee_data['employment_status'],
                    basic_salary=Decimal(str(new_employee_data['basic_salary'])),
                    allowances=Decimal(str(new_employee_data['allowances'])),
                    email=new_employee_data['email'],
                    phone=new_employee_data['phone'],
                    address=new_employee_data['address']
                )
                
                db.session.add(new_employee)
                db.session.commit()
                
                print(f"✅ تم إضافة الموظف: {new_employee.full_name_ar}")
                print(f"   📧 البريد الإلكتروني: {new_employee.email}")
                print(f"   💰 الراتب الإجمالي: {float(new_employee.basic_salary + new_employee.allowances):,.0f} ر.ق")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة الموظف: {e}")
                db.session.rollback()
        
        # اختبار تحديث بيانات موظف
        print("\n🔄 اختبار تحديث بيانات موظف...")
        
        first_employee = Employee.query.first()
        if first_employee:
            old_phone = first_employee.phone
            new_phone = '+974 5555 0000'
            
            first_employee.phone = new_phone
            db.session.commit()
            
            print(f"✅ تم تحديث رقم هاتف {first_employee.full_name_ar}")
            print(f"   📞 من: {old_phone or 'غير محدد'} إلى: {new_phone}")
        
        # اختبار البحث والتصفية
        print("\n🔍 اختبار البحث والتصفية...")
        
        # البحث حسب القسم
        hr_employees = Employee.query.filter_by(department='hr').all()
        print(f"👥 موظفو الموارد البشرية: {len(hr_employees)}")
        
        # البحث حسب الحالة
        active_employees = Employee.query.filter_by(employment_status='active').all()
        print(f"✅ الموظفون النشطون: {len(active_employees)}")
        
        # اختبار تصدير البيانات
        print("\n📤 اختبار تصدير البيانات...")
        
        all_employees = Employee.query.all()
        export_data = []
        
        for emp in all_employees:
            export_data.append({
                'رقم_الموظف': emp.employee_number,
                'الاسم_الكامل': emp.full_name_ar,
                'القسم': emp.department or 'غير محدد',
                'المنصب': emp.position or 'غير محدد',
                'تاريخ_التوظيف': emp.hire_date.strftime('%Y-%m-%d') if emp.hire_date else '',
                'الحالة': {
                    'active': 'نشط',
                    'inactive': 'غير نشط',
                    'terminated': 'منتهي الخدمة'
                }.get(emp.employment_status, emp.employment_status or ''),
                'الراتب_الأساسي': float(emp.basic_salary) if emp.basic_salary else 0,
                'البدلات': float(emp.allowances) if emp.allowances else 0,
                'إجمالي_الراتب': float((emp.basic_salary or 0) + (emp.allowances or 0)),
                'البريد_الإلكتروني': emp.email or '',
                'رقم_الهاتف': emp.phone or ''
            })
        
        # حفظ البيانات المصدرة في ملف
        export_filename = f'test_employees_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(export_filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم تصدير {len(export_data)} موظف إلى ملف: {export_filename}")
        
        # إحصائيات الموظفين
        print("\n📊 إحصائيات الموظفين:")
        
        # حسب القسم
        departments = db.session.query(Employee.department, db.func.count(Employee.id)).group_by(Employee.department).all()
        print("   📂 توزيع الموظفين حسب القسم:")
        for dept, count in departments:
            dept_name = {
                'hr': 'الموارد البشرية',
                'finance': 'المالية',
                'it': 'تقنية المعلومات',
                'operations': 'العمليات',
                'marketing': 'التسويق',
                'sales': 'المبيعات'
            }.get(dept, dept or 'غير محدد')
            print(f"      - {dept_name}: {count} موظف")
        
        # حسب الحالة
        statuses = db.session.query(Employee.employment_status, db.func.count(Employee.id)).group_by(Employee.employment_status).all()
        print("   📈 توزيع الموظفين حسب الحالة:")
        for status, count in statuses:
            status_name = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'terminated': 'منتهي الخدمة'
            }.get(status, status or 'غير محدد')
            print(f"      - {status_name}: {count} موظف")
        
        # متوسط الرواتب
        avg_salary = db.session.query(db.func.avg(Employee.basic_salary + Employee.allowances)).scalar()
        if avg_salary:
            print(f"   💰 متوسط الرواتب: {float(avg_salary):,.0f} ر.ق")
        
        # أعلى وأقل راتب
        max_salary = db.session.query(db.func.max(Employee.basic_salary + Employee.allowances)).scalar()
        min_salary = db.session.query(db.func.min(Employee.basic_salary + Employee.allowances)).scalar()
        
        if max_salary and min_salary:
            print(f"   📈 أعلى راتب: {float(max_salary):,.0f} ر.ق")
            print(f"   📉 أقل راتب: {float(min_salary):,.0f} ر.ق")
        
        print("\n" + "=" * 60)
        print("🎯 ملخص الاختبار:")
        print("✅ تم اختبار جميع وظائف إدارة الموظفين")
        print("📊 إضافة، تحديث، بحث، وتصدير الموظفين")
        print("📈 إحصائيات شاملة للموظفين")
        print("💾 تصدير البيانات بتنسيق JSON")
        print("\n🌐 يمكنك الآن استخدام واجهة إدارة الموظفين في:")
        print("   http://localhost:5000/hr/employees")
        print("\n🔑 استخدم: hr_manager / 123456 للدخول")

if __name__ == '__main__':
    print("🚀 بدء اختبار وظائف إدارة الموظفين")
    print("=" * 60)
    
    test_employee_management()
    
    print("\n🎉 انتهى الاختبار بنجاح!")
    print("💡 تلميح: تأكد من تشغيل النظام باستخدام 'python app.py' لاختبار الواجهة")
