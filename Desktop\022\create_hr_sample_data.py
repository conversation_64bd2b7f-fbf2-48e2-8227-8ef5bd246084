#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لوحدة الموارد البشرية
"""

from app import app
from database_setup import db
from models.hr import Employee, Attendance, Payroll, LeaveRequest
from datetime import datetime, date, time, timedelta
from decimal import Decimal
import random

def create_hr_sample_data():
    """إنشاء بيانات تجريبية للموارد البشرية"""
    
    with app.app_context():
        print("🔄 إنشاء بيانات تجريبية للموارد البشرية...")
        
        # الحصول على الموظفين الموجودين
        employees = Employee.query.all()
        
        if not employees:
            print("❌ لا يوجد موظفين في النظام. يرجى تشغيل البيانات التجريبية الأساسية أولاً.")
            return
        
        # إنشاء سجلات حضور للأسبوع الماضي
        today = date.today()
        
        for i in range(7):  # آخر 7 أيام
            attendance_date = today - timedelta(days=i)
            
            # تخطي عطلة نهاية الأسبوع
            if attendance_date.weekday() >= 5:  # السبت والأحد
                continue
            
            for employee in employees:
                # فحص إذا كان هناك سجل حضور موجود
                existing_attendance = Attendance.query.filter_by(
                    employee_id=employee.id,
                    date=attendance_date
                ).first()
                
                if not existing_attendance:
                    # إنشاء سجل حضور عشوائي
                    status_options = ['present', 'present', 'present', 'late', 'absent']
                    status = random.choice(status_options)
                    
                    attendance = Attendance(
                        employee_id=employee.id,
                        date=attendance_date,
                        status=status
                    )
                    
                    if status in ['present', 'late']:
                        # أوقات عشوائية للحضور والانصراف
                        check_in_hour = random.randint(7, 9)
                        check_in_minute = random.randint(0, 59)
                        attendance.check_in = time(check_in_hour, check_in_minute)
                        
                        check_out_hour = random.randint(15, 18)
                        check_out_minute = random.randint(0, 59)
                        attendance.check_out = time(check_out_hour, check_out_minute)
                        
                        # حساب ساعات العمل
                        attendance.calculate_hours()
                    
                    db.session.add(attendance)
        
        # إنشاء كشوف رواتب للشهر الماضي
        last_month = today.replace(day=1) - timedelta(days=1)
        pay_period_start = last_month.replace(day=1)
        pay_period_end = last_month
        
        for employee in employees:
            # فحص إذا كان هناك كشف راتب موجود
            existing_payroll = Payroll.query.filter_by(
                employee_id=employee.id,
                pay_period_start=pay_period_start,
                pay_period_end=pay_period_end
            ).first()
            
            if not existing_payroll:
                # حساب الراتب الأساسي
                basic_salary = employee.basic_salary or Decimal('10000.00')
                allowances = employee.allowances or Decimal('1000.00')
                
                # مبالغ عشوائية للساعات الإضافية والمكافآت
                overtime_amount = Decimal(random.randint(0, 2000))
                bonus = Decimal(random.randint(0, 1000))
                
                # خصومات عشوائية
                tax_deduction = basic_salary * Decimal('0.05')  # 5% ضريبة
                insurance_deduction = basic_salary * Decimal('0.02')  # 2% تأمين
                loan_deduction = Decimal(random.randint(0, 500))
                other_deductions = Decimal(random.randint(0, 200))
                
                payroll = Payroll(
                    employee_id=employee.id,
                    pay_period_start=pay_period_start,
                    pay_period_end=pay_period_end,
                    basic_salary=basic_salary,
                    allowances=allowances,
                    overtime_amount=overtime_amount,
                    bonus=bonus,
                    tax_deduction=tax_deduction,
                    insurance_deduction=insurance_deduction,
                    loan_deduction=loan_deduction,
                    other_deductions=other_deductions,
                    status='approved'
                )
                
                # حساب الإجماليات
                payroll.calculate_totals()
                
                db.session.add(payroll)
        
        # إنشاء طلبات إجازات
        leave_types = ['annual', 'sick', 'emergency', 'maternity', 'unpaid']
        statuses = ['pending', 'approved', 'rejected']
        
        for i in range(10):  # 10 طلبات إجازة
            employee = random.choice(employees)
            leave_type = random.choice(leave_types)
            status = random.choice(statuses)
            
            # تواريخ عشوائية للإجازة
            start_date = today + timedelta(days=random.randint(1, 60))
            days_requested = random.randint(1, 14)
            end_date = start_date + timedelta(days=days_requested - 1)
            
            # أسباب مختلفة للإجازة
            reasons = [
                'إجازة عائلية للسفر',
                'ظروف شخصية طارئة',
                'إجازة مرضية للعلاج',
                'حضور مناسبة عائلية',
                'راحة واستجمام',
                'إنجاز معاملات شخصية',
                'السفر للخارج',
                'ظروف صحية',
                'مناسبة زواج',
                'إجازة أمومة'
            ]
            
            leave_request = LeaveRequest(
                employee_id=employee.id,
                leave_type=leave_type,
                start_date=start_date,
                end_date=end_date,
                days_requested=days_requested,
                reason=random.choice(reasons),
                status=status,
                created_at=datetime.utcnow() - timedelta(days=random.randint(1, 30))
            )
            
            if status in ['approved', 'rejected']:
                leave_request.review_date = datetime.utcnow() - timedelta(days=random.randint(1, 10))
                if status == 'rejected':
                    leave_request.review_notes = 'لا يمكن الموافقة على الإجازة في هذا التوقيت'
                else:
                    leave_request.review_notes = 'تم الموافقة على الطلب'
            
            db.session.add(leave_request)
        
        # حفظ جميع البيانات
        db.session.commit()
        
        print("✅ تم إنشاء البيانات التجريبية للموارد البشرية بنجاح!")
        print(f"   📅 سجلات حضور للأسبوع الماضي")
        print(f"   💰 كشوف رواتب للشهر الماضي")
        print(f"   🏖️  10 طلبات إجازة متنوعة")
        print("\n🌐 يمكنك الآن استكشاف وحدة الموارد البشرية")

if __name__ == '__main__':
    create_hr_sample_data()
