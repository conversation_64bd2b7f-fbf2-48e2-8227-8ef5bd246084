{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-user me-2 text-primary"></i>
            الملف الشخصي
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">الملف الشخصي</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i>
                معلومات المستخدم
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>الاسم الكامل:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.full_name }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>اسم المستخدم:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.username }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>البريد الإلكتروني:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.email }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>الدور:</strong>
                    </div>
                    <div class="col-sm-9">
                        <span class="badge bg-primary">{{ user.role }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>القسم:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.department or 'غير محدد' }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>المنصب:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.position or 'غير محدد' }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>تاريخ الإنشاء:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>آخر تسجيل دخول:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يتم التسجيل من قبل' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cogs me-2"></i>
                الإعدادات
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </a>
                    
                    <a href="{{ url_for('auth.activities') }}" class="btn btn-outline-info">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <i class="fas fa-shield-alt me-2"></i>
                الصلاحيات
            </div>
            <div class="card-body">
                {% set dashboard_data = user.get_dashboard_data() %}
                <p class="text-muted mb-2">الوحدات المتاحة:</p>
                <ul class="list-unstyled">
                    {% for module in dashboard_data.modules %}
                    <li class="mb-1">
                        <i class="fas fa-check text-success me-2"></i>
                        {{ module }}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
