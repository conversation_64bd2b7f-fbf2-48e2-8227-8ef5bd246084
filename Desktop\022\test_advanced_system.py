#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المتقدم - نظام إدارة الموارد المتكامل
دولة قطر 🇶🇦
"""

import requests
import json
import time
from datetime import datetime

class AdvancedSystemTester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def print_header(self):
        """طباعة رأس الاختبار"""
        print("🚀 بدء اختبار النظام المتقدم")
        print("=" * 60)
        print("🧪 اختبار جميع الوحدات والميزات المتقدمة...")
        print("=" * 60)
    
    def login(self, username='admin', password='admin123'):
        """تسجيل الدخول"""
        print("🔑 تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول
        login_page = self.session.get(f"{self.base_url}/login")
        if login_page.status_code == 200:
            print("✅ تم الوصول لصفحة تسجيل الدخول")
        else:
            print("❌ فشل في الوصول لصفحة تسجيل الدخول")
            return False
        
        # تسجيل الدخول
        login_data = {
            'username': username,
            'password': password
        }
        
        response = self.session.post(f"{self.base_url}/login", data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تم تسجيل الدخول بنجاح")
            return True
        else:
            print("❌ فشل في تسجيل الدخول")
            return False
    
    def test_page(self, path, name):
        """اختبار صفحة معينة"""
        print(f"🔍 اختبار: {path}")
        
        try:
            response = self.session.get(f"{self.base_url}{path}")
            
            if response.status_code == 200:
                print(f"✅ {path} يعمل بنجاح")
                self.test_results.append({'path': path, 'name': name, 'status': 'Success', 'code': 200})
                return True
            else:
                print(f"⚠️ {path} رمز الحالة: {response.status_code}")
                self.test_results.append({'path': path, 'name': name, 'status': f'Status {response.status_code}', 'code': response.status_code})
                return False
                
        except Exception as e:
            print(f"❌ {path} خطأ: {str(e)}")
            self.test_results.append({'path': path, 'name': name, 'status': f'Error: {str(e)}', 'code': 0})
            return False
    
    def test_api_endpoint(self, path, name):
        """اختبار نقطة API"""
        print(f"🔍 اختبار API: {path}")
        
        try:
            response = self.session.get(f"{self.base_url}{path}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ {path} API يعمل بنجاح")
                    self.test_results.append({'path': path, 'name': name, 'status': 'Success', 'code': 200})
                    return True
                except:
                    print(f"⚠️ {path} استجابة غير صحيحة")
                    self.test_results.append({'path': path, 'name': name, 'status': 'Invalid Response', 'code': 200})
                    return False
            else:
                print(f"⚠️ {path} رمز الحالة: {response.status_code}")
                self.test_results.append({'path': path, 'name': name, 'status': f'Status {response.status_code}', 'code': response.status_code})
                return False
                
        except Exception as e:
            print(f"❌ {path} خطأ: {str(e)}")
            self.test_results.append({'path': path, 'name': name, 'status': f'Error: {str(e)}', 'code': 0})
            return False
    
    def test_core_modules(self):
        """اختبار الوحدات الأساسية"""
        print("\n📊 اختبار الوحدات الأساسية...")
        
        core_modules = [
            ('/', 'الصفحة الرئيسية'),
            ('/login', 'تسجيل الدخول'),
            ('/dashboard', 'لوحة التحكم'),
            ('/accounting', 'المحاسبة'),
            ('/hr', 'الموارد البشرية'),
            ('/inventory', 'المخزون'),
            ('/sales', 'المبيعات'),
            ('/procurement', 'المشتريات'),
            ('/projects', 'المشاريع'),
            ('/crm', 'إدارة العملاء')
        ]
        
        for path, name in core_modules:
            self.test_page(path, name)
            time.sleep(0.5)  # تأخير قصير بين الطلبات
    
    def test_advanced_modules(self):
        """اختبار الوحدات المتقدمة"""
        print("\n🚀 اختبار الوحدات المتقدمة...")
        
        advanced_modules = [
            ('/admin', 'لوحة الإدارة'),
            ('/admin/modules', 'إدارة الوحدات'),
            ('/admin/users', 'إدارة المستخدمين'),
            ('/admin/settings', 'إعدادات النظام'),
            ('/admin/logs', 'سجلات النظام'),
            ('/reports', 'التقارير المتقدمة'),
            ('/reports/financial', 'التقارير المالية'),
            ('/reports/hr', 'تقارير الموارد البشرية'),
            ('/reports/operational', 'التقارير التشغيلية'),
            ('/reports/custom', 'التقارير المخصصة'),
            ('/reports/analytics', 'تحليلات البيانات')
        ]
        
        for path, name in advanced_modules:
            self.test_page(path, name)
            time.sleep(0.5)
    
    def test_api_endpoints(self):
        """اختبار نقاط API"""
        print("\n🔌 اختبار نقاط API...")
        
        api_endpoints = [
            ('/reports/api/generate/employee_summary', 'تقرير ملخص الموظفين'),
            ('/reports/api/generate/financial_overview', 'النظرة المالية العامة'),
            ('/reports/api/generate/sales_analysis', 'تحليل المبيعات'),
            ('/reports/api/generate/inventory_status', 'حالة المخزون'),
            ('/reports/api/generate/project_progress', 'تقدم المشاريع')
        ]
        
        for path, name in api_endpoints:
            self.test_api_endpoint(path, name)
            time.sleep(0.5)
    
    def test_security_features(self):
        """اختبار ميزات الأمان"""
        print("\n🔒 اختبار ميزات الأمان...")
        
        # اختبار الوصول بدون تسجيل دخول
        test_session = requests.Session()
        
        protected_pages = [
            '/dashboard',
            '/admin',
            '/reports'
        ]
        
        for page in protected_pages:
            response = test_session.get(f"{self.base_url}{page}")
            if 'login' in response.url or response.status_code == 401:
                print(f"✅ {page} محمي بشكل صحيح")
            else:
                print(f"⚠️ {page} قد يكون غير محمي")
    
    def test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        test_pages = [
            '/dashboard',
            '/accounting',
            '/hr',
            '/reports'
        ]
        
        for page in test_pages:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}{page}")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response_time < 2.0:
                print(f"✅ {page} سريع ({response_time:.2f}s)")
            elif response_time < 5.0:
                print(f"⚠️ {page} متوسط ({response_time:.2f}s)")
            else:
                print(f"❌ {page} بطيء ({response_time:.2f}s)")
    
    def generate_report(self):
        """إنتاج تقرير الاختبار"""
        print("\n" + "=" * 60)
        print("📋 ملخص نتائج الاختبار:")
        print("=" * 60)
        
        success_count = len([r for r in self.test_results if r['status'] == 'Success'])
        total_count = len(self.test_results)
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'Success' else "❌"
            print(f"{status_icon} {result['path']:<20} - {result['status']}")
        
        print("\n" + "=" * 60)
        print("📊 الإحصائيات النهائية:")
        print(f"✅ صفحات تعمل بنجاح: {success_count}")
        print(f"❌ صفحات بها أخطاء: {total_count - success_count}")
        print(f"📄 إجمالي الصفحات: {total_count}")
        
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        if success_rate == 100:
            print("\n🎉 جميع الاختبارات نجحت! النظام المتقدم يعمل بشكل مثالي")
        elif success_rate >= 80:
            print(f"\n✅ معدل النجاح: {success_rate:.1f}% - النظام يعمل بشكل جيد")
        elif success_rate >= 60:
            print(f"\n⚠️ معدل النجاح: {success_rate:.1f}% - يحتاج بعض الإصلاحات")
        else:
            print(f"\n❌ معدل النجاح: {success_rate:.1f}% - يحتاج إصلاحات كبيرة")
        
        print("\n" + "=" * 60)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط الرئيسي: http://localhost:5000")
        print("🔑 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        
        print("\n📋 الوحدات المتاحة:")
        modules = [
            "✅ 💰 المحاسبة والتقارير المالية",
            "✅ 👥 الموارد البشرية والرواتب", 
            "✅ 📦 إدارة المخزون والمنتجات",
            "✅ 🛒 المبيعات وإدارة العملاء",
            "✅ 🛍️ المشتريات والموردين",
            "✅ 📋 إدارة المشاريع والمهام",
            "✅ 🤝 نظام CRM المتقدم",
            "✅ ⚙️ لوحة الإدارة المتقدمة",
            "✅ 📊 التقارير والتحليلات المتقدمة",
            "✅ 🔔 نظام الإشعارات الذكية"
        ]
        
        for module in modules:
            print(module)
        
        print("\n🎯 تم الانتهاء من الاختبار!")
        
        if success_rate == 100:
            print("\n🎉 الاختبار نجح! النظام المتقدم جاهز للاستخدام")
        else:
            print("\n⚠️ الاختبار كشف عن بعض المشاكل")
        
        print("\n💡 تلميح: تأكد من تشغيل النظام باستخدام 'python start_advanced_system.py' قبل الاختبار")
    
    def run_full_test(self):
        """تشغيل الاختبار الكامل"""
        self.print_header()
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return
        
        # تشغيل جميع الاختبارات
        self.test_core_modules()
        self.test_advanced_modules()
        self.test_api_endpoints()
        self.test_security_features()
        self.test_performance()
        
        # إنتاج التقرير
        self.generate_report()

def main():
    """الوظيفة الرئيسية"""
    tester = AdvancedSystemTester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
