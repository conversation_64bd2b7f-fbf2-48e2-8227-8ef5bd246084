from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from models.user import User, UserActivity
from database_setup import db

bp = Blueprint('auth', __name__, url_prefix='/auth')

@bp.route('/profile')
@login_required
def profile():
    """صفحة الملف الشخصي"""
    return render_template('auth/profile.html', user=current_user)

@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    if request.method == 'POST':
        current_password = request.form['current_password']
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']

        # التحقق من كلمة المرور الحالية
        if not check_password_hash(current_user.password_hash, current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('auth/change_password.html')

        # التحقق من تطابق كلمة المرور الجديدة
        if new_password != confirm_password:
            flash('كلمة المرور الجديدة غير متطابقة', 'error')
            return render_template('auth/change_password.html')

        # تحديث كلمة المرور
        from werkzeug.security import generate_password_hash
        current_user.password_hash = generate_password_hash(new_password)

        # تسجيل النشاط
        activity = UserActivity(
            user_id=current_user.id,
            action='password_changed',
            description='تم تغيير كلمة المرور',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.add(activity)
        db.session.commit()

        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html')

@bp.route('/activities')
@login_required
def activities():
    """عرض أنشطة المستخدم"""
    user_activities = UserActivity.query.filter_by(user_id=current_user.id)\
                                       .order_by(UserActivity.timestamp.desc())\
                                       .limit(50).all()
    return render_template('auth/activities.html', activities=user_activities)
