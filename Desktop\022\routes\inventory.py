from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.inventory import Product, Warehouse, StockMovement
from database_setup import db

bp = Blueprint('inventory', __name__, url_prefix='/inventory')

@bp.route('/')
@login_required
def index():
    """صفحة المخزون الرئيسية"""
    stats = {
        'total_products': Product.query.filter_by(is_active=True).count(),
        'low_stock_products': Product.query.filter(Product.current_stock <= Product.minimum_stock).count(),
        'total_warehouses': Warehouse.query.filter_by(is_active=True).count()
    }
    return render_template('inventory/index.html', stats=stats)

@bp.route('/products')
@login_required
def products():
    """قائمة المنتجات"""
    products = Product.query.filter_by(is_active=True).all()
    return render_template('inventory/products.html', products=products)

@bp.route('/warehouses')
@login_required
def warehouses():
    """المستودعات"""
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    return render_template('inventory/warehouses.html', warehouses=warehouses)

@bp.route('/stock_movements')
@login_required
def stock_movements():
    """حركات المخزون"""
    movements = StockMovement.query.order_by(StockMovement.created_at.desc()).limit(100).all()
    return render_template('inventory/stock_movements.html', movements=movements)
