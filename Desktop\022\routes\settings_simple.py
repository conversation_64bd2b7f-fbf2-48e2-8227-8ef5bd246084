from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime

bp = Blueprint('settings', __name__, url_prefix='/settings')

@bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للإعدادات"""
    return render_template('settings/index.html')

@bp.route('/general')
@login_required
def general():
    """الإعدادات العامة"""
    return render_template('settings/general.html', settings=[])

@bp.route('/notifications')
@login_required
def notifications():
    """إعدادات الإشعارات"""
    return render_template('settings/notifications.html',
                         settings=[],
                         templates=[],
                         user_preferences={})

@bp.route('/security')
@login_required
def security():
    """إعدادات الأمان"""
    if current_user.role != 'admin':
        flash('غير مصرح لك بالوصول لإعدادات الأمان', 'error')
        return redirect(url_for('settings.index'))

    return render_template('settings/security.html',
                         settings=[],
                         recent_logs=[])

@bp.route('/modules')
@login_required
def modules():
    """إعدادات الوحدات"""
    if current_user.role != 'admin':
        flash('غير مصرح لك بإدارة الوحدات', 'error')
        return redirect(url_for('settings.index'))

    return render_template('settings/modules.html', modules=[])

@bp.route('/preferences')
@login_required
def preferences():
    """تفضيلات المستخدم"""
    return render_template('settings/preferences.html', preferences={})

@bp.route('/backup')
@login_required
def backup():
    """إعدادات النسخ الاحتياطي"""
    if current_user.role != 'admin':
        flash('غير مصرح لك بالوصول لإعدادات النسخ الاحتياطي', 'error')
        return redirect(url_for('settings.index'))

    return render_template('settings/backup.html', settings=[])

# ===== APIs للإعدادات =====

@bp.route('/api/update', methods=['POST'])
@login_required
def update_setting():
    """تحديث إعداد"""
    try:
        data = request.get_json()
        flash('تم تحديث الإعداد بنجاح', 'success')
        return jsonify({'success': True, 'message': 'تم تحديث الإعداد بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث الإعداد: {str(e)}'}), 500

@bp.route('/api/module/<module_name>/toggle', methods=['POST'])
@login_required
def toggle_module(module_name):
    """تفعيل/إيقاف وحدة"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإدارة الوحدات'}), 403

    try:
        flash(f'تم تبديل حالة وحدة {module_name} بنجاح', 'success')

        return jsonify({
            'success': True,
            'message': f'تم تبديل حالة الوحدة بنجاح',
            'is_enabled': True
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في تبديل حالة الوحدة: {str(e)}'}), 500

@bp.route('/api/notification-template', methods=['POST'])
@login_required
def create_notification_template():
    """إنشاء قالب إشعار جديد"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإنشاء قوالب الإشعارات'}), 403

    try:
        flash('تم إنشاء قالب الإشعار بنجاح', 'success')
        return jsonify({'success': True, 'message': 'تم إنشاء القالب بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء قالب الإشعار: {str(e)}'}), 500

@bp.route('/api/test-notification', methods=['POST'])
@login_required
def test_notification():
    """اختبار إرسال إشعار"""
    try:
        data = request.get_json()

        # محاكاة إنشاء إشعار
        return jsonify({
            'success': True,
            'message': 'تم إرسال الإشعار التجريبي بنجاح',
            'notification_id': 1
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في إرسال الإشعار التجريبي: {str(e)}'}), 500

@bp.route('/api/preferences/update', methods=['POST'])
@login_required
def update_preferences():
    """تحديث تفضيلات المستخدم"""
    try:
        flash('تم تحديث التفضيلات بنجاح', 'success')
        return jsonify({'success': True, 'message': 'تم تحديث التفضيلات بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث التفضيلات: {str(e)}'}), 500

@bp.route('/api/reset-defaults', methods=['POST'])
@login_required
def reset_to_defaults():
    """إعادة تعيين الإعدادات للقيم الافتراضية"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بإعادة تعيين الإعدادات'}), 403

    try:
        flash('تم إعادة تعيين الإعدادات للقيم الافتراضية', 'success')
        return jsonify({
            'success': True,
            'message': 'تم إعادة تعيين الإعدادات بنجاح',
            'reset_count': 5
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في إعادة تعيين الإعدادات: {str(e)}'}), 500
