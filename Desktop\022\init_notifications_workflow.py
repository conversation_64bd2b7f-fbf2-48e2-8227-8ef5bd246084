"""
تهيئة البيانات الأولية لنظام الإشعارات وسير العمل
"""

from app import app, db
from models.settings import SystemSetting, ModuleConfiguration
from models.notifications import NotificationTemplate
from services.workflow_service import LeaveRequestWorkflow
from datetime import datetime

def init_system_settings():
    """تهيئة إعدادات النظام"""

    # إعدادات الإشعارات
    notification_settings = [
        {
            'key': 'notifications_enabled',
            'value': 'true',
            'name_ar': 'تفعيل الإشعارات',
            'name_en': 'Enable Notifications',
            'description': 'تفعيل أو إيقاف نظام الإشعارات',
            'setting_type': 'boolean',
            'category': 'notifications',
            'default_value': 'true',
            'is_required': True,
            'is_system': False
        },
        {
            'key': 'notification_retention_days',
            'value': '30',
            'name_ar': 'مدة الاحتفاظ بالإشعارات',
            'name_en': 'Notification Retention Days',
            'description': 'عدد الأيام للاحتفاظ بالإشعارات المقروءة',
            'setting_type': 'integer',
            'category': 'notifications',
            'default_value': '30',
            'is_required': True,
            'is_system': False
        },
        {
            'key': 'auto_cleanup_enabled',
            'value': 'true',
            'name_ar': 'التنظيف التلقائي',
            'name_en': 'Auto Cleanup Enabled',
            'description': 'تفعيل التنظيف التلقائي للإشعارات القديمة',
            'setting_type': 'boolean',
            'category': 'notifications',
            'default_value': 'true',
            'is_required': True,
            'is_system': False
        }
    ]

    # إعدادات سير العمل
    workflow_settings = [
        {
            'key': 'workflow_enabled',
            'value': 'true',
            'name_ar': 'تفعيل سير العمل',
            'name_en': 'Enable Workflow',
            'description': 'تفعيل أو إيقاف نظام سير العمل',
            'setting_type': 'boolean',
            'category': 'workflow',
            'default_value': 'true',
            'is_required': True,
            'is_system': False
        },
        {
            'key': 'workflow_timeout_hours',
            'value': '24',
            'name_ar': 'مهلة سير العمل (ساعات)',
            'name_en': 'Workflow Timeout Hours',
            'description': 'المهلة الافتراضية لخطوات سير العمل بالساعات',
            'setting_type': 'integer',
            'category': 'workflow',
            'default_value': '24',
            'is_required': True,
            'is_system': False
        }
    ]

    # إعدادات الأمان
    security_settings = [
        {
            'key': 'audit_log_enabled',
            'value': 'true',
            'name_ar': 'تفعيل سجل التدقيق',
            'name_en': 'Enable Audit Log',
            'description': 'تفعيل تسجيل جميع العمليات في سجل التدقيق',
            'setting_type': 'boolean',
            'category': 'security',
            'default_value': 'true',
            'is_required': True,
            'is_system': True
        },
        {
            'key': 'audit_log_retention_days',
            'value': '180',
            'name_ar': 'مدة الاحتفاظ بسجل التدقيق',
            'name_en': 'Audit Log Retention Days',
            'description': 'عدد الأيام للاحتفاظ بسجلات التدقيق',
            'setting_type': 'integer',
            'category': 'security',
            'default_value': '180',
            'is_required': True,
            'is_system': True
        }
    ]

    # إعدادات النسخ الاحتياطي
    backup_settings = [
        {
            'key': 'auto_backup_enabled',
            'value': 'true',
            'name_ar': 'النسخ الاحتياطي التلقائي',
            'name_en': 'Auto Backup Enabled',
            'description': 'تفعيل النسخ الاحتياطي التلقائي',
            'setting_type': 'boolean',
            'category': 'backup',
            'default_value': 'true',
            'is_required': True,
            'is_system': True
        },
        {
            'key': 'backup_frequency_hours',
            'value': '24',
            'name_ar': 'تكرار النسخ الاحتياطي (ساعات)',
            'name_en': 'Backup Frequency Hours',
            'description': 'تكرار النسخ الاحتياطي التلقائي بالساعات',
            'setting_type': 'integer',
            'category': 'backup',
            'default_value': '24',
            'is_required': True,
            'is_system': True
        }
    ]

    all_settings = notification_settings + workflow_settings + security_settings + backup_settings

    for setting_data in all_settings:
        existing = SystemSetting.query.filter_by(key=setting_data['key']).first()
        if not existing:
            setting = SystemSetting(**setting_data)
            db.session.add(setting)

    db.session.commit()
    print("✓ تم إنشاء إعدادات النظام")

def init_notification_templates():
    """تهيئة قوالب الإشعارات"""

    templates = [
        {
            'name': 'employee_added',
            'title_template': 'موظف جديد',
            'message_template': 'تم إضافة موظف جديد: {employee_name}',
            'type': 'success',
            'priority': 'normal',
            'icon': 'fas fa-user-plus',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'leave_request_submitted',
            'title_template': 'طلب إجازة جديد',
            'message_template': 'تم تقديم طلب إجازة من {employee_name} من {start_date} إلى {end_date}',
            'type': 'info',
            'priority': 'normal',
            'icon': 'fas fa-calendar-alt',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'leave_request_approved',
            'title_template': 'تم الموافقة على طلب الإجازة',
            'message_template': 'تم الموافقة على طلب إجازتك من {start_date} إلى {end_date}',
            'type': 'success',
            'priority': 'high',
            'icon': 'fas fa-check-circle',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'leave_request_rejected',
            'title_template': 'تم رفض طلب الإجازة',
            'message_template': 'تم رفض طلب إجازتك من {start_date} إلى {end_date}. السبب: {reason}',
            'type': 'error',
            'priority': 'high',
            'icon': 'fas fa-times-circle',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'invoice_created',
            'title_template': 'فاتورة جديدة',
            'message_template': 'تم إنشاء فاتورة جديدة رقم {invoice_number} بقيمة {amount}',
            'type': 'info',
            'priority': 'normal',
            'icon': 'fas fa-file-invoice',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'payment_received',
            'title_template': 'تم استلام دفعة',
            'message_template': 'تم استلام دفعة بقيمة {amount} من العميل {customer_name}',
            'type': 'success',
            'priority': 'normal',
            'icon': 'fas fa-money-bill-wave',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'system_backup_completed',
            'title_template': 'تم إنشاء نسخة احتياطية',
            'message_template': 'تم إنشاء النسخة الاحتياطية بنجاح: {filename}',
            'type': 'success',
            'priority': 'low',
            'icon': 'fas fa-download',
            'is_active': True,
            'auto_send': True
        },
        {
            'name': 'system_error',
            'title_template': 'خطأ في النظام',
            'message_template': 'حدث خطأ في النظام: {error_message}',
            'type': 'error',
            'priority': 'urgent',
            'icon': 'fas fa-exclamation-triangle',
            'is_active': True,
            'auto_send': True
        }
    ]

    for template_data in templates:
        existing = NotificationTemplate.query.filter_by(name=template_data['name']).first()
        if not existing:
            template = NotificationTemplate(**template_data)
            db.session.add(template)

    db.session.commit()
    print("✓ تم إنشاء قوالب الإشعارات")

def init_module_configurations():
    """تهيئة إعدادات الوحدات"""

    modules = [
        {
            'module_name': 'accounting',
            'module_title': 'المحاسبة',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"currency": "QAR", "fiscal_year_start": "01-01"}'
        },
        {
            'module_name': 'hr',
            'module_title': 'الموارد البشرية',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"working_hours": 8, "weekend_days": ["friday", "saturday"]}'
        },
        {
            'module_name': 'inventory',
            'module_title': 'المخزون',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"auto_reorder": true, "low_stock_threshold": 10}'
        },
        {
            'module_name': 'sales',
            'module_title': 'المبيعات',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"tax_rate": 0.05, "discount_limit": 0.20}'
        },
        {
            'module_name': 'procurement',
            'module_title': 'المشتريات',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"approval_limit": 10000, "auto_receive": false}'
        },
        {
            'module_name': 'projects',
            'module_title': 'المشاريع',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"default_project_duration": 30, "task_reminder_days": 3}'
        },
        {
            'module_name': 'crm',
            'module_title': 'إدارة العملاء',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"lead_followup_days": 7, "auto_convert_leads": false}'
        },
        {
            'module_name': 'notifications',
            'module_title': 'الإشعارات',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"real_time": true, "email_notifications": false}'
        },
        {
            'module_name': 'workflow',
            'module_title': 'سير العمل',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"auto_assign": true, "escalation_enabled": true}'
        },
        {
            'module_name': 'reports',
            'module_title': 'التقارير',
            'is_enabled': True,
            'version': '1.0.0',
            'configuration': '{"auto_generate": false, "export_formats": ["pdf", "excel"]}'
        }
    ]

    for module_data in modules:
        existing = ModuleConfiguration.query.filter_by(module_name=module_data['module_name']).first()
        if not existing:
            module = ModuleConfiguration(**module_data)
            db.session.add(module)

    db.session.commit()
    print("✓ تم إنشاء إعدادات الوحدات")

def init_workflow_definitions():
    """تهيئة تعريفات سير العمل"""

    try:
        # إنشاء سير عمل طلبات الإجازات
        LeaveRequestWorkflow.setup_default_workflow()
        print("✓ تم إنشاء سير عمل طلبات الإجازات")
    except Exception as e:
        print(f"⚠ خطأ في إنشاء سير العمل: {str(e)}")

def main():
    """تشغيل تهيئة البيانات الأولية"""

    with app.app_context():
        print("🚀 بدء تهيئة نظام الإشعارات وسير العمل...")

        # إنشاء الجداول
        db.create_all()
        print("✓ تم إنشاء جداول قاعدة البيانات")

        # تهيئة البيانات
        init_system_settings()
        init_notification_templates()
        init_module_configurations()
        init_workflow_definitions()

        print("🎉 تم إكمال تهيئة النظام بنجاح!")
        print("\nالميزات المتاحة:")
        print("• نظام الإشعارات المتقدم")
        print("• سير العمل والموافقات")
        print("• لوحة الإدارة المتقدمة")
        print("• إعدادات النظام الشاملة")
        print("• سجل التدقيق والأمان")
        print("• النسخ الاحتياطي التلقائي")

if __name__ == '__main__':
    main()
