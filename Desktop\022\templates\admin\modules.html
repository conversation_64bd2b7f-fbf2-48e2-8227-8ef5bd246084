{% extends "base.html" %}

{% block title %}إدارة الوحدات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-puzzle-piece me-2"></i>
                        إدارة الوحدات
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة الإدارة</a></li>
                            <li class="breadcrumb-item active">إدارة الوحدات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-success" onclick="installNewModule()">
                        <i class="fas fa-plus me-2"></i>
                        تثبيت وحدة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الوحدات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <h3>{{ modules|selectattr('status', 'equalto', 'active')|list|length }}</h3>
                    <p class="mb-0">وحدات نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <h3>{{ modules|selectattr('status', 'equalto', 'beta')|list|length }}</h3>
                    <p class="mb-0">وحدات تجريبية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <h3>{{ modules|selectattr('status', 'equalto', 'development')|list|length }}</h3>
                    <p class="mb-0">قيد التطوير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <h3>{{ modules|length }}</h3>
                    <p class="mb-0">إجمالي الوحدات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الوحدات -->
    <div class="row">
        {% for module in modules %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card module-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="{{ module.icon }} fa-lg me-2 text-qatar"></i>
                        <h6 class="mb-0">{{ module.title }}</h6>
                    </div>
                    <div>
                        {% if module.status == 'active' %}
                        <span class="badge bg-success">نشط</span>
                        {% elif module.status == 'beta' %}
                        <span class="badge bg-warning">تجريبي</span>
                        {% elif module.status == 'development' %}
                        <span class="badge bg-info">قيد التطوير</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted small">{{ module.description }}</p>
                    
                    <div class="module-info">
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">الإصدار</small>
                                <div class="fw-bold">{{ module.version }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">آخر تحديث</small>
                                <div class="fw-bold">{{ module.last_update }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        {% if module.status == 'active' %}
                        <button class="btn btn-outline-primary btn-sm" onclick="configureModule('{{ module.name }}')">
                            <i class="fas fa-cog"></i> إعدادات
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="toggleModule('{{ module.name }}', false)">
                            <i class="fas fa-pause"></i> إيقاف
                        </button>
                        {% else %}
                        <button class="btn btn-outline-success btn-sm" onclick="toggleModule('{{ module.name }}', true)">
                            <i class="fas fa-play"></i> تفعيل
                        </button>
                        {% endif %}
                        <button class="btn btn-outline-info btn-sm" onclick="viewModuleDetails('{{ module.name }}')">
                            <i class="fas fa-info"></i> تفاصيل
                        </button>
                        {% if module.status != 'active' %}
                        <button class="btn btn-outline-danger btn-sm" onclick="uninstallModule('{{ module.name }}')">
                            <i class="fas fa-trash"></i> إزالة
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- وحدات متاحة للتثبيت -->
    <div class="row mt-5">
        <div class="col-12">
            <h4 class="text-qatar mb-3">
                <i class="fas fa-download me-2"></i>
                وحدات متاحة للتثبيت
            </h4>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card available-module-card">
                <div class="card-body text-center">
                    <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                    <h5>تطبيق الهاتف المحمول</h5>
                    <p class="text-muted">تطبيق للهواتف الذكية للوصول للنظام</p>
                    <button class="btn btn-primary" onclick="installModule('mobile_app')">
                        <i class="fas fa-download me-2"></i>
                        تثبيت
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card available-module-card">
                <div class="card-body text-center">
                    <i class="fas fa-robot fa-3x text-success mb-3"></i>
                    <h5>الذكاء الاصطناعي</h5>
                    <p class="text-muted">مساعد ذكي لتحليل البيانات والتنبؤات</p>
                    <button class="btn btn-success" onclick="installModule('ai_assistant')">
                        <i class="fas fa-download me-2"></i>
                        تثبيت
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card available-module-card">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                    <h5>الأمان المتقدم</h5>
                    <p class="text-muted">نظام أمان متقدم مع المصادقة الثنائية</p>
                    <button class="btn btn-warning" onclick="installModule('advanced_security')">
                        <i class="fas fa-download me-2"></i>
                        تثبيت
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الوحدة -->
<div class="modal fade" id="moduleDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الوحدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="moduleDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<style>
.module-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B1538;
}

.available-module-card {
    transition: all 0.3s ease;
    border: 2px dashed #dee2e6;
}

.available-module-card:hover {
    border-color: #8B1538;
    transform: translateY(-3px);
}

.text-qatar {
    color: #8B1538;
}

.module-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}
</style>

<script>
// وظائف إدارة الوحدات
function toggleModule(moduleName, enable) {
    const action = enable ? 'تفعيل' : 'إيقاف';
    
    if (confirm(`هل تريد ${action} وحدة ${moduleName}؟`)) {
        fetch(`/admin/api/module/${moduleName}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم ${action} الوحدة بنجاح`);
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function configureModule(moduleName) {
    alert(`سيتم فتح إعدادات وحدة ${moduleName}`);
}

function viewModuleDetails(moduleName) {
    // محاكاة تفاصيل الوحدة
    const moduleDetails = {
        'accounting': {
            name: 'المحاسبة',
            description: 'وحدة شاملة لإدارة الحسابات والقيود المحاسبية',
            features: ['دليل الحسابات', 'القيود اليومية', 'التقارير المالية', 'الميزانية العمومية'],
            dependencies: ['قاعدة البيانات', 'نظام المستخدمين'],
            size: '2.5 MB',
            developer: 'فريق التطوير - دولة قطر'
        },
        'hr': {
            name: 'الموارد البشرية',
            description: 'إدارة شاملة للموظفين والحضور والرواتب',
            features: ['إدارة الموظفين', 'نظام الحضور', 'الرواتب', 'الإجازات'],
            dependencies: ['قاعدة البيانات', 'نظام المستخدمين'],
            size: '3.1 MB',
            developer: 'فريق التطوير - دولة قطر'
        }
    };
    
    const details = moduleDetails[moduleName] || {
        name: moduleName,
        description: 'وصف الوحدة غير متاح',
        features: ['ميزة 1', 'ميزة 2'],
        dependencies: ['قاعدة البيانات'],
        size: '1.0 MB',
        developer: 'مطور غير معروف'
    };
    
    document.getElementById('moduleDetailsContent').innerHTML = `
        <h6>اسم الوحدة: ${details.name}</h6>
        <p><strong>الوصف:</strong> ${details.description}</p>
        
        <h6>الميزات:</h6>
        <ul>
            ${details.features.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
        
        <h6>المتطلبات:</h6>
        <ul>
            ${details.dependencies.map(dep => `<li>${dep}</li>`).join('')}
        </ul>
        
        <div class="row">
            <div class="col-6">
                <strong>الحجم:</strong> ${details.size}
            </div>
            <div class="col-6">
                <strong>المطور:</strong> ${details.developer}
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('moduleDetailsModal')).show();
}

function installModule(moduleName) {
    if (confirm(`هل تريد تثبيت وحدة ${moduleName}؟`)) {
        alert(`سيتم تثبيت وحدة ${moduleName}. هذه العملية قد تستغرق بضع دقائق.`);
    }
}

function uninstallModule(moduleName) {
    if (confirm(`هل تريد إزالة وحدة ${moduleName}؟ سيتم حذف جميع البيانات المرتبطة بها.`)) {
        alert(`تم حذف وحدة ${moduleName}`);
        location.reload();
    }
}

function installNewModule() {
    alert('سيتم فتح متجر الوحدات لتصفح الوحدات المتاحة');
}
</script>
{% endblock %}
