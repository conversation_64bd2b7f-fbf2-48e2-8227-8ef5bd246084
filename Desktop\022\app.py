from flask import Flask, render_template, request, redirect, url_for, flash
from flask_login import Lo<PERSON>Mana<PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config.from_object('config.Config')

# إنشاء مجلد قاعدة البيانات
os.makedirs('database', exist_ok=True)
os.makedirs('static/uploads', exist_ok=True)

# إعداد قاعدة البيانات
from database_setup import db
db.init_app(app)

# إضافة فلتر تنسيق الأرقام
@app.template_filter('number_format')
def number_format_filter(value):
    """تنسيق الأرقام مع فواصل الآلاف"""
    if value is None or value == '':
        return '0.00'
    try:
        # تحويل إلى رقم عشري
        if isinstance(value, str):
            # إزالة أي رموز غير رقمية
            value = ''.join(c for c in value if c.isdigit() or c in '.-')
            if not value or value in ['.', '-']:
                return '0.00'

        num = float(value)
        # تنسيق مع فواصل الآلاف
        formatted = "{:,.2f}".format(num)
        # استبدال الفواصل بمسافات للعرض العربي
        return formatted.replace(',', ' ')
    except (ValueError, TypeError, AttributeError):
        return '0.00'

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# استيراد النماذج
from models.user import User
from models.accounting import Account, JournalEntry, Transaction
from models.hr import Employee, Attendance, Payroll, LeaveRequest
from models.inventory import Product, Warehouse, StockMovement
from models.sales import Customer, SalesOrder, Invoice
from models.procurement import Supplier, PurchaseOrder
from models.projects import Project, Task, ProjectMember
from models.crm import Lead, Contact, Activity

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات الرئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user, remember=True)
            flash('تم تسجيل الدخول بنجاح', 'success')

            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات سريعة للوحة التحكم
    stats = {
        'total_employees': Employee.query.count(),
        'total_customers': Customer.query.count(),
        'total_suppliers': Supplier.query.count(),
        'total_products': Product.query.count(),
        'pending_orders': SalesOrder.query.filter_by(status='pending').count(),
        'active_projects': Project.query.filter_by(status='active').count()
    }

    return render_template('dashboard/index.html', stats=stats)

# استيراد المسارات
from routes import auth, accounting, hr, inventory, sales, procurement, projects, crm
from routes import api_simple as api
from routes import settings_simple as settings
from routes import admin_simple as admin_advanced

# استيراد الوحدات المتقدمة
# from admin_panel import admin_bp
# from advanced_reports import reports_bp

# تسجيل المسارات
app.register_blueprint(auth.bp)
app.register_blueprint(accounting.bp)
app.register_blueprint(hr.bp)
app.register_blueprint(inventory.bp)
app.register_blueprint(sales.bp)
app.register_blueprint(procurement.bp)
app.register_blueprint(projects.bp)
app.register_blueprint(crm.bp)

# تسجيل المسارات الجديدة
app.register_blueprint(api.bp)
app.register_blueprint(settings.bp)
app.register_blueprint(admin_advanced.bp)

# تسجيل الوحدات المتقدمة
# app.register_blueprint(admin_bp)
# app.register_blueprint(reports_bp)

# إنشاء الجداول وبيانات تجريبية
def create_tables():
    db.create_all()

    # إنشاء مستخدم افتراضي
    if not User.query.filter_by(username='admin').first():
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='admin',
            password_hash=generate_password_hash('admin123'),
            is_active=True
        )
        db.session.add(admin_user)
        db.session.commit()

if __name__ == '__main__':
    with app.app_context():
        create_tables()
    app.run(debug=True, host='0.0.0.0', port=5000)
