from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from models.user import User
from models.settings import SystemSetting, ModuleConfiguration, AuditLog
from models.notifications import Notification, WorkflowInstance
from services.notification_service import NotificationService
from app import db
import os
import json

bp = Blueprint('admin_advanced', __name__, url_prefix='/admin')

def admin_required(f):
    """ديكوريتر للتأكد من صلاحيات المدير"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def dashboard():
    """لوحة الإدارة الرئيسية"""
    try:
        # إحصائيات عامة
        stats = {
            'total_users': User.query.count(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'total_notifications': Notification.query.count(),
            'unread_notifications': Notification.query.filter_by(is_read=False).count(),
            'active_workflows': WorkflowInstance.query.filter_by(status='in_progress').count(),
            'completed_workflows': WorkflowInstance.query.filter_by(status='completed').count(),
            'system_settings': SystemSetting.query.count(),
            'enabled_modules': ModuleConfiguration.query.filter_by(is_enabled=True).count()
        }
        
        # إحصائيات الأسبوع الماضي
        week_ago = datetime.utcnow() - timedelta(days=7)
        weekly_stats = {
            'new_users': User.query.filter(User.created_at >= week_ago).count(),
            'new_notifications': Notification.query.filter(Notification.created_at >= week_ago).count(),
            'completed_workflows': WorkflowInstance.query.filter(
                WorkflowInstance.completed_at >= week_ago,
                WorkflowInstance.status == 'completed'
            ).count(),
            'audit_logs': AuditLog.query.filter(AuditLog.timestamp >= week_ago).count()
        }
        
        # آخر الأنشطة
        recent_activities = AuditLog.query.order_by(AuditLog.timestamp.desc()).limit(10).all()
        
        # حالة الوحدات
        modules = ModuleConfiguration.query.all()
        
        # تحذيرات النظام
        warnings = []
        
        # فحص المساحة (محاكاة)
        if True:  # يمكن إضافة فحص حقيقي للمساحة
            warnings.append({
                'type': 'info',
                'message': 'مساحة قاعدة البيانات: 2.5 MB (طبيعية)',
                'icon': 'fas fa-database'
            })
        
        # فحص النسخ الاحتياطي
        warnings.append({
            'type': 'warning',
            'message': 'آخر نسخة احتياطية: منذ 3 أيام',
            'icon': 'fas fa-exclamation-triangle'
        })
        
        return render_template('admin/dashboard.html',
                             stats=stats,
                             weekly_stats=weekly_stats,
                             recent_activities=recent_activities,
                             modules=modules,
                             warnings=warnings)
    
    except Exception as e:
        flash(f'خطأ في تحميل لوحة الإدارة: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/users')
@login_required
@admin_required
def users():
    """إدارة المستخدمين"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    
    query = User.query
    
    # تطبيق الفلاتر
    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.full_name.contains(search),
                User.email.contains(search)
            )
        )
    
    if role_filter:
        query = query.filter_by(role=role_filter)
    
    if status_filter:
        is_active = status_filter == 'active'
        query = query.filter_by(is_active=is_active)
    
    users = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    # إحصائيات المستخدمين
    user_stats = {
        'total': User.query.count(),
        'active': User.query.filter_by(is_active=True).count(),
        'inactive': User.query.filter_by(is_active=False).count(),
        'admins': User.query.filter_by(role='admin').count(),
        'managers': User.query.filter_by(role='manager').count(),
        'employees': User.query.filter_by(role='employee').count()
    }
    
    return render_template('admin/users.html',
                         users=users,
                         user_stats=user_stats,
                         search=search,
                         role_filter=role_filter,
                         status_filter=status_filter)

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """سجلات النظام"""
    page = request.args.get('page', 1, type=int)
    category_filter = request.args.get('category', '')
    severity_filter = request.args.get('severity', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    query = AuditLog.query
    
    # تطبيق الفلاتر
    if category_filter:
        query = query.filter_by(category=category_filter)
    
    if severity_filter:
        query = query.filter_by(severity=severity_filter)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(AuditLog.timestamp >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(AuditLog.timestamp < date_to_obj)
        except ValueError:
            pass
    
    logs = query.order_by(AuditLog.timestamp.desc()).paginate(
        page=page, per_page=50, error_out=False
    )
    
    # إحصائيات السجلات
    log_stats = {
        'total': AuditLog.query.count(),
        'today': AuditLog.query.filter(
            AuditLog.timestamp >= datetime.utcnow().replace(hour=0, minute=0, second=0)
        ).count(),
        'this_week': AuditLog.query.filter(
            AuditLog.timestamp >= datetime.utcnow() - timedelta(days=7)
        ).count(),
        'critical': AuditLog.query.filter_by(severity='critical').count(),
        'errors': AuditLog.query.filter_by(severity='error').count(),
        'warnings': AuditLog.query.filter_by(severity='warning').count()
    }
    
    # فئات السجلات
    categories = db.session.query(AuditLog.category).distinct().all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    return render_template('admin/logs.html',
                         logs=logs,
                         log_stats=log_stats,
                         categories=categories,
                         category_filter=category_filter,
                         severity_filter=severity_filter,
                         date_from=date_from,
                         date_to=date_to)

@bp.route('/system')
@login_required
@admin_required
def system():
    """معلومات النظام"""
    # معلومات النظام
    system_info = {
        'version': '2.1.0',
        'database_size': '2.5 MB',
        'uptime': '15 يوم، 8 ساعات',
        'last_backup': '2024-12-19 10:00:00',
        'python_version': '3.9.0',
        'flask_version': '2.3.0',
        'database_type': 'SQLite',
        'server_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
    }
    
    # إحصائيات الأداء
    performance_stats = {
        'avg_response_time': '250ms',
        'requests_per_minute': 45,
        'error_rate': '0.1%',
        'memory_usage': '128 MB',
        'cpu_usage': '15%',
        'disk_usage': '45%'
    }
    
    # حالة الخدمات
    services_status = {
        'database': {'status': 'online', 'response_time': '5ms'},
        'file_system': {'status': 'online', 'response_time': '2ms'},
        'notifications': {'status': 'online', 'response_time': '15ms'},
        'workflow': {'status': 'online', 'response_time': '12ms'},
        'backup': {'status': 'warning', 'response_time': '3 days ago'}
    }
    
    return render_template('admin/system.html',
                         system_info=system_info,
                         performance_stats=performance_stats,
                         services_status=services_status)

@bp.route('/backup')
@login_required
@admin_required
def backup():
    """إدارة النسخ الاحتياطي"""
    # قائمة النسخ الاحتياطية (محاكاة)
    backups = [
        {
            'id': 1,
            'filename': 'backup_2024_12_19_10_00.db',
            'size': '2.5 MB',
            'created_at': '2024-12-19 10:00:00',
            'type': 'automatic',
            'status': 'completed'
        },
        {
            'id': 2,
            'filename': 'backup_2024_12_18_10_00.db',
            'size': '2.4 MB',
            'created_at': '2024-12-18 10:00:00',
            'type': 'automatic',
            'status': 'completed'
        },
        {
            'id': 3,
            'filename': 'backup_2024_12_17_15_30.db',
            'size': '2.3 MB',
            'created_at': '2024-12-17 15:30:00',
            'type': 'manual',
            'status': 'completed'
        }
    ]
    
    # إعدادات النسخ الاحتياطي
    backup_settings = SystemSetting.query.filter_by(category='backup').all()
    
    return render_template('admin/backup.html',
                         backups=backups,
                         backup_settings=backup_settings)

# ===== APIs الإدارة =====

@bp.route('/api/user/<int:user_id>/toggle', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """تفعيل/إيقاف مستخدم"""
    try:
        user = User.query.get_or_404(user_id)
        
        if user.id == current_user.id:
            return jsonify({'error': 'لا يمكنك إيقاف حسابك الخاص'}), 400
        
        old_status = user.is_active
        user.is_active = not user.is_active
        
        db.session.commit()
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='user_status_toggled',
            entity_type='user',
            entity_id=user.id,
            old_values={'is_active': old_status},
            new_values={'is_active': user.is_active},
            user_id=current_user.id,
            category='user_management'
        )
        
        status_text = 'تم تفعيل' if user.is_active else 'تم إيقاف'
        return jsonify({
            'success': True,
            'message': f'{status_text} المستخدم بنجاح',
            'is_active': user.is_active
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تبديل حالة المستخدم: {str(e)}'}), 500

@bp.route('/api/backup/create', methods=['POST'])
@login_required
@admin_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        # محاكاة إنشاء نسخة احتياطية
        backup_filename = f"backup_{datetime.utcnow().strftime('%Y_%m_%d_%H_%M')}.db"
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='backup_created',
            entity_type='system',
            old_values=None,
            new_values={'filename': backup_filename},
            user_id=current_user.id,
            category='backup'
        )
        
        # إرسال إشعار
        NotificationService.create_notification(
            user_id=current_user.id,
            title='تم إنشاء نسخة احتياطية',
            message=f'تم إنشاء النسخة الاحتياطية: {backup_filename}',
            notification_type='success',
            icon='fas fa-download'
        )
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
            'filename': backup_filename
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'}), 500

@bp.route('/api/system/cleanup', methods=['POST'])
@login_required
@admin_required
def system_cleanup():
    """تنظيف النظام"""
    try:
        cleanup_stats = {
            'old_notifications': 0,
            'old_logs': 0,
            'temp_files': 0
        }
        
        # تنظيف الإشعارات القديمة
        cleanup_stats['old_notifications'] = NotificationService.cleanup_old_notifications(days=90)
        
        # تنظيف السجلات القديمة (محاكاة)
        old_logs = AuditLog.query.filter(
            AuditLog.timestamp < datetime.utcnow() - timedelta(days=180)
        ).all()
        
        for log in old_logs:
            db.session.delete(log)
        
        cleanup_stats['old_logs'] = len(old_logs)
        
        # محاكاة تنظيف الملفات المؤقتة
        cleanup_stats['temp_files'] = 15
        
        db.session.commit()
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='system_cleanup',
            entity_type='system',
            new_values=cleanup_stats,
            user_id=current_user.id,
            category='maintenance'
        )
        
        return jsonify({
            'success': True,
            'message': 'تم تنظيف النظام بنجاح',
            'stats': cleanup_stats
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تنظيف النظام: {str(e)}'}), 500

@bp.route('/api/send-announcement', methods=['POST'])
@login_required
@admin_required
def send_announcement():
    """إرسال إعلان لجميع المستخدمين"""
    try:
        data = request.get_json()
        title = data.get('title')
        message = data.get('message')
        notification_type = data.get('type', 'info')
        priority = data.get('priority', 'normal')
        
        if not title or not message:
            return jsonify({'error': 'العنوان والرسالة مطلوبان'}), 400
        
        # الحصول على جميع المستخدمين النشطين
        active_users = User.query.filter_by(is_active=True).all()
        user_ids = [user.id for user in active_users]
        
        # إرسال الإشعار لجميع المستخدمين
        notifications = NotificationService.send_to_multiple_users(
            user_ids=user_ids,
            title=title,
            message=message,
            notification_type=notification_type,
            priority=priority,
            icon='fas fa-bullhorn',
            source_type='announcement'
        )
        
        # تسجيل في سجل التدقيق
        AuditLog.log_action(
            action='announcement_sent',
            entity_type='notification',
            new_values={
                'title': title,
                'recipients_count': len(user_ids),
                'type': notification_type
            },
            user_id=current_user.id,
            category='announcements'
        )
        
        return jsonify({
            'success': True,
            'message': f'تم إرسال الإعلان لـ {len(user_ids)} مستخدم',
            'recipients_count': len(user_ids)
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إرسال الإعلان: {str(e)}'}), 500
