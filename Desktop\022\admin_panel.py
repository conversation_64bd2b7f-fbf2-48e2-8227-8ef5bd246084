#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة الإدارة المتقدمة - نظام إدارة الموارد المتكامل
دولة قطر 🇶🇦
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from database_setup import db
from models.user import User
from models.hr import Employee
from models.accounting import Account
from models.inventory import Product
from models.sales import Customer
from models.procurement import Supplier
from models.projects import Project
from models.crm import Lead, Contact
import json
from datetime import datetime, timedelta
import os

# إنشاء Blueprint للوحة الإدارة
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@login_required
def dashboard():
    """لوحة الإدارة الرئيسية"""
    if not current_user.role == 'admin':
        flash('غير مصرح لك بالوصول إلى لوحة الإدارة', 'error')
        return redirect(url_for('dashboard'))
    
    # إحصائيات شاملة للنظام
    stats = {
        'users': {
            'total': User.query.count(),
            'active': User.query.filter_by(is_active=True).count(),
            'admins': User.query.filter_by(role='admin').count(),
            'managers': User.query.filter_by(role='manager').count()
        },
        'employees': {
            'total': Employee.query.count(),
            'active': Employee.query.filter_by(is_active=True).count(),
            'departments': Employee.query.with_entities(Employee.department).distinct().count()
        },
        'financial': {
            'accounts': Account.query.count(),
            'active_accounts': Account.query.filter_by(is_active=True).count(),
            'customers': Customer.query.count(),
            'suppliers': Supplier.query.count()
        },
        'operations': {
            'products': Product.query.count(),
            'projects': Project.query.count(),
            'active_projects': Project.query.filter_by(status='active').count(),
            'leads': Lead.query.count()
        }
    }
    
    # إحصائيات الأداء
    performance = {
        'database_size': get_database_size(),
        'last_backup': get_last_backup_date(),
        'system_uptime': get_system_uptime(),
        'active_sessions': get_active_sessions()
    }
    
    # أحدث الأنشطة
    recent_activities = get_recent_activities()
    
    return render_template('admin/dashboard.html', 
                         stats=stats, 
                         performance=performance,
                         recent_activities=recent_activities)

@admin_bp.route('/modules')
@login_required
def modules():
    """إدارة الوحدات"""
    if not current_user.role == 'admin':
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    # قائمة الوحدات المتاحة
    modules_list = [
        {
            'name': 'accounting',
            'title': 'المحاسبة',
            'icon': 'fas fa-calculator',
            'description': 'إدارة الحسابات والقيود المحاسبية والتقارير المالية',
            'status': 'active',
            'version': '2.1.0',
            'last_update': '2024-12-19'
        },
        {
            'name': 'hr',
            'title': 'الموارد البشرية',
            'icon': 'fas fa-users',
            'description': 'إدارة الموظفين والحضور والرواتب والإجازات',
            'status': 'active',
            'version': '2.0.5',
            'last_update': '2024-12-19'
        },
        {
            'name': 'inventory',
            'title': 'المخزون',
            'icon': 'fas fa-boxes',
            'description': 'إدارة المنتجات والمخازن وحركة المخزون',
            'status': 'active',
            'version': '1.8.2',
            'last_update': '2024-12-18'
        },
        {
            'name': 'sales',
            'title': 'المبيعات',
            'icon': 'fas fa-shopping-cart',
            'description': 'إدارة العملاء والفواتير وأوامر البيع',
            'status': 'active',
            'version': '1.9.1',
            'last_update': '2024-12-19'
        },
        {
            'name': 'procurement',
            'title': 'المشتريات',
            'icon': 'fas fa-shopping-bag',
            'description': 'إدارة الموردين وأوامر الشراء والمشتريات',
            'status': 'active',
            'version': '1.7.3',
            'last_update': '2024-12-19'
        },
        {
            'name': 'projects',
            'title': 'المشاريع',
            'icon': 'fas fa-project-diagram',
            'description': 'إدارة المشاريع والمهام وفرق العمل',
            'status': 'active',
            'version': '1.6.0',
            'last_update': '2024-12-19'
        },
        {
            'name': 'crm',
            'title': 'إدارة العملاء',
            'icon': 'fas fa-handshake',
            'description': 'إدارة العملاء المحتملين والأنشطة التسويقية',
            'status': 'active',
            'version': '1.5.2',
            'last_update': '2024-12-19'
        },
        {
            'name': 'reports',
            'title': 'التقارير المتقدمة',
            'icon': 'fas fa-chart-bar',
            'description': 'تقارير شاملة ولوحات معلومات تفاعلية',
            'status': 'beta',
            'version': '0.9.0',
            'last_update': '2024-12-19'
        },
        {
            'name': 'workflow',
            'title': 'سير العمل',
            'icon': 'fas fa-sitemap',
            'description': 'أتمتة العمليات وسير العمل والموافقات',
            'status': 'development',
            'version': '0.5.0',
            'last_update': '2024-12-15'
        },
        {
            'name': 'notifications',
            'title': 'الإشعارات',
            'icon': 'fas fa-bell',
            'description': 'نظام الإشعارات والتنبيهات الذكية',
            'status': 'beta',
            'version': '0.8.1',
            'last_update': '2024-12-18'
        }
    ]
    
    return render_template('admin/modules.html', modules=modules_list)

@admin_bp.route('/users')
@login_required
def users():
    """إدارة المستخدمين"""
    if not current_user.role == 'admin':
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    users_list = User.query.all()
    return render_template('admin/users.html', users=users_list)

@admin_bp.route('/settings')
@login_required
def settings():
    """إعدادات النظام"""
    if not current_user.role == 'admin':
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    # إعدادات النظام الحالية
    system_settings = {
        'general': {
            'system_name': 'نظام إدارة الموارد المتكامل - دولة قطر',
            'version': '2.1.0',
            'language': 'ar',
            'timezone': 'Asia/Qatar',
            'currency': 'QAR'
        },
        'security': {
            'session_timeout': 480,  # 8 hours
            'password_policy': 'medium',
            'two_factor_auth': False,
            'login_attempts': 5
        },
        'backup': {
            'auto_backup': True,
            'backup_frequency': 'daily',
            'backup_retention': 30,
            'last_backup': get_last_backup_date()
        },
        'notifications': {
            'email_notifications': True,
            'sms_notifications': False,
            'push_notifications': True,
            'notification_frequency': 'immediate'
        }
    }
    
    return render_template('admin/settings.html', settings=system_settings)

@admin_bp.route('/logs')
@login_required
def logs():
    """سجلات النظام"""
    if not current_user.role == 'admin':
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    # محاكاة سجلات النظام
    system_logs = [
        {
            'timestamp': datetime.now() - timedelta(minutes=5),
            'level': 'INFO',
            'module': 'HR',
            'user': 'hr_manager',
            'action': 'تم إضافة موظف جديد',
            'details': 'تم إضافة الموظف أحمد محمد إلى قسم المحاسبة'
        },
        {
            'timestamp': datetime.now() - timedelta(minutes=15),
            'level': 'WARNING',
            'module': 'INVENTORY',
            'user': 'warehouse_manager',
            'action': 'تحذير مخزون منخفض',
            'details': 'المنتج "أقلام حبر" وصل إلى الحد الأدنى للمخزون'
        },
        {
            'timestamp': datetime.now() - timedelta(hours=1),
            'level': 'SUCCESS',
            'module': 'SALES',
            'user': 'sales_manager',
            'action': 'تم إنشاء فاتورة جديدة',
            'details': 'فاتورة رقم INV-2024-001 بقيمة 15,000 ر.ق'
        },
        {
            'timestamp': datetime.now() - timedelta(hours=2),
            'level': 'ERROR',
            'module': 'SYSTEM',
            'user': 'system',
            'action': 'فشل في النسخ الاحتياطي',
            'details': 'فشل في إنشاء النسخة الاحتياطية التلقائية'
        }
    ]
    
    return render_template('admin/logs.html', logs=system_logs)

# وظائف مساعدة
def get_database_size():
    """حساب حجم قاعدة البيانات"""
    try:
        db_path = 'instance/erp_system.db'
        if os.path.exists(db_path):
            size_bytes = os.path.getsize(db_path)
            size_mb = round(size_bytes / (1024 * 1024), 2)
            return f"{size_mb} MB"
        return "غير متاح"
    except:
        return "غير متاح"

def get_last_backup_date():
    """تاريخ آخر نسخة احتياطية"""
    return "2024-12-19 03:00:00"

def get_system_uptime():
    """وقت تشغيل النظام"""
    return "2 أيام، 14 ساعة، 32 دقيقة"

def get_active_sessions():
    """عدد الجلسات النشطة"""
    return 12

def get_recent_activities():
    """أحدث الأنشطة في النظام"""
    return [
        {
            'user': 'أحمد محمد',
            'action': 'تسجيل دخول',
            'module': 'النظام',
            'timestamp': datetime.now() - timedelta(minutes=5)
        },
        {
            'user': 'فاطمة علي',
            'action': 'إضافة منتج جديد',
            'module': 'المخزون',
            'timestamp': datetime.now() - timedelta(minutes=12)
        },
        {
            'user': 'محمد خالد',
            'action': 'إنشاء فاتورة',
            'module': 'المبيعات',
            'timestamp': datetime.now() - timedelta(minutes=18)
        }
    ]

# API endpoints للوحة الإدارة
@admin_bp.route('/api/module/<module_name>/toggle', methods=['POST'])
@login_required
def toggle_module(module_name):
    """تفعيل/إلغاء تفعيل وحدة"""
    if not current_user.role == 'admin':
        return jsonify({'error': 'غير مصرح'}), 403
    
    # محاكاة تفعيل/إلغاء تفعيل الوحدة
    return jsonify({'success': True, 'message': f'تم تحديث حالة وحدة {module_name}'})

@admin_bp.route('/api/backup/create', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    if not current_user.role == 'admin':
        return jsonify({'error': 'غير مصرح'}), 403
    
    # محاكاة إنشاء نسخة احتياطية
    return jsonify({'success': True, 'message': 'تم إنشاء النسخة الاحتياطية بنجاح'})

@admin_bp.route('/api/system/restart', methods=['POST'])
@login_required
def restart_system():
    """إعادة تشغيل النظام"""
    if not current_user.role == 'admin':
        return jsonify({'error': 'غير مصرح'}), 403
    
    # محاكاة إعادة تشغيل النظام
    return jsonify({'success': True, 'message': 'سيتم إعادة تشغيل النظام خلال 30 ثانية'})
