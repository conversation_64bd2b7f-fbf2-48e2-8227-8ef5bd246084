{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-tachometer-alt me-2 text-primary"></i>
            لوحة التحكم
        </h1>
        <p class="text-muted">مرحباً {{ current_user.full_name }}، إليك نظرة عامة على النظام</p>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-2x text-primary"></i>
                </div>
                <h3 class="text-primary">{{ stats.total_employees }}</h3>
                <p class="text-muted mb-0">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-handshake fa-2x text-success"></i>
                </div>
                <h3 class="text-success">{{ stats.total_customers }}</h3>
                <p class="text-muted mb-0">العملاء</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-truck fa-2x text-info"></i>
                </div>
                <h3 class="text-info">{{ stats.total_suppliers }}</h3>
                <p class="text-muted mb-0">الموردين</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-boxes fa-2x text-warning"></i>
                </div>
                <h3 class="text-warning">{{ stats.total_products }}</h3>
                <p class="text-muted mb-0">المنتجات</p>
            </div>
        </div>
    </div>
</div>

<!-- الوحدات الرئيسية -->
{% set dashboard_data = current_user.get_dashboard_data() %}

<div class="row mb-4">
    <div class="col-12">
        <h3 class="mb-3">الوحدات المتاحة</h3>
    </div>
</div>

<div class="row">
    {% if 'accounting' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-calculator me-2"></i>
                المحاسبة العامة
            </div>
            <div class="card-body">
                <p class="card-text">إدارة الحسابات، قيود اليومية، والتقارير المالية</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>دفتر اليومية</li>
                    <li><i class="fas fa-check text-success me-2"></i>الحسابات العامة</li>
                    <li><i class="fas fa-check text-success me-2"></i>التقارير المالية</li>
                </ul>
                <a href="{{ url_for('accounting.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if 'hr' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-users me-2"></i>
                الموارد البشرية
            </div>
            <div class="card-body">
                <p class="card-text">إدارة الموظفين، الحضور، الرواتب، والإجازات</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>بيانات الموظفين</li>
                    <li><i class="fas fa-check text-success me-2"></i>الحضور والانصراف</li>
                    <li><i class="fas fa-check text-success me-2"></i>إدارة الرواتب</li>
                </ul>
                <a href="{{ url_for('hr.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if 'inventory' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-boxes me-2"></i>
                إدارة المخزون
            </div>
            <div class="card-body">
                <p class="card-text">تسجيل المنتجات، المستودعات، وحركات المخزون</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>المنتجات والخدمات</li>
                    <li><i class="fas fa-check text-success me-2"></i>المستودعات</li>
                    <li><i class="fas fa-check text-success me-2"></i>حركات المخزون</li>
                </ul>
                <a href="{{ url_for('inventory.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if 'sales' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-shopping-cart me-2"></i>
                المبيعات
            </div>
            <div class="card-body">
                <p class="card-text">إدارة العملاء، أوامر البيع، والفواتير</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>قاعدة بيانات العملاء</li>
                    <li><i class="fas fa-check text-success me-2"></i>أوامر البيع</li>
                    <li><i class="fas fa-check text-success me-2"></i>الفواتير</li>
                </ul>
                <a href="{{ url_for('sales.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if 'procurement' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-truck me-2"></i>
                المشتريات
            </div>
            <div class="card-body">
                <p class="card-text">إدارة الموردين، أوامر الشراء، والاستلام</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>قاعدة بيانات الموردين</li>
                    <li><i class="fas fa-check text-success me-2"></i>أوامر الشراء</li>
                    <li><i class="fas fa-check text-success me-2"></i>استلام المواد</li>
                </ul>
                <a href="{{ url_for('procurement.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if 'projects' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-project-diagram me-2"></i>
                إدارة المشاريع
            </div>
            <div class="card-body">
                <p class="card-text">تخطيط وتنفيذ ومتابعة المشاريع والمهام</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>إنشاء المشاريع</li>
                    <li><i class="fas fa-check text-success me-2"></i>تخصيص المهام</li>
                    <li><i class="fas fa-check text-success me-2"></i>متابعة التقدم</li>
                </ul>
                <a href="{{ url_for('projects.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if 'crm' in dashboard_data.modules %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-handshake me-2"></i>
                إدارة علاقات العملاء
            </div>
            <div class="card-body">
                <p class="card-text">متابعة العملاء المحتملين والأنشطة التسويقية</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>العملاء المحتملين</li>
                    <li><i class="fas fa-check text-success me-2"></i>جهات الاتصال</li>
                    <li><i class="fas fa-check text-success me-2"></i>الأنشطة التسويقية</li>
                </ul>
                <a href="{{ url_for('crm.index') }}" class="btn btn-primary">دخول الوحدة</a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- إشعارات سريعة -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bell me-2"></i>
                الإشعارات
            </div>
            <div class="card-body">
                {% if stats.pending_orders > 0 %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    يوجد {{ stats.pending_orders }} أوامر بيع في انتظار المعالجة
                </div>
                {% endif %}
                
                {% if stats.active_projects > 0 %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يوجد {{ stats.active_projects }} مشاريع نشطة
                </div>
                {% endif %}
                
                {% if stats.pending_orders == 0 and stats.active_projects == 0 %}
                <p class="text-muted mb-0">لا توجد إشعارات جديدة</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clock me-2"></i>
                النشاط الأخير
            </div>
            <div class="card-body">
                <p class="text-muted">آخر تسجيل دخول: {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'لم يتم التسجيل من قبل' }}</p>
                <p class="text-muted mb-0">الدور: {{ current_user.role }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
