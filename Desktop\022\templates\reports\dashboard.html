{% extends "base.html" %}

{% block title %}التقارير المتقدمة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-qatar mb-1">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير المتقدمة
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">التقارير المتقدمة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="location.href='{{ url_for('reports.custom') }}'">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء تقرير مخصص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h3>{{ stats.total_reports }}</h3>
                    <p class="mb-0">إجمالي التقارير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3>{{ stats.scheduled_reports }}</h3>
                    <p class="mb-0">تقارير مجدولة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-robot fa-2x mb-2"></i>
                    <h3>{{ stats.automated_reports }}</h3>
                    <p class="mb-0">تقارير تلقائية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-cog fa-2x mb-2"></i>
                    <h3>{{ stats.custom_reports }}</h3>
                    <p class="mb-0">تقارير مخصصة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فئات التقارير -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card report-category-card h-100" onclick="location.href='{{ url_for('reports.financial') }}'">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-4x text-primary mb-3"></i>
                    <h4>التقارير المالية</h4>
                    <p class="text-muted">الميزانية العمومية، قائمة الدخل، التدفقات النقدية</p>
                    <div class="mt-3">
                        <span class="badge bg-primary me-2">12 تقرير</span>
                        <span class="badge bg-success">متاح</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card report-category-card h-100" onclick="location.href='{{ url_for('reports.hr') }}'">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-4x text-success mb-3"></i>
                    <h4>تقارير الموارد البشرية</h4>
                    <p class="text-muted">الموظفين، الحضور، الرواتب، الإجازات</p>
                    <div class="mt-3">
                        <span class="badge bg-primary me-2">8 تقارير</span>
                        <span class="badge bg-success">متاح</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card report-category-card h-100" onclick="location.href='{{ url_for('reports.operational') }}'">
                <div class="card-body text-center">
                    <i class="fas fa-cogs fa-4x text-info mb-3"></i>
                    <h4>التقارير التشغيلية</h4>
                    <p class="text-muted">المبيعات، المخزون، المشاريع، العملاء</p>
                    <div class="mt-3">
                        <span class="badge bg-primary me-2">15 تقرير</span>
                        <span class="badge bg-success">متاح</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التقارير الأكثر استخداماً -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        التقارير الأكثر استخداماً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم التقرير</th>
                                    <th>الوحدة</th>
                                    <th>الاستخدام</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in popular_reports %}
                                <tr>
                                    <td>{{ report.name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ report.module }}</span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-qatar" role="progressbar" 
                                                 style="width: {{ report.usage }}%" 
                                                 aria-valuenow="{{ report.usage }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ report.usage }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="generateQuickReport('{{ report.name }}')">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="scheduleReport('{{ report.name }}')">
                                                <i class="fas fa-clock"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="exportReport('{{ report.name }}')">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        تقارير سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="generateReport('employee_summary')">
                            <i class="fas fa-users me-2"></i>
                            ملخص الموظفين
                        </button>
                        <button class="btn btn-outline-success" onclick="generateReport('financial_overview')">
                            <i class="fas fa-chart-pie me-2"></i>
                            نظرة مالية عامة
                        </button>
                        <button class="btn btn-outline-info" onclick="generateReport('sales_analysis')">
                            <i class="fas fa-shopping-cart me-2"></i>
                            تحليل المبيعات
                        </button>
                        <button class="btn btn-outline-warning" onclick="generateReport('inventory_status')">
                            <i class="fas fa-boxes me-2"></i>
                            حالة المخزون
                        </button>
                        <button class="btn btn-outline-danger" onclick="generateReport('project_progress')">
                            <i class="fas fa-project-diagram me-2"></i>
                            تقدم المشاريع
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- منطقة عرض التقرير -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            <span id="reportTitle">منطقة عرض التقرير</span>
                        </h5>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="printReport()" id="printBtn" disabled>
                                <i class="fas fa-print me-2"></i>
                                طباعة
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="exportCurrentReport()" id="exportBtn" disabled>
                                <i class="fas fa-download me-2"></i>
                                تصدير
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="scheduleCurrentReport()" id="scheduleBtn" disabled>
                                <i class="fas fa-clock me-2"></i>
                                جدولة
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="reportContent" class="text-center py-5">
                        <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">اختر تقريراً من الأعلى</h5>
                        <p class="text-muted">سيتم عرض التقرير هنا بعد اختياره</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.report-category-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.report-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B1538;
}

.text-qatar {
    color: #8B1538;
}

.bg-qatar {
    background-color: #8B1538 !important;
}

.progress {
    background-color: #f8f9fa;
}

#reportContent {
    min-height: 400px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #8B1538;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
let currentReport = null;

// وظائف إنتاج التقارير
function generateReport(reportType) {
    currentReport = reportType;
    
    // تحديث عنوان التقرير
    const titles = {
        'employee_summary': 'ملخص الموظفين',
        'financial_overview': 'نظرة مالية عامة',
        'sales_analysis': 'تحليل المبيعات',
        'inventory_status': 'حالة المخزون',
        'project_progress': 'تقدم المشاريع'
    };
    
    document.getElementById('reportTitle').textContent = titles[reportType] || 'تقرير';
    
    // تفعيل الأزرار
    document.getElementById('printBtn').disabled = false;
    document.getElementById('exportBtn').disabled = false;
    document.getElementById('scheduleBtn').disabled = false;
    
    // عرض مؤشر التحميل
    showLoadingReport();
    
    // جلب بيانات التقرير
    fetch(`/reports/api/generate/${reportType}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showErrorReport(data.error);
            } else {
                displayReport(data, reportType);
            }
        })
        .catch(error => {
            showErrorReport('حدث خطأ في تحميل التقرير');
        });
}

function showLoadingReport() {
    document.getElementById('reportContent').innerHTML = `
        <div class="text-center py-5">
            <div class="loading-spinner mb-3"></div>
            <h5 class="text-muted">جاري إنتاج التقرير...</h5>
            <p class="text-muted">يرجى الانتظار</p>
        </div>
    `;
}

function showErrorReport(error) {
    document.getElementById('reportContent').innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-4x text-danger mb-3"></i>
            <h5 class="text-danger">خطأ في إنتاج التقرير</h5>
            <p class="text-muted">${error}</p>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </button>
        </div>
    `;
}

function displayReport(data, reportType) {
    let html = '';
    
    switch(reportType) {
        case 'employee_summary':
            html = generateEmployeeReportHTML(data);
            break;
        case 'financial_overview':
            html = generateFinancialReportHTML(data);
            break;
        case 'sales_analysis':
            html = generateSalesReportHTML(data);
            break;
        case 'inventory_status':
            html = generateInventoryReportHTML(data);
            break;
        case 'project_progress':
            html = generateProjectReportHTML(data);
            break;
        default:
            html = '<p>نوع التقرير غير مدعوم</p>';
    }
    
    document.getElementById('reportContent').innerHTML = html;
}

function generateEmployeeReportHTML(data) {
    return `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_employees}</h3>
                        <p class="mb-0">إجمالي الموظفين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <h3>${data.active_employees}</h3>
                        <p class="mb-0">الموظفين النشطين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white text-center">
                    <div class="card-body">
                        <h3>${Object.keys(data.departments).length}</h3>
                        <p class="mb-0">عدد الأقسام</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <img src="data:image/png;base64,${data.chart}" class="img-fluid" alt="رسم بياني">
            </div>
            <div class="col-md-6">
                <h6>توزيع الموظفين حسب الأقسام:</h6>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>القسم</th>
                            <th>عدد الموظفين</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(data.departments).map(([dept, count]) => `
                            <tr>
                                <td>${dept}</td>
                                <td>${count}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function generateFinancialReportHTML(data) {
    return `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_accounts}</h3>
                        <p class="mb-0">إجمالي الحسابات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <img src="data:image/png;base64,${data.chart}" class="img-fluid" alt="رسم بياني">
            </div>
        </div>
    `;
}

function generateSalesReportHTML(data) {
    return `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_orders}</h3>
                        <p class="mb-0">إجمالي الطلبات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_customers}</h3>
                        <p class="mb-0">إجمالي العملاء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-info text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_revenue.toLocaleString()} ر.ق</h3>
                        <p class="mb-0">إجمالي الإيرادات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <img src="data:image/png;base64,${data.chart}" class="img-fluid" alt="اتجاه المبيعات">
            </div>
        </div>
    `;
}

function generateInventoryReportHTML(data) {
    return `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_products}</h3>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white text-center">
                    <div class="card-body">
                        <h3>${data.low_stock_products}</h3>
                        <p class="mb-0">مخزون منخفض</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white text-center">
                    <div class="card-body">
                        <h3>${data.out_of_stock}</h3>
                        <p class="mb-0">نفد المخزون</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_value.toLocaleString()} ر.ق</h3>
                        <p class="mb-0">قيمة المخزون</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <img src="data:image/png;base64,${data.chart}" class="img-fluid" alt="توزيع المنتجات">
            </div>
        </div>
    `;
}

function generateProjectReportHTML(data) {
    return `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <h3>${data.total_projects}</h3>
                        <p class="mb-0">إجمالي المشاريع</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <h3>${data.active_projects}</h3>
                        <p class="mb-0">المشاريع النشطة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white text-center">
                    <div class="card-body">
                        <h3>${data.completed_projects}</h3>
                        <p class="mb-0">المشاريع المكتملة</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <img src="data:image/png;base64,${data.chart}" class="img-fluid" alt="توزيع المشاريع">
            </div>
        </div>
    `;
}

// وظائف أخرى
function generateQuickReport(reportName) {
    alert(`سيتم إنتاج تقرير: ${reportName}`);
}

function scheduleReport(reportName) {
    alert(`سيتم جدولة تقرير: ${reportName}`);
}

function exportReport(reportName) {
    alert(`سيتم تصدير تقرير: ${reportName}`);
}

function printReport() {
    if (currentReport) {
        window.print();
    } else {
        alert('يرجى اختيار تقرير أولاً');
    }
}

function exportCurrentReport() {
    if (currentReport) {
        alert(`سيتم تصدير التقرير الحالي: ${currentReport}`);
    } else {
        alert('يرجى اختيار تقرير أولاً');
    }
}

function scheduleCurrentReport() {
    if (currentReport) {
        alert(`سيتم جدولة التقرير الحالي: ${currentReport}`);
    } else {
        alert('يرجى اختيار تقرير أولاً');
    }
}
</script>
{% endblock %}
