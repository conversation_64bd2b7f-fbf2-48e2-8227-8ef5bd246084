#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح خطأ number_format
"""

import requests
import time
from datetime import datetime

def test_all_pages():
    """اختبار جميع صفحات النظام للتأكد من عدم وجود أخطاء"""
    
    print("🧪 اختبار إصلاح خطأ number_format...")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # قائمة الصفحات للاختبار
    pages_to_test = [
        "/",
        "/login",
        "/dashboard",
        "/accounting",
        "/hr",
        "/inventory", 
        "/sales",
        "/procurement",
        "/projects",
        "/crm"
    ]
    
    # معلومات تسجيل الدخول
    login_data = {
        'username': 'hr_manager',
        'password': '123456'
    }
    
    session = requests.Session()
    
    print("🔑 تسجيل الدخول...")
    try:
        # تسجيل الدخول
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        print("💡 تأكد من تشغيل النظام باستخدام: python app.py")
        return
    
    print("\n📊 اختبار الصفحات...")
    
    results = []
    
    for page in pages_to_test:
        try:
            print(f"🔍 اختبار: {page}")
            
            response = session.get(f"{base_url}{page}")
            
            if response.status_code == 200:
                # فحص وجود أخطاء في المحتوى
                content = response.text.lower()
                
                if 'templateassertionerror' in content:
                    print(f"❌ خطأ Template في {page}")
                    results.append((page, "Template Error", response.status_code))
                elif 'number_format' in content and 'error' in content:
                    print(f"❌ خطأ number_format في {page}")
                    results.append((page, "Number Format Error", response.status_code))
                elif 'internal server error' in content:
                    print(f"❌ خطأ خادم داخلي في {page}")
                    results.append((page, "Internal Server Error", response.status_code))
                else:
                    print(f"✅ {page} يعمل بنجاح")
                    results.append((page, "Success", response.status_code))
            
            elif response.status_code == 302:
                print(f"🔄 {page} إعادة توجيه (طبيعي)")
                results.append((page, "Redirect", response.status_code))
            
            else:
                print(f"⚠️ {page} رمز الحالة: {response.status_code}")
                results.append((page, f"Status {response.status_code}", response.status_code))
                
            # انتظار قصير بين الطلبات
            time.sleep(0.5)
            
        except Exception as e:
            print(f"❌ خطأ في {page}: {e}")
            results.append((page, f"Exception: {e}", 0))
    
    # تلخيص النتائج
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبار:")
    print("=" * 60)
    
    success_count = 0
    error_count = 0
    
    for page, status, code in results:
        if status == "Success" or status == "Redirect":
            print(f"✅ {page:<15} - {status}")
            success_count += 1
        else:
            print(f"❌ {page:<15} - {status}")
            error_count += 1
    
    print("\n" + "=" * 60)
    print("📊 الإحصائيات النهائية:")
    print(f"✅ صفحات تعمل بنجاح: {success_count}")
    print(f"❌ صفحات بها أخطاء: {error_count}")
    print(f"📄 إجمالي الصفحات: {len(results)}")
    
    if error_count == 0:
        print("\n🎉 جميع الصفحات تعمل بنجاح! تم إصلاح خطأ number_format")
        print("✅ النظام جاهز للاستخدام")
    else:
        print(f"\n⚠️ يوجد {error_count} صفحات بها أخطاء تحتاج إصلاح")
    
    # معلومات إضافية
    print("\n" + "=" * 60)
    print("🌐 معلومات الوصول:")
    print(f"🔗 الرابط الرئيسي: {base_url}")
    print("🔑 المستخدم: hr_manager")
    print("🔑 كلمة المرور: 123456")
    
    print("\n📋 الوحدات المتاحة:")
    modules = [
        ("💰 المحاسبة", "/accounting"),
        ("👥 الموارد البشرية", "/hr"),
        ("📦 المخزون", "/inventory"),
        ("🛒 المبيعات", "/sales"),
        ("🛍️ المشتريات", "/procurement"),
        ("📋 المشاريع", "/projects"),
        ("🤝 إدارة العملاء", "/crm")
    ]
    
    for name, path in modules:
        module_result = next((r for r in results if r[0] == path), None)
        if module_result and (module_result[1] == "Success" or module_result[1] == "Redirect"):
            print(f"✅ {name}")
        else:
            print(f"❌ {name}")
    
    print("\n🎯 تم الانتهاء من الاختبار!")
    
    return error_count == 0

if __name__ == '__main__':
    print("🚀 بدء اختبار إصلاح خطأ number_format")
    print("=" * 60)
    
    success = test_all_pages()
    
    if success:
        print("\n🎉 الاختبار نجح! جميع الوحدات تعمل بدون أخطاء")
    else:
        print("\n⚠️ الاختبار كشف عن بعض الأخطاء")
    
    print("\n💡 تلميح: تأكد من تشغيل النظام باستخدام 'python app.py' قبل الاختبار")
