from flask_login import UserMixin
from datetime import datetime
from database_setup import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)

    # معلومات الدور والصلاحيات
    role = db.Column(db.String(20), nullable=False, default='employee')  # admin, manager, accountant, hr, employee
    department = db.Column(db.String(50))
    position = db.Column(db.String(50))

    # حالة الحساب
    is_active = db.Column(db.Bo<PERSON>, default=True)
    is_verified = db.Column(db.Boolean, default=False)

    # تواريخ مهمة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    last_activity = db.Column(db.DateTime)

    # معلومات إضافية
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    profile_picture = db.Column(db.String(255))

    # العلاقات
    employee = db.relationship('Employee', backref='user_account', uselist=False)
    activities = db.relationship('UserActivity', backref='user', lazy='dynamic')

    def __repr__(self):
        return f'<User {self.username}>'

    def has_permission(self, permission):
        """فحص الصلاحيات حسب الدور"""
        permissions = {
            'admin': ['all'],
            'manager': ['view_all', 'edit_department', 'approve_requests'],
            'accountant': ['view_accounting', 'edit_accounting', 'view_reports'],
            'hr': ['view_hr', 'edit_hr', 'view_employees'],
            'employee': ['view_own', 'edit_own', 'submit_requests']
        }

        user_permissions = permissions.get(self.role, [])
        return permission in user_permissions or 'all' in user_permissions

    def get_dashboard_data(self):
        """الحصول على بيانات لوحة التحكم حسب الدور"""
        # تفعيل جميع الوحدات لجميع المستخدمين
        all_modules = ['accounting', 'hr', 'inventory', 'sales', 'procurement', 'projects', 'crm']

        if self.role == 'admin':
            return {
                'modules': all_modules,
                'can_view_all': True
            }
        elif self.role == 'manager':
            return {
                'modules': all_modules,
                'can_view_department': True
            }
        elif self.role == 'accountant':
            return {
                'modules': all_modules,
                'can_view_financial': True
            }
        elif self.role == 'hr':
            return {
                'modules': all_modules,
                'can_view_hr': True
            }
        elif self.role == 'hr_manager':
            return {
                'modules': all_modules,
                'can_view_hr': True
            }
        else:
            return {
                'modules': all_modules,
                'can_view_own': True
            }

class UserActivity(db.Model):
    __tablename__ = 'user_activities'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<UserActivity {self.action} by {self.user_id}>'
