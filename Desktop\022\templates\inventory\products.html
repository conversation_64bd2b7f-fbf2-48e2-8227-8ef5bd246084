{% extends "base.html" %}

{% block title %}قائمة المنتجات - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-cube me-2 text-primary"></i>
            قائمة المنتجات
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('inventory.index') }}">المخزون</a></li>
                <li class="breadcrumb-item active">المنتجات</li>
            </ol>
        </nav>
    </div>
</div>

<!-- أدوات البحث والتصفية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="البحث في المنتجات..." id="searchInput">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="categoryFilter">
                            <option value="">جميع الفئات</option>
                            <option value="electronics">إلكترونيات</option>
                            <option value="furniture">أثاث</option>
                            <option value="supplies">مستلزمات</option>
                            <option value="equipment">معدات</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="low_stock">مخزون منخفض</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="sortBy">
                            <option value="name">ترتيب بالاسم</option>
                            <option value="code">ترتيب بالكود</option>
                            <option value="stock">ترتيب بالمخزون</option>
                            <option value="price">ترتيب بالسعر</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة منتج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المنتجات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                قائمة المنتجات ({{ products|length if products else 0 }} منتج)
            </div>
            <div class="card-body">
                {% if products %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>كود المنتج</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>الوحدة</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>سعر التكلفة</th>
                                <th>سعر البيع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ product.code }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-secondary text-white rounded d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-cube"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ product.name_ar }}</div>
                                            {% if product.name_en %}
                                            <small class="text-muted">{{ product.name_en }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ product.category or 'غير محدد' }}</span>
                                </td>
                                <td>{{ product.unit_of_measure }}</td>
                                <td>
                                    <span class="fw-bold {% if product.is_low_stock() %}text-danger{% else %}text-success{% endif %}">
                                        {{ product.current_stock }}
                                    </span>
                                </td>
                                <td>{{ product.minimum_stock }}</td>
                                <td>
                                    {% if product.cost_price %}
                                    {{ "%.2f"|format(product.cost_price) }} ر.ق
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.selling_price %}
                                    {{ "%.2f"|format(product.selling_price) }} ر.ق
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.is_active %}
                                        {% if product.is_low_stock() %}
                                        <span class="badge bg-warning">مخزون منخفض</span>
                                        {% else %}
                                        <span class="badge bg-success">نشط</span>
                                        {% endif %}
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" title="حركات المخزون">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" title="إدخال مخزون">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-cube"></i>
                    <h5 class="text-muted">لا يوجد منتجات مسجلة</h5>
                    <p class="text-muted">ابدأ بإضافة المنتجات إلى النظام</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if products %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ products|selectattr("is_active", "equalto", true)|list|length }}</h5>
                <small class="text-muted">منتج نشط</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ products|selectattr("category", "equalto", "electronics")|list|length }}</h5>
                <small class="text-muted">إلكترونيات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ products|selectattr("category", "equalto", "furniture")|list|length }}</h5>
                <small class="text-muted">أثاث</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ products|selectattr("category", "equalto", "supplies")|list|length }}</h5>
                <small class="text-muted">مستلزمات</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// البحث في الجدول
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// تصفية حسب الفئة
document.getElementById('categoryFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        if (!filterValue) {
            row.style.display = '';
        } else {
            const categoryCell = row.cells[2].textContent.toLowerCase();
            row.style.display = categoryCell.includes(filterValue) ? '' : 'none';
        }
    });
});

// تصفية حسب الحالة
document.getElementById('statusFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        if (!filterValue) {
            row.style.display = '';
        } else {
            const statusCell = row.cells[8].textContent.toLowerCase();
            let showRow = false;
            
            if (filterValue === 'active' && statusCell.includes('نشط')) {
                showRow = true;
            } else if (filterValue === 'inactive' && statusCell.includes('غير نشط')) {
                showRow = true;
            } else if (filterValue === 'low_stock' && statusCell.includes('منخفض')) {
                showRow = true;
            }
            
            row.style.display = showRow ? '' : 'none';
        }
    });
});
</script>
{% endblock %}
