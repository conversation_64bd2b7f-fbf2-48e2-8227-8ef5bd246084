"""
اختبار النظام المتقدم للإشعارات وسير العمل
"""

import os
import sys

def test_system_files():
    """اختبار وجود ملفات النظام"""
    
    print("🔍 فحص ملفات النظام...")
    print("=" * 50)
    
    # الملفات المطلوبة
    required_files = [
        'app.py',
        'models/notifications.py',
        'models/settings.py',
        'services/notification_service.py',
        'services/workflow_service.py',
        'routes/api_simple.py',
        'routes/settings_simple.py',
        'routes/admin_simple.py',
        'templates/base.html',
        'templates/settings/index.html',
        'templates/admin/advanced_dashboard.html',
        'init_notifications_workflow.py'
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    print(f"\n📊 النتائج:")
    print(f"✅ الملفات الموجودة: {len(existing_files)}")
    print(f"❌ الملفات المفقودة: {len(missing_files)}")
    
    return len(missing_files) == 0

def test_templates():
    """اختبار قوالب HTML"""
    
    print("\n🎨 فحص قوالب HTML...")
    print("=" * 30)
    
    # فحص القالب الأساسي
    if os.path.exists('templates/base.html'):
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
            # فحص الميزات الجديدة
            features = [
                ('الإشعارات', 'notificationsDropdown' in content),
                ('سير العمل', 'workflowDropdown' in content),
                ('الإعدادات السريعة', 'fas fa-cog' in content),
                ('JavaScript المتقدم', 'loadNotifications' in content),
                ('تحديث تلقائي', 'setInterval' in content)
            ]
            
            for feature_name, exists in features:
                status = "✅" if exists else "❌"
                print(f"{status} {feature_name}")
    
    # فحص قوالب الإعدادات
    settings_templates = [
        'templates/settings/index.html',
        'templates/admin/advanced_dashboard.html'
    ]
    
    for template in settings_templates:
        if os.path.exists(template):
            print(f"✅ {template}")
        else:
            print(f"❌ {template}")

def test_models():
    """اختبار النماذج"""
    
    print("\n🗄️ فحص النماذج...")
    print("=" * 20)
    
    try:
        # فحص نموذج الإشعارات
        if os.path.exists('models/notifications.py'):
            with open('models/notifications.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
                models = [
                    ('Notification', 'class Notification' in content),
                    ('NotificationTemplate', 'class NotificationTemplate' in content),
                    ('WorkflowDefinition', 'class WorkflowDefinition' in content),
                    ('WorkflowStep', 'class WorkflowStep' in content),
                    ('WorkflowInstance', 'class WorkflowInstance' in content),
                    ('WorkflowExecution', 'class WorkflowExecution' in content)
                ]
                
                for model_name, exists in models:
                    status = "✅" if exists else "❌"
                    print(f"{status} {model_name}")
        
        # فحص نموذج الإعدادات
        if os.path.exists('models/settings.py'):
            with open('models/settings.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
                settings_models = [
                    ('SystemSetting', 'class SystemSetting' in content),
                    ('UserPreference', 'class UserPreference' in content),
                    ('ModuleConfiguration', 'class ModuleConfiguration' in content),
                    ('AuditLog', 'class AuditLog' in content)
                ]
                
                for model_name, exists in settings_models:
                    status = "✅" if exists else "❌"
                    print(f"{status} {model_name}")
    
    except Exception as e:
        print(f"❌ خطأ في فحص النماذج: {str(e)}")

def test_services():
    """اختبار الخدمات"""
    
    print("\n⚙️ فحص الخدمات...")
    print("=" * 20)
    
    services = [
        ('notification_service.py', 'NotificationService'),
        ('workflow_service.py', 'WorkflowService')
    ]
    
    for service_file, service_class in services:
        service_path = f'services/{service_file}'
        if os.path.exists(service_path):
            with open(service_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if f'class {service_class}' in content:
                    print(f"✅ {service_class}")
                else:
                    print(f"❌ {service_class} (الملف موجود لكن الكلاس مفقود)")
        else:
            print(f"❌ {service_file}")

def test_apis():
    """اختبار APIs"""
    
    print("\n🔌 فحص APIs...")
    print("=" * 15)
    
    api_files = [
        ('api_simple.py', ['/api/notifications', '/api/workflow/tasks', '/api/system/status']),
        ('settings_simple.py', ['/settings', '/settings/api/test-notification']),
        ('admin_simple.py', ['/admin', '/admin/api/send-announcement'])
    ]
    
    for api_file, endpoints in api_files:
        api_path = f'routes/{api_file}'
        if os.path.exists(api_path):
            with open(api_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                print(f"\n📁 {api_file}:")
                for endpoint in endpoints:
                    if endpoint in content:
                        print(f"  ✅ {endpoint}")
                    else:
                        print(f"  ❌ {endpoint}")
        else:
            print(f"❌ {api_file}")

def show_features_summary():
    """عرض ملخص الميزات"""
    
    print("\n" + "=" * 60)
    print("🎉 ملخص الميزات المتقدمة المضافة")
    print("=" * 60)
    
    features = [
        "🔔 نظام الإشعارات المتقدم:",
        "   • إشعارات فورية في الشريط العلوي",
        "   • عدادات الإشعارات غير المقروءة",
        "   • قوالب إشعارات قابلة للتخصيص",
        "   • تصنيف الإشعارات حسب النوع والأولوية",
        "   • تنظيف تلقائي للإشعارات القديمة",
        "",
        "⚙️ نظام سير العمل والموافقات:",
        "   • سير عمل قابل للتخصيص",
        "   • مهام معلقة مع التنبيهات",
        "   • تتبع حالة العمليات",
        "   • موافقات متعددة المستويات",
        "   • إشعارات تلقائية للمهام",
        "",
        "🛠️ لوحة الإدارة المتقدمة:",
        "   • إحصائيات شاملة ومرئية",
        "   • إدارة المستخدمين والصلاحيات",
        "   • مراقبة حالة النظام",
        "   • إدارة الوحدات والإعدادات",
        "   • إرسال إعلانات عامة",
        "",
        "⚙️ إعدادات النظام الشاملة:",
        "   • إعدادات قابلة للتخصيص",
        "   • تفضيلات المستخدم الشخصية",
        "   • إعدادات الأمان والتدقيق",
        "   • إدارة النسخ الاحتياطي",
        "",
        "🔒 الأمان وسجل التدقيق:",
        "   • تسجيل جميع العمليات",
        "   • مراقبة النشاطات المشبوهة",
        "   • إعدادات أمان متقدمة",
        "   • تتبع تغييرات النظام",
        "",
        "🎨 واجهة المستخدم المحسنة:",
        "   • تصميم عربي RTL متجاوب",
        "   • ألوان علم قطر والهوية البصرية",
        "   • تأثيرات بصرية متقدمة",
        "   • تحديث تلقائي للبيانات",
        "   • مودالات تفاعلية",
        "",
        "📱 ميزات تقنية متقدمة:",
        "   • APIs RESTful شاملة",
        "   • تحديث البيانات في الوقت الفعلي",
        "   • نظام تخزين مؤقت ذكي",
        "   • معالجة الأخطاء المتقدمة",
        "   • تحسين الأداء والسرعة"
    ]
    
    for feature in features:
        print(feature)
    
    print("\n" + "=" * 60)
    print("🌐 معلومات الوصول:")
    print("🔗 الرابط: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    
    print("\n📋 طريقة التشغيل:")
    print("1. python run_simple.py")
    print("2. افتح المتصفح على http://localhost:5000")
    print("3. سجل الدخول باستخدام admin/admin123")
    print("4. استكشف الميزات الجديدة!")

def main():
    """تشغيل اختبار النظام"""
    
    print("🚀 اختبار نظام الإشعارات وسير العمل المتقدم")
    print("=" * 60)
    
    # تغيير المجلد الحالي
    if not os.path.exists('app.py'):
        print("❌ لم يتم العثور على ملفات النظام في المجلد الحالي")
        return
    
    # تشغيل الاختبارات
    files_ok = test_system_files()
    test_templates()
    test_models()
    test_services()
    test_apis()
    
    # عرض النتائج
    print("\n" + "=" * 60)
    if files_ok:
        print("✅ جميع ملفات النظام موجودة ومكتملة!")
        print("🎯 النظام جاهز للتشغيل مع جميع الميزات المتقدمة")
    else:
        print("⚠️ بعض الملفات مفقودة، لكن الميزات الأساسية متاحة")
    
    show_features_summary()

if __name__ == '__main__':
    main()
