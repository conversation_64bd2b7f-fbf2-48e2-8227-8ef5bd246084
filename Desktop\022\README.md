# نظام إدارة الموارد - دولة قطر
## Qatar ERP System

نظام إدارة الموارد الشامل مصمم خصيصاً لدولة قطر بواجهة عربية كاملة وألوان علم قطر.

## المزايا الرئيسية

### 🏢 الوحدات المتاحة
- **المحاسبة العامة**: دفتر اليومية، الحسابات العامة، التقارير المالية
- **الموارد البشرية**: إدارة الموظفين، الحضور والانصراف، الرواتب، الإجازات
- **إدارة المخزون**: المنتجات، المستودعات، حركات المخزون
- **المبيعات**: العملاء، أوامر البيع، الفواتير
- **المشتريات**: الموردين، أوامر الشراء، الاستلام
- **إدارة المشاريع**: المشاريع، المهام، أعضاء الفريق
- **إدارة علاقات العملاء (CRM)**: العملاء المحتملين، جهات الاتصال، الأنشطة
- **بوابة الخدمة الذاتية**: للموظفين لمتابعة بياناتهم وطلباتهم

### 🎨 التصميم
- واجهة عربية كاملة مع دعم RTL
- ألوان علم قطر (العنابي والأبيض)
- تصميم متجاوب يعمل على جميع الأجهزة
- خط Cairo العربي الجميل

### 🔐 الأمان والصلاحيات
- نظام تسجيل دخول آمن
- صلاحيات متدرجة حسب الدور (مدير، محاسب، موارد بشرية، موظف)
- تتبع أنشطة المستخدمين
- حماية البيانات الحساسة

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- Flask 2.3.3
- SQLAlchemy 2.0.35
- Bootstrap 5.3.0

### المكتبات المطلوبة
```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1
Werkzeug==2.3.7
SQLAlchemy==2.0.35
Jinja2==3.1.2
reportlab==4.0.4
openpyxl==3.1.2
python-dateutil==2.8.2
bcrypt==4.0.1
email-validator==2.0.0
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd qatar-erp-system
```

### 2. تثبيت المكتبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل النظام
```bash
python app.py
```

### 4. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## بيانات تسجيل الدخول الافتراضية

### المدير العام
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### مدير الموارد البشرية
- **اسم المستخدم**: hr_manager
- **كلمة المرور**: 123456

### المحاسب
- **اسم المستخدم**: accountant
- **كلمة المرور**: 123456

### مدير المخزون
- **اسم المستخدم**: inventory_manager
- **كلمة المرور**: 123456

## هيكل المشروع

```
qatar-erp-system/
├── app.py                 # التطبيق الرئيسي
├── config.py             # إعدادات النظام
├── database_setup.py     # إعداد قاعدة البيانات
├── seed_data.py          # البيانات التجريبية
├── requirements.txt      # المكتبات المطلوبة
├── models/              # نماذج قاعدة البيانات
│   ├── user.py          # نموذج المستخدمين
│   ├── accounting.py    # نماذج المحاسبة
│   ├── hr.py           # نماذج الموارد البشرية
│   ├── inventory.py    # نماذج المخزون
│   ├── sales.py        # نماذج المبيعات
│   ├── procurement.py  # نماذج المشتريات
│   ├── projects.py     # نماذج المشاريع
│   └── crm.py          # نماذج CRM
├── routes/             # مسارات النظام
│   ├── auth.py         # مسارات المصادقة
│   ├── accounting.py   # مسارات المحاسبة
│   ├── hr.py          # مسارات الموارد البشرية
│   ├── inventory.py   # مسارات المخزون
│   ├── sales.py       # مسارات المبيعات
│   ├── procurement.py # مسارات المشتريات
│   ├── projects.py    # مسارات المشاريع
│   └── crm.py         # مسارات CRM
├── templates/          # قوالب HTML
│   ├── base.html      # القالب الأساسي
│   ├── auth/          # قوالب المصادقة
│   ├── dashboard/     # قوالب لوحة التحكم
│   ├── accounting/    # قوالب المحاسبة
│   ├── hr/           # قوالب الموارد البشرية
│   ├── inventory/    # قوالب المخزون
│   ├── sales/        # قوالب المبيعات
│   ├── procurement/  # قوالب المشتريات
│   ├── projects/     # قوالب المشاريع
│   └── crm/          # قوالب CRM
├── static/            # الملفات الثابتة
│   ├── css/          # ملفات CSS
│   ├── js/           # ملفات JavaScript
│   └── uploads/      # الملفات المرفوعة
└── database/         # ملفات قاعدة البيانات
```

## الوظائف المتقدمة

### 📊 التقارير
- تقارير مالية شاملة
- تقارير الموارد البشرية
- تقارير المخزون والمبيعات
- تصدير إلى Excel و PDF

### 📱 الاستجابة
- يعمل على الهواتف الذكية والأجهزة اللوحية
- تصميم متجاوب بالكامل
- واجهة سهلة الاستخدام

### 🔄 التكامل
- إمكانية التكامل مع أنظمة خارجية
- API للتطبيقات الأخرى
- تصدير واستيراد البيانات

## الدعم والصيانة

### 🛠️ الصيانة
- نسخ احتياطية تلقائية
- تحديثات أمنية منتظمة
- مراقبة الأداء

### 📞 الدعم الفني
- دعم فني متخصص
- تدريب المستخدمين
- توثيق شامل

## الترخيص

هذا النظام مطور خصيصاً لدولة قطر ومؤسساتها الحكومية.

## المطور

تم تطوير هذا النظام بواسطة فريق متخصص في أنظمة إدارة الموارد للمؤسسات الحكومية.

---

**ملاحظة**: هذا النظام في مرحلة التطوير والاختبار. يرجى التواصل مع فريق التطوير لأي استفسارات أو تحسينات مطلوبة.
