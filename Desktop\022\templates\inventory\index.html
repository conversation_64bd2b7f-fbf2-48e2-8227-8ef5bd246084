{% extends "base.html" %}

{% block title %}إدارة المخزون - نظام إدارة الموارد{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-boxes me-2 text-primary"></i>
            إدارة المخزون
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">المخزون</li>
            </ol>
        </nav>
    </div>
</div>

<!-- إحصائيات المخزون -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-cube fa-2x text-primary"></i>
                </div>
                <h3 class="text-primary">{{ stats.total_products }}</h3>
                <p class="text-muted mb-0">إجمالي المنتجات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                </div>
                <h3 class="text-warning">{{ stats.low_stock_products }}</h3>
                <p class="text-muted mb-0">منتجات منخفضة المخزون</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-warehouse fa-2x text-info"></i>
                </div>
                <h3 class="text-info">{{ stats.total_warehouses }}</h3>
                <p class="text-muted mb-0">المستودعات</p>
            </div>
        </div>
    </div>
</div>

<!-- الوظائف الرئيسية -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-cube me-2"></i>
                إدارة المنتجات
            </div>
            <div class="card-body">
                <p class="card-text">إدارة كتالوج المنتجات والخدمات</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>تسجيل المنتجات</li>
                    <li><i class="fas fa-check text-success me-2"></i>تصنيف المنتجات</li>
                    <li><i class="fas fa-check text-success me-2"></i>إدارة الأسعار</li>
                </ul>
                <a href="{{ url_for('inventory.products') }}" class="btn btn-primary">
                    <i class="fas fa-eye me-2"></i>
                    عرض المنتجات
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-warehouse me-2"></i>
                إدارة المستودعات
            </div>
            <div class="card-body">
                <p class="card-text">إدارة المستودعات ومواقع التخزين</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>المستودعات الرئيسية</li>
                    <li><i class="fas fa-check text-success me-2"></i>المستودعات الفرعية</li>
                    <li><i class="fas fa-check text-success me-2"></i>إدارة المواقع</li>
                </ul>
                <a href="{{ url_for('inventory.warehouses') }}" class="btn btn-primary">
                    <i class="fas fa-eye me-2"></i>
                    عرض المستودعات
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-exchange-alt me-2"></i>
                حركات المخزون
            </div>
            <div class="card-body">
                <p class="card-text">متابعة حركات الإدخال والإخراج</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>حركات الإدخال</li>
                    <li><i class="fas fa-check text-success me-2"></i>حركات الإخراج</li>
                    <li><i class="fas fa-check text-success me-2"></i>التحويلات بين المستودعات</li>
                </ul>
                <a href="{{ url_for('inventory.stock_movements') }}" class="btn btn-primary">
                    <i class="fas fa-eye me-2"></i>
                    عرض الحركات
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>
                تقارير المخزون
            </div>
            <div class="card-body">
                <p class="card-text">تقارير شاملة عن حالة المخزون</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>تقرير الأرصدة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تقرير الحركات</li>
                    <li><i class="fas fa-check text-success me-2"></i>تقرير المنتجات المنخفضة</li>
                </ul>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-chart-bar me-2"></i>
                    عرض التقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات المخزون -->
{% if stats.low_stock_products > 0 %}
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                <div>
                    <h5 class="alert-heading mb-1">تنبيه: منتجات منخفضة المخزون</h5>
                    <p class="mb-0">يوجد {{ stats.low_stock_products }} منتج وصل إلى الحد الأدنى للمخزون. يرجى مراجعة المخزون وإعادة الطلب.</p>
                </div>
                <div class="ms-auto">
                    <a href="{{ url_for('inventory.products') }}?filter=low_stock" class="btn btn-warning">
                        عرض المنتجات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- الإجراءات السريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة منتج جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-arrow-down me-2"></i>
                            إدخال مخزون
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="fas fa-arrow-up me-2"></i>
                            إخراج مخزون
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-clipboard-list me-2"></i>
                            جرد المخزون
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث الحركات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history me-2"></i>
                أحدث حركات المخزون
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المنتج</th>
                                <th>نوع الحركة</th>
                                <th>الكمية</th>
                                <th>المستودع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15</td>
                                <td>أجهزة كمبيوتر محمولة</td>
                                <td><span class="badge bg-success">إدخال</span></td>
                                <td>+10</td>
                                <td>المستودع الرئيسي</td>
                            </tr>
                            <tr>
                                <td>2024-01-14</td>
                                <td>طابعات ليزر</td>
                                <td><span class="badge bg-danger">إخراج</span></td>
                                <td>-5</td>
                                <td>مستودع الفرع الأول</td>
                            </tr>
                            <tr>
                                <td>2024-01-13</td>
                                <td>أوراق A4</td>
                                <td><span class="badge bg-info">تحويل</span></td>
                                <td>100</td>
                                <td>من الرئيسي إلى الفرع</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="{{ url_for('inventory.stock_movements') }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الحركات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
